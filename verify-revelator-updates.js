#!/usr/bin/env node

/**
 * Verification script to check if all Revelator method calls have been updated
 * to remove email and enterpriseName parameters
 */

const fs = require('fs');
const path = require('path');

// Function to recursively find all TypeScript files
function findTsFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory() && !file.includes('node_modules') && !file.includes('.git')) {
      findTsFiles(filePath, fileList);
    } else if (file.endsWith('.ts') && !file.endsWith('.d.ts')) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// Function to check for old Revelator method signatures
function checkFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  const issues = [];
  
  lines.forEach((line, index) => {
    const lineNumber = index + 1;
    
    // Check for revelator method calls with email/enterpriseName
    if (line.includes('revelator.') && (line.includes('email') || line.includes('enterpriseName'))) {
      // Skip comments and type definitions
      if (!line.trim().startsWith('//') && !line.trim().startsWith('*') && !line.includes('interface') && !line.includes('type ')) {
        issues.push({
          line: lineNumber,
          content: line.trim(),
          type: 'method_call'
        });
      }
    }
    
    // Check for method definitions with email/enterpriseName parameters
    if (line.includes('email: string') || line.includes('enterpriseName: string')) {
      if (line.includes('partnerUserId') || line.includes('revelator')) {
        issues.push({
          line: lineNumber,
          content: line.trim(),
          type: 'method_definition'
        });
      }
    }
    
    // Check for memoAuthGetRequest calls with old signature
    if (line.includes('memoAuthGetRequest') && line.includes('email')) {
      issues.push({
        line: lineNumber,
        content: line.trim(),
        type: 'memo_auth_request'
      });
    }
    
    // Check for authRequest calls with old signature
    if (line.includes('authRequest') && (line.includes('email') || line.includes('enterpriseName'))) {
      issues.push({
        line: lineNumber,
        content: line.trim(),
        type: 'auth_request'
      });
    }
  });
  
  return issues;
}

// Main function
function main() {
  console.log('🔍 Verifying Revelator method updates...\n');
  
  const srcDir = path.join(process.cwd(), 'src');
  const tsFiles = findTsFiles(srcDir);
  
  let totalIssues = 0;
  const fileIssues = {};
  
  tsFiles.forEach(filePath => {
    const issues = checkFile(filePath);
    if (issues.length > 0) {
      const relativePath = path.relative(process.cwd(), filePath);
      fileIssues[relativePath] = issues;
      totalIssues += issues.length;
    }
  });
  
  if (totalIssues === 0) {
    console.log('✅ All Revelator methods have been successfully updated!');
    console.log('✅ No email or enterpriseName parameters found in method calls.');
  } else {
    console.log(`❌ Found ${totalIssues} potential issues in ${Object.keys(fileIssues).length} files:\n`);
    
    Object.entries(fileIssues).forEach(([filePath, issues]) => {
      console.log(`📁 ${filePath}:`);
      issues.forEach(issue => {
        console.log(`   Line ${issue.line} (${issue.type}): ${issue.content}`);
      });
      console.log('');
    });
    
    console.log('Please review and update the above method calls to remove email and enterpriseName parameters.');
  }
  
  console.log(`\n📊 Summary:`);
  console.log(`   Files checked: ${tsFiles.length}`);
  console.log(`   Issues found: ${totalIssues}`);
  console.log(`   Files with issues: ${Object.keys(fileIssues).length}`);
}

// Run the verification
main();
