{"name": "makerforge", "version": "1.0.0", "description": "Backend system for the Makerverse platform", "main": "dist/main.js", "types": "dist/main.d.ts", "author": "CreatorOs", "license": "ISC", "private": true, "repository": {"type": "git", "url": "git+https://gitlab.com/creator-os/makerforge.git"}, "bugs": {"url": "https://gitlab.com/creator-os/makerforge/issues"}, "homepage": "https://gitlab.com/creator-os/makerforge#readme", "scripts": {"start": "node dist/main.js", "start:socket": "node dist/socket.js", "dev": "concurrently -k -p \"{time}\" -c \"auto,cyan\" \"tsc -p ./tsconfig.json -w\" \"nodemon ./dist/main.js\"", "dev:socket": "concurrently -k -p \"{time}\" -c \"auto,cyan\" \"tsc -p ./tsconfig.json -w\" \"nodemon ./dist/socket.js\"", "debug": "tsc -p ./tsconfig.json && node --inspect=9229 dist/main.js", "debug:socket": "tsc -p ./tsconfig.json && node --inspect=9239 dist/socket.js", "build": "tsc -p ./tsconfig.json", "test": "jest --config jest.config.json --runInBand --detectOpenHandles --forceExit", "lint": "eslint \"src/**/*.ts\" --fix", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\""}, "dependencies": {"@aws-sdk/client-acm": "^3.645.0", "@aws-sdk/client-cloudfront": "^3.645.0", "@aws-sdk/client-s3": "^3.614.0", "@aws-sdk/client-ses": "^3.614.0", "@aws-sdk/client-sns": "^3.614.0", "@aws-sdk/s3-request-presigner": "^3.750.0", "@joi/date": "^2.1.1", "@types/adm-zip": "^0.5.7", "adm-zip": "^0.5.16", "axios": "1.7.4", "bcryptjs": "^2.4.3", "bullmq": "^5.8.7", "bunyan": "^1.8.15", "cors": "^2.8.5", "csv-parse": "^5.6.0", "date-fns": "^3.6.0", "deasyncify": "^0.0.4", "device-detector-js": "^3.0.3", "dotenv": "^16.4.5", "express": "^4.21.1", "helmet": "^7.1.0", "http-status-codes": "^2.3.0", "inversify": "^6.0.2", "inversify-express-utils": "^6.4.6", "ioredis": "^5.4.1", "joi": "^17.12.2", "joi-phone-number": "^5.1.1", "jsonwebtoken": "^9.0.2", "knex": "^3.1.0", "lodash": "^4.17.21", "module-alias": "^2.2.3", "moment": "^2.30.1", "multer": "^1.4.4", "music-metadata": "^7.13.1", "nanoid": "3.3.4", "pg": "^8.11.3", "postgrator": "5.0.0", "reflect-metadata": "^0.2.1", "response-time": "^2.3.2", "sharp": "^0.33.5", "uWebSockets.js": "uNetworking/uWebSockets.js#v20.51.0", "ulid": "^2.3.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/bunyan": "^1.8.11", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/joi-phone-number": "^5.0.8", "@types/jsonwebtoken": "^9.0.6", "@types/lodash": "^4.17.0", "@types/multer": "^1.4.11", "@types/node": "^20.11.29", "@types/pg": "^8.11.3", "@types/response-time": "^2.3.8", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "chalk": "4.1.2", "concurrently": "7", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^29.7.0", "nodemon": "^3.1.0", "prettier": "^2.3.2", "supertest": "^6.3.4", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.4.2"}, "_moduleAliases": {"@app": "./dist"}}