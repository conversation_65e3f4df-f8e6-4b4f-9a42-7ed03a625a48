import { inject, injectable } from 'inversify';
import { MODULE_TOKENS } from '@app/internal/ioc/tokens';
import { RedisStore } from '../redis/store';

const PRESENCE_TTL_MS = 120_000; // user stays “online” for 2 min
const key = (id: string) => `presence:${id}`;

@injectable()
export class PresenceCache {
  constructor(@inject(MODULE_TOKENS.RedisStore) private store: RedisStore) {}

  /** mark / renew presence */
  async online(user_id: string) {
    await this.store.set(key(user_id), 1, PRESENCE_TTL_MS);
  }

  /** clear presence immediately */
  async offline(user_id: string) {
    await this.store.delete(key(user_id));
  }

  /** true if the presence key still has TTL > 0 */
  async isOnline(user_id: string) {
    return (await this.store.ttl(key(user_id))) > 0;
  }

  /** bulk check – returns the IDs (users(s)) that are currently online */
  async onlineSet(ids: string[]) {
    if (ids.length === 0) return new Set<string>();

    const pipeline = this.store['redis'].pipeline(); // reach into the ioredis instance once
    ids.forEach((id) => pipeline.pttl(key(id)));
    const replies = await pipeline.exec();

    const online = new Set<string>();
    replies.forEach(([, ttl], i) => {
      if (+ttl > 0) online.add(ids[i]);
    });
    return online;
  }
}
