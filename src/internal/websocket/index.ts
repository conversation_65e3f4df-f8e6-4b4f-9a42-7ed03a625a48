import { MODULE_TOKENS, SERVICE_TOKENS } from '@app/internal/ioc/tokens';
import { Logger } from '@app/internal/logger';
import { InvalidTokenError, TokenAuth } from '@app/internal/token/auth';
import { inject, injectable } from 'inversify';
import {
  App,
  HttpRequest,
  HttpResponse,
  TemplatedApp,
  WebSocket,
} from 'uWebSockets.js';
import { NotificationService } from '@app/services/notification/notification.service';
import { env } from '@app/config/env';

export interface WebSocketUserData {
  token?: string;
  user_id?: string;
  connection_id?: string;
}

export interface SendNotification {
  message: string;
  metadata?: Record<string, any>;
}

export interface SocketServer {
  /**
   * Start the WebSocket server.
   */
  start(): Promise<void>;

  /**
   * Broadcast an event to all connected clients.
   */
  broadcast(event_name: string, data: any): void;

  /**
   * Send an event to a single connected client by its WebSocket Id.
   */
  sendTo(connection_id: string, event_name: string, data: any): void;

  /**
   * Send an event to all clients associated with a specific user.
   */
  sendToUser(user_id: string, event_name: string, data: any): void;
}

@injectable()
export class WebSocketServer implements SocketServer {
  private app: TemplatedApp;
  private connection_map: Map<string, WebSocket<WebSocketUserData>>;
  private user_connection_map: Map<string, Set<string>>;
  private last_connection_id = 0;
  private server_start_time: Date;

  constructor(
    @inject(MODULE_TOKENS.Logger) private logger: Logger,
    @inject(MODULE_TOKENS.TokenAuth) private tokenAuth: TokenAuth,
    @inject(SERVICE_TOKENS.NotificationService)
    private notification: NotificationService,
  ) {
    this.app = App();
    this.connection_map = new Map();
    this.user_connection_map = new Map();
    this.server_start_time = new Date();
  }

  public async shutdown() {
    for (const [, ws] of this.connection_map.entries()) {
      ws.close();
    }
  }

  public async start(): Promise<void> {
    this.app.ws('/*', {
      compression: 0,
      maxPayloadLength: 16 * 1024,
      idleTimeout: 30,

      upgrade: (res: HttpResponse, req: HttpRequest, context) => {
        const queryString = req.getQuery();

        this.logger.log({ query: queryString });

        const searchParams = new URLSearchParams(queryString);
        const token = searchParams.get('token');

        if (!token) {
          res.writeStatus('UNAUTHORIZED');
          res.end('Unauthorized');
          this.logger.log('Unauthorized WebSocket connection attempt');
          return;
        }

        const userData: Partial<WebSocketUserData> = { token };

        res.upgrade(
          userData,
          req.getHeader('sec-websocket-key'),
          req.getHeader('sec-websocket-protocol'),
          req.getHeader('sec-websocket-extensions'),
          context,
        );
      },

      open: async (ws: WebSocket<WebSocketUserData>) => {
        const { token } = ws.getUserData();
        const connection_id = (++this.last_connection_id).toString();
        this.connection_map.set(connection_id, ws);
        ws.getUserData().connection_id = connection_id;

        if (token) {
          this.tokenAuth
            .verify<{ id: string; email: string }>(String(token))
            .then((payload) => {
              const user_id = payload.id;
              ws.getUserData().user_id = user_id;

              let connections = this.user_connection_map.get(user_id);
              if (!connections) {
                connections = new Set();
                this.user_connection_map.set(user_id, connections);
              }
              connections.add(connection_id);

              this.logger.log(
                `WebSocket connection ${connection_id} opened for user ${user_id}`,
              );
            })
            .catch((err) => {
              if (err instanceof InvalidTokenError) {
                this.logger.error(
                  err,
                  `Invalid token for connection ${connection_id}, closing socket...`,
                );
                ws.close();
              } else {
                this.logger.error(
                  err,
                  `Error verifying token for connection ${connection_id}, closing socket...`,
                );
                ws.close();
              }
            });
        }
      },

      message: async (ws, message) => {
        let decoded: string;
        try {
          decoded = Buffer.from(message).toString('utf8');
        } catch (err) {
          this.logger.error(err, 'Failed to decode socket message');
          return;
        }

        let parsed;
        try {
          parsed = JSON.parse(decoded);
        } catch (err) {
          this.logger.error(err, 'Failed to parse socket message');
          return;
        }

        const { event, data } = parsed;
        if (!event) return;

        // Handle various events
        await this.handleEvent(ws, event, data);
      },

      close: (ws) => {
        const connection_id = ws.getUserData().connection_id;
        this.connection_map.delete(connection_id);

        const user_id = ws.getUserData().user_id;
        if (user_id) {
          const connections = this.user_connection_map.get(user_id);
          if (connections) {
            connections.delete(connection_id);
            if (connections.size === 0) {
              this.user_connection_map.delete(user_id);
            }
          }
        }
      },
    });

    this.app.get('/health', (res: HttpResponse) => {
      const uptime = Date.now() - this.server_start_time.getTime();
      const activeConnections = this.connection_map.size;
      const activeUsers = this.user_connection_map.size;

      const healthData = {
        status: 'ok',
        uptime_ms: uptime,
        active_connections: activeConnections,
        active_users: activeUsers,
        server_time: new Date().toISOString(),
      };

      res.writeHeader('Content-Type', 'application/json');
      res.end(JSON.stringify(healthData));
    });

    const port = env.ws_port;
    return new Promise((resolve, reject) => {
      this.app.listen(port, (token) => {
        if (token) {
          this.logger.log(`Websocket server started on port ${port}`);
          resolve();
        } else {
          reject(new Error(`Failed to start Websocket server on port ${port}`));
        }
      });
    });
  }

  broadcast(event_name: string, data: any): void {
    const payload = JSON.stringify({ event: event_name, data });
    for (const [, ws] of this.connection_map.entries()) {
      ws.send(payload, false);
    }
  }

  sendTo(connection_id: string, event_name: string, data: any): void {
    const ws = this.connection_map.get(connection_id);
    if (!ws) {
      this.logger.log(`Connection ${connection_id} not found`);
      return;
    }
    const payload = JSON.stringify({ event: event_name, data });
    ws.send(payload, false);
  }

  sendToUser(user_id: string, event_name: string, data: any): void {
    const connection_set = this.user_connection_map.get(user_id);
    if (!connection_set) {
      this.logger.log(`No active connections for user ${user_id}`);
      return;
    }
    const payload = JSON.stringify({ event: event_name, data });
    for (const connection_id of connection_set) {
      const ws = this.connection_map.get(connection_id);
      if (ws) {
        ws.send(payload, false);
      }
    }
  }

  public disconnectUser(user_id: string): void {
    const connection_set = this.user_connection_map.get(user_id);
    if (!connection_set || connection_set.size === 0) {
      this.logger.log(`No WebSocket connections to close for user ${user_id}`);
      return;
    }

    this.logger.log(
      `Closing ${connection_set.size} WebSocket connections for user ${user_id}`,
    );

    const connectionsCopy = [...connection_set];

    for (const connection_id of connectionsCopy) {
      const ws = this.connection_map.get(connection_id);
      if (ws) {
        try {
          ws.close();

          this.logger.log(
            `Closed WebSocket connection ${connection_id} for user ${user_id}`,
          );
        } catch (error) {
          this.logger.error(
            error,
            `Error closing WebSocket connection ${connection_id} for user ${user_id}`,
          );
        }
      }
    }
  }

  public async handleEvent(
    ws: WebSocket<WebSocketUserData>,
    event_name: string,
    data: any,
  ): Promise<void> {
    const user_id = ws.getUserData()?.user_id;
    if (!user_id) {
      this.logger.log(`Cannot handle ${event_name} - no user_id on ws.`);
      return;
    }

    switch (event_name) {
      case 'auth:logout': {
        this.disconnectUser(user_id);
        return;
      }

      case 'notification:fetch': {
        // fetch all notifications
        const notifications = await this.notification.all(user_id);
        this.sendTo(
          ws.getUserData().connection_id,
          'notification:fetch:result',
          notifications,
        );
        break;
      }

      case 'notification:open': {
        if (!Array.isArray(data?.notification_ids)) return;
        await this.notification.markAsRead(data.notification_ids);

        this.sendTo(
          ws.getUserData().connection_id,
          'notification:open:result',
          { opened: data.notification_ids },
        );
        break;
      }

      default:
        this.logger.log(`Unhandled notification event: ${event_name}`);
        break;
    }
  }

  /**
   * Server-initiated push of a new notification to a user
   */
  public send(user_id: string, payload: SendNotification): void {
    this.sendToUser(user_id, 'notification:new', payload);
  }
}
