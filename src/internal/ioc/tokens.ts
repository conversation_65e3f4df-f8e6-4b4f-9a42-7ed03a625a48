export const SERVICE_TOKENS = {
  AdminService: Symbol.for('AdminService'),
  CommunityService: Symbol.for('CommunityService'),
  UserService: Symbol.for('UserService'),
  TrackService: Symbol.for('TrackService'),
  ReleaseService: Symbol.for('ReleaseService'),
  TrackDraftService: Symbol.for('TrackDraftService'),
  ArtistService: Symbol.for('ArtistService'),
  MediaService: Symbol.for('MediaService'),
  PostService: Symbol.for('PostService'),
  inviteService: Symbol.for('InviteService'),
  LabelService: Symbol.for('LabelService'),
  MonetizationService: Symbol.for('MonetizationService'),
  NotificationService: Symbol.for('NotificationService'),
  SubscriptionService: Symbol.for('SubscriptionService'),
  WalletService: Symbol.for('WalletService'),
  TransactionService: Symbol.for('TransactionService'),
  BeneficiaryService: Symbol.for('BeneficiaryService'),
  ReportsService: Symbol.for('ReportsService'),
  RoyaltyRunService: Symbol.for('RoyaltyRunService'),
};

export const MODULE_TOKENS = {
  Jwt: Symbol.for('Jwt'),
  Repository: Symbol.for('Repository'),
  KnexClient: Symbol.for('KnexClient'),
  Logger: Symbol.for('Logger'),
  ACMClient: Symbol.for('ACMClient'),
  ACM: Symbol.for('ACM'),
  CloudFrontClient: Symbol.for('CloudFrontClient'),
  CloudFront: Symbol.for('CloudFront'),
  S3Client: Symbol.for('S3Client'),
  S3: Symbol.for('S3'),
  SESClient: Symbol.for('SesClient'),
  SES: Symbol.for('SES'),
  SNSClient: Symbol.for('SNSClient'),
  SNS: Symbol.for('SNS'),
  Stripe: Symbol.for('Stripe'),
  Paystack: Symbol.for('Paystack'),
  RedisStore: Symbol.for('RedisStore'),
  PresenceCache: Symbol.for('PresenceCache'),
  JobQueueManager: Symbol.for('JobQueueManager'),
  TokenStore: Symbol.for('TokenStore'),
  TokenAuth: Symbol.for('TokenAuth'),
  Vibrate: Symbol.for('Vibrate'),
  Spotify: Symbol.for('Spotify'),
  Revelator: Symbol.for('Revelator'),
  IP2C: Symbol.for('IP2C'),
  GoogleOauthClient: Symbol.for('GoogleOauthClient'),
  SocketServer: Symbol.for('SocketServer'),
  FacebookOauthClient: Symbol.for('FacebookOauthClient'),
  InstagramOauthClient: Symbol.for('InstagramOauthClient'),
  TikTokOauthClient: Symbol.for('TikTokOauthClient'),
};

export const OPTIONS_TOKENS = {
  JwtOptions: Symbol.for('JwtOptions'),
};

export const MIDDLEWARE_TOKENS = {
  AuthMiddleware: Symbol.for('AuthMiddleware'),
  FileMiddleware: Symbol.for('FileMiddleware'),
  OptionalAuthMiddleware: Symbol.for('OptionalAuthMiddleware'),
  SubscriptionCheckMiddleware: Symbol.for('SubscriptionCheckMiddleware'),
};
