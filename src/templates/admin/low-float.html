<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Provider Float Alert</title>

    <style>
      * { margin:0; padding:0; font-family:Inter,Arial,Helvetica,sans-serif; -webkit-font-smoothing:antialiased; -moz-osx-font-smoothing:grayscale; }
      a { text-decoration:none; color:inherit; }
      body { font-weight:100; }
      .container { padding:50px 20px; width:100%; max-width:700px; }
      .paragraph { font-size:16px; line-height:1.8; }
      .button {
        background-color:#87ACF7;
        padding:12px 40px;
        border-radius:6px;
        display:inline-block;
        font-weight:600;
        color:#000;
      }
    </style>
  </head>
  <body>
    <table width="100%" border="0" cellspacing="0" cellpadding="0">
      <tr style="background-color:#b4d2ff;">
        <td align="center" style="padding:63px 0; font-size:26.55px;">
          <img src="https://s3.eu-west-1.amazonaws.com/develop.tallracks.xyz/makerverse-black.png" style="height:28px;" alt="Makerverse logo" />
        </td>
      </tr>

      <tr>
        <td align="center">
          <table class="container">
            <tr><td>
              <p style="font-size:20px; font-weight:600;">
                ⚠️ Insufficient Float Detected - {providerName}
              </p>

              <br /><br />

              <div class="paragraph">
                Hi&nbsp;Admin,
                <br /><br />
                Our monitoring system has detected that the available float for <strong>{providerName}</strong> has dropped below the configured threshold.
              </div>

              <br />

              <table width="100%" border="0" cellspacing="0" cellpadding="0" style="border:1px solid #e0e0e0; border-radius:4px;">
                <tr>
                  <td style="padding:20px;">
                    <p style="font-weight:600; margin-bottom:12px; font-size:16px;">Current Float Status:</p>
                    <p class="paragraph">Current Balance:&nbsp;<strong>{currentBalance}</strong></p>
                    <p class="paragraph">Amount tried to withdraw:&nbsp;<strong>{amount}</strong></p>
                    <p class="paragraph">Time Detected:&nbsp;<strong>{timeStamp}</strong></p>
                  </td>
                </tr>
              </table>

              <br /><br />

              <p class="paragraph">
                Please replenish the float as soon as possible (12 hours) to avoid failed customer transactions.
                If you have any questions or need assistance, contact the finance team at&nbsp;
                <a href="mailto:<EMAIL>" style="text-decoration:underline;"><EMAIL></a>.
              </p>

              <br />

              <p class="paragraph">
                Best regards,<br />
                Makerverse&nbsp;Finance Team
              </p>
            </td></tr>
          </table>
        </td>
      </tr>

      <tr style="background-color:#000; color:#fff;">
        <td align="center" style="padding:50px 30px;">
          <img src="https://s3.eu-west-1.amazonaws.com/develop.tallracks.xyz/Group+1171275814%402x.png" style="height:28px; margin-bottom:16px;" alt="Makerverse logo white" />

          <p class="paragraph">
            © {year} MAKERVERSE - Internal Notice<br /><br />
            If you're not the intended recipient, please delete this email.
          </p>
        </td>
      </tr>
    </table>
  </body>
</html>
