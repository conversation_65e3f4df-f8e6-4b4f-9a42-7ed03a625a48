import { ExtendedJoi } from '@app/utils/joi-utils';

export class PaginatedQueryDto {
  page_number: number;

  result_per_page: number;

  cursor: string;

  static validationSchema = ExtendedJoi.object({
    page_number: ExtendedJoi.number().min(1).default(1),
    result_per_page: ExtendedJoi.number().min(1).default(10),
    cursor: ExtendedJoi.string().optional(),
  });
}

export class PaginatedQueryWithDateRangeDto extends PaginatedQueryDto {
  from: Date;
  to: Date;
  release_id: string;

  static validationSchema = PaginatedQueryDto.validationSchema.concat(
    ExtendedJoi.object({
      from: ExtendedJoi.date().format('YYYY-MM-DD').optional(),
      to: ExtendedJoi.date().when('from', {
        is: ExtendedJoi.exist(),
        then: ExtendedJoi.date()
          .format('YYYY-MM-DD')
          .min(ExtendedJoi.ref('from'))
          .required(),
      }),
      release_id: ExtendedJoi.string().optional(),
    }),
  );
}
