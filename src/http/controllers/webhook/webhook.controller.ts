import { controller, httpPost, requestBody } from 'inversify-express-utils';
import { verifyStripeRequest } from '@app/http/middlewares/verify-stripe-request.middleware';
import { SuccessResponseDto } from '@app/http/dtos/http-response.dto';
import { inject } from 'inversify';
import { MODULE_TOKENS, SERVICE_TOKENS } from '@app/internal/ioc/tokens';
import { SubscriptionService } from '@app/services/subscription/subscription.service';
import { Stripe, SubscriptionInterval } from '@app/modules/stripe';
import { UserService } from '@app/services/user/user.service';
import { TransactionService } from '@app/services/transaction/transaction.service';
import { CommunityService } from '@app/services/community/community.service';
import {
  Currency,
  PaymentMethod,
  PaymentType,
  TransactionStatus,
  TransactionType,
} from '@app/services/transaction/entities/transaction.entity';
import { generateTransactionRef } from '@app/utils/transaction.util';
import { WalletService } from '@app/services/wallet/wallet.service';
import { WalletType } from '@app/services/wallet/entities/wallet.entity';
import { verifyPaystackRequest } from '@app/http/middlewares/verify-paystack-request';
import { JobQueueManager } from '@app/internal/bull';
import {
  emailQueue,
  SendEmailJob,
  sendNotificationJob,
  SendNotificationJob,
  UpsertRevelatorAccountJob,
  upsertRevelatorAccountQueue,
} from '@app/mq/jobs';
import { loadTemplate, render } from '@app/utils/template.utils';
import { BeneficiaryService } from '@app/services/beneficiary/beneficiary.service';
import { formatDateTime } from '@app/utils/date-utils';
import { UserCategory } from '@app/services/user/entities/user.entity';
import { MonetizationService } from '@app/services/monetization/monetization.service';

@controller('/webhook')
export class WebhookController {
  private readonly notificationQueue =
    this.jobQueueManager.getQueue<SendNotificationJob>(sendNotificationJob);

  private readonly emailQueue =
    this.jobQueueManager.getQueue<SendEmailJob>(emailQueue);

  private readonly upsertRevelatorAccountQueue =
    this.jobQueueManager.getQueue<UpsertRevelatorAccountJob>(
      upsertRevelatorAccountQueue,
    );

  constructor(
    @inject(MODULE_TOKENS.JobQueueManager)
    private readonly jobQueueManager: JobQueueManager,
    @inject(MODULE_TOKENS.Stripe) private readonly stripe: Stripe,
    @inject(SERVICE_TOKENS.SubscriptionService)
    private readonly subscriptions: SubscriptionService,
    @inject(SERVICE_TOKENS.UserService)
    private readonly user: UserService,
    @inject(SERVICE_TOKENS.TransactionService)
    private readonly transactions: TransactionService,
    @inject(SERVICE_TOKENS.WalletService)
    private readonly wallet: WalletService,
    @inject(SERVICE_TOKENS.CommunityService)
    private readonly community: CommunityService,
    @inject(SERVICE_TOKENS.BeneficiaryService)
    private readonly beneficiary: BeneficiaryService,
    @inject(SERVICE_TOKENS.MonetizationService)
    private readonly monetization: MonetizationService,
  ) {}

  @httpPost('/stripe', verifyStripeRequest)
  public async stripeEventListener(@requestBody() payload: any) {
    let user_id: string;

    switch (payload.type) {
      case 'invoice.paid': {
        const { user_id, price_id, setup_fee_price_id, includes_setup_fee } =
          payload.data.object.subscription_details.metadata;

        const sub = await this.subscriptions.getCurrentSubscription(user_id);

        const user = await this.user.describe(user_id);

        const community = await this.community.get(user.community_id);

        const plans = await this.monetization.getCommunitySubPlans(
          user.community_id,
        );

        const matchingPlan = plans.find(
          (p) =>
            p.metadata.pricing.annual === sub.metadata.price_id ||
            p.metadata.pricing.monthly === sub.metadata.price_id,
        );

        const today = new Date();

        const invoiceItem = payload.data.object.lines.data.find(
          (item: any) => item.price.id === price_id,
        );

        const interval = <Exclude<SubscriptionInterval, 'one_time'>>(
          this.stripe.getSubscriptionInterval(invoiceItem.price)
        );

        const next_payment_date = this.subscriptions.estimateNextPaymentDate(
          today,
          interval,
        );

        let grossAmount = invoiceItem.price.unit_amount / 100;
        const fee = Number((grossAmount * 0.1).toFixed(2));
        const netAmount = Number((grossAmount - fee).toFixed(2));

        if (includes_setup_fee === 'true') {
          const setupFeeItem = payload.data.object.lines.data.find(
            (item: any) => item.price.id === setup_fee_price_id,
          );

          const setupFee = setupFeeItem.price.unit_amount / 100;
          grossAmount += setupFee;
        }

        if (!sub) {
          const subscription = await this.subscriptions.subscribe({
            user_id: user_id,
            interval,
            next_payment_date,
            includes_setup_fee,
            payment_method: PaymentMethod.CARD,
            failed_at: null,
            amount: grossAmount,
            currency: invoiceItem.currency,
            description: `Subscription payment for ${invoiceItem.price.product}`,
            metadata: {
              sub_id: payload.data.object.subscription,
              product_id: invoiceItem.metadata.product_id,
              price_id,
              idempotency_key: payload.request.idempotency_key,
            },
          });

          if (
            [UserCategory.COMMUNITY_OWNER, UserCategory.CREATOR].includes(
              user.category,
            )
          ) {
            await this.upsertRevelatorAccountQueue.add(
              upsertRevelatorAccountQueue,
              { user_id: user_id },
            );
          }

          if (!user.community_level_user) {
            const wallet = await this.wallet.getOrCreate(
              user.community_owner.id,
              Currency.USD,
              WalletType.FIAT,
            );

            await this.wallet.credit({
              id: wallet.id,
              amount: netAmount,
              fee,
              currency: Currency.USD,
              community_id: user.community_id,
              subscription_id: subscription.id,
              payment_method: PaymentMethod.INTERNAL,
              payment_type: PaymentType.SUBSCRIPTION,
              reference: generateTransactionRef('sub'),
              description: `Subscription payment for ${invoiceItem.price.product}`,
              metadata: {
                sub_id: payload.data.object.subscription,
                product: invoiceItem.price.product,
                price_id: invoiceItem.price.id,
                idempotency_key: payload.request.idempotency_key,
              },
            });

            const template = loadTemplate('makerverse/successful-subscription');

            const html = render(template, {
              fname: user.first_name,
              communityName: community.name,
              planName: matchingPlan.name,
              subscriberEmail: user.email,
            });

            await this.emailQueue.add('email', {
              to: user.community_owner.email,
              subject: 'New Subscriber Alert 🔔',
              body: html,
            });
          }

          const template = loadTemplate('community/successful-subscription');
          const html = render(template, {
            fname: user.first_name,
            planName: matchingPlan.name,
            planPrice:
              matchingPlan.metadata.pricing.monthly === sub.metadata.price_id
                ? matchingPlan.price_per_month.toString()
                : matchingPlan.price_per_annum.toString(),
            startDate: formatDateTime(subscription.created_at),
            endDate: formatDateTime(subscription.next_payment_date),
            communityName: community.name,
            supportEmail: community.support_email,
            logoUrl: community.brand_themed_logo,
            color: community.brand_color,
          });

          await this.emailQueue.add('email', {
            to: user.email,
            subject: 'Subscription Successful 🚀',
            body: html,
          });
          break;
        }

        if (sub.metadata.idempotency_key === payload.request.idempotency_key) {
          break;
        }

        if (sub.status === 'paused') {
          const subscription = await this.subscriptions.renew(sub.id, {
            interval,
            next_payment_date,
            payment_method: PaymentMethod.CARD,
            failed_at: null,
            amount: grossAmount,
            currency: invoiceItem.currency,
            description: `Subscription payment for ${invoiceItem.price.product}`,
            metadata: {
              ...sub.metadata,
              idempotency_key: payload.request.idempotency_key,
            },
          });

          if (
            [UserCategory.COMMUNITY_OWNER, UserCategory.CREATOR].includes(
              user.category,
            )
          ) {
            await this.upsertRevelatorAccountQueue.add(
              upsertRevelatorAccountQueue,
              { user_id: user_id },
            );
          }

          if (!user.community_level_user) {
            const wallet = await this.wallet.getOrCreate(
              user.community_owner.id,
              Currency.USD,
              WalletType.FIAT,
            );

            await this.wallet.credit({
              id: wallet.id,
              amount: netAmount,
              fee,
              currency: Currency.USD,
              community_id: user.community_id,
              subscription_id: subscription.id,
              payment_method: PaymentMethod.INTERNAL,
              payment_type: PaymentType.SUBSCRIPTION,
              reference: generateTransactionRef('sub'),
              description: `Subscription payment for ${invoiceItem.price.product}`,
              metadata: {
                sub_id: payload.data.object.subscription,
                product: invoiceItem.price.product,
                price_id: invoiceItem.price.id,
                idempotency_key: payload.request.idempotency_key,
              },
            });
          }

          const template = loadTemplate('community/successful-subscription');
          const html = render(template, {
            fname: user.first_name,
            planName: matchingPlan.name,
            planPrice:
              matchingPlan.metadata.pricing.monthly === sub.metadata.price_id
                ? matchingPlan.price_per_month.toString()
                : matchingPlan.price_per_annum.toString(),
            startDate: formatDateTime(subscription.created_at),
            endDate: formatDateTime(subscription.next_payment_date),
            communityName: community.name,
            supportEmail: community.support_email,
            logoUrl: community.brand_themed_logo,
            color: community.brand_color,
          });

          await this.emailQueue.add('email', {
            to: user.email,
            subject: 'Subscription Successful 🚀',
            body: html,
          });
        }
        break;
      }
      case 'invoice.payment_failed': {
        user_id = payload.data.object.subscription_details.metadata.user_id;
        const sub = await this.subscriptions.getCurrentSubscription(user_id);

        if (sub) {
          if (
            sub.metadata.idempotency_key === payload.request.idempotency_key
          ) {
            break;
          }
          await this.subscriptions.update(sub.id, {
            status: 'paused',
            failed_at: new Date(),
            metadata: {
              ...sub.metadata,
              idempotency_key: payload.request.idempotency_key,
            },
          });

          const user = await this.user.getById(user_id);
          const invoiceItem = payload.data.object.lines.data.find(
            (item: any) => item.price.id === sub.metadata.price_id,
          );

          await this.transactions.create({
            user_id,
            community_id: user.community_id,
            subscription_id: sub.id,
            payment_method: PaymentMethod.CARD,
            payment_type: PaymentType.SUBSCRIPTION,
            reference: generateTransactionRef('sub'),
            status: TransactionStatus.FAILED,
            amount: payload.data.object.amount_due,
            currency: Currency.USD,
            type: TransactionType.DEBIT,
            description: `Subscription payment for ${invoiceItem.price.product}`,
            metadata: {
              sub_id: payload.data.object.subscription,
              product: invoiceItem.price.product,
              price_id: invoiceItem.price.id,
              idempotency_key: payload.request.idempotency_key,
            },
          });
        }
        break;
      }

      case 'customer.subscription.updated': {
        const stripeSub = payload.data.object;

        const user_id = stripeSub.metadata.user_id;

        const localSub = await this.subscriptions.getCurrentSubscription(
          user_id,
        );

        if (
          localSub &&
          localSub.metadata &&
          localSub.metadata.idempotency_key === payload.request.idempotency_key
        )
          break;

        if (localSub) {
          let newStatus: 'active' | 'paused' | 'cancelled';

          if (stripeSub.status === 'active') {
            newStatus = 'active';
          } else if (stripeSub.status === 'canceled') {
            newStatus = 'cancelled';
          } else {
            newStatus = 'paused';
          }

          const today = new Date();

          const priceItem =
            stripeSub.items.data.find(
              (item: any) => item.price.id === stripeSub.metadata.price_id,
            ) ?? stripeSub.items.data[0];

          const interval = <Exclude<SubscriptionInterval, 'one_time'>>(
            this.stripe.getSubscriptionInterval(priceItem.price)
          );

          const next_payment_date = this.subscriptions.estimateNextPaymentDate(
            today,
            interval,
          );

          const activeItem = stripeSub.items.data[0];

          const updatedMetadata = {
            ...localSub.metadata,
            sub_id: stripeSub.id,
            product_id: activeItem.price.product as string,
            price_id: activeItem.price.id,
            idempotency_key: payload.request.idempotency_key,
          };

          await this.subscriptions.update(localSub.id, {
            status: newStatus,
            next_payment_date,
            interval,
            metadata: updatedMetadata,
          });

          const user = await this.user.describe(user_id);
          const community = await this.community.get(user.community_id);
          const plans = await this.monetization.getCommunitySubPlans(
            user.community_id,
          );

          const matchingPlan = plans.find(
            (p) =>
              p.metadata.pricing.annual === updatedMetadata.price_id ||
              p.metadata.pricing.monthly === updatedMetadata.price_id,
          );

          const planPrice =
            updatedMetadata.price_id === matchingPlan.metadata.pricing.monthly
              ? matchingPlan.price_per_month
              : matchingPlan.price_per_annum;

          const isOwner = user.community_owner;
          const templateName = isOwner
            ? 'community/updated-subscription'
            : 'makerverse/updated-subscription';

          const template = loadTemplate(templateName);

          const html = render(template, {
            fname: user.first_name,
            planName: matchingPlan.name,
            planPrice: planPrice.toString(),
            cycle: interval,
            communityName: community?.name,
            supportEmail: community?.support_email,
            logoUrl: community.brand_themed_logo,
            color: community.brand_color,
          });

          await this.emailQueue.add('email', {
            to: user.email,
            subject: 'Subscription Successfully Updated',
            body: html,
          });
        }
        break;
      }

      case 'customer.subscription.deleted': {
        const stripeSub = payload.data.object;
        const user_id = stripeSub.metadata.user_id;

        const sub = await this.subscriptions.getCurrentSubscription(user_id);

        if (sub) {
          if (
            sub.metadata.idempotency_key === payload.request.idempotency_key
          ) {
            break;
          }
          await this.subscriptions.update(sub.id, {
            status: 'cancelled',
            metadata: {
              ...sub.metadata,
              idempotency_key: payload.request.idempotency_key,
            },
          });
        }

        const user = await this.user.describe(user_id);
        const community = await this.community.get(user.community_id);

        const isOwner = user.community_owner;
        const templateName = isOwner
          ? 'community/failed-subscription'
          : 'makerverse/failed-subscription';

        const template = loadTemplate(templateName);

        const html = render(template, {
          fname: user.first_name,
          cycle: sub.interval,
          subscribeUrl: `https://${community.custom_domain}/plans`,
          communityName: community.name,
          supportEmail: community?.support_email,
          logoUrl: community.brand_themed_logo,
          color: community.brand_color,
        });

        await this.emailQueue.add('email', {
          to: user.email,
          subject: 'Subscription Cancelled ❌',
          body: html,
        });
        break;
      }

      case 'payout.created': {
        const payout = payload.data.object;

        const transaction = await this.transactions.get(
          payout.metadata.transaction_id,
        );

        if (transaction) {
          if (
            transaction.metadata.idempotency_key ===
            payload.request.idempotency_key
          ) {
            break;
          }

          await this.transactions.update(transaction.id, {
            metadata: {
              ...transaction.metadata,
              payout_id: payout.id,
              idempotency_key: payload.request.idempotency_key,
            },
          });
        }

        break;
      }

      case 'payout.paid': {
        const payout = payload.data.object;

        const transaction = await this.transactions.get(
          payout.metadata.transaction_id,
        );

        if (transaction) {
          if (
            transaction.metadata.idempotency_key ===
            payload.request.idempotency_key
          ) {
            break;
          }

          // confirm the amount before updating anything.
          if (payout.amount === transaction.amount) {
            await this.transactions.update(transaction.id, {
              status: TransactionStatus.SUCCESSFUL,
              metadata: {
                ...transaction.metadata,
                idempotency_key: payload.request.idempotency_key,
              },
            });
          }

          await this.notificationQueue.add('send_notification_job', {
            user_id: transaction.user_id,
            message: `You just successfully initiated a withdrawal of ${Currency.USD}${transaction.amount} from you wallet`,
          });

          const user = await this.user.describe(transaction.user_id);
          const community = await this.community.get(user.community_id);

          const templateData = {
            communityName: user.community_level_user
              ? community.name
              : 'Makerverse',
            templateName: user.community_level_user
              ? 'makerverse/successful-withdrawal'
              : 'community/successful-withdrawal',
            redisData: {
              email: user.email,
              first_name: user.first_name,
              community_name: user.community_level_user
                ? community.name
                : 'Makerverse',
              community_id: null,
            },
          };

          if (user.community_level_user) {
            templateData.redisData.community_id = community.id;
          }

          const beneficiary = await this.beneficiary.get(
            transaction.beneficiary_id,
            user.id,
          );

          const htmlData = {
            fname: user.first_name,
            transactionRef: transaction.reference,
            dateTime: formatDateTime(transaction.created_at),
            currency: Currency.USD,
            beneficiaryName: beneficiary.bank_name,
            bankName: beneficiary?.bank_name,
            withdrawMethod: transaction.payment_method,
            fee: transaction.fee.toString(),
            amountReceived:
              beneficiary.currency === Currency.USD
                ? transaction.amount.toString()
                : String(transaction.amount * transaction.conversion_rate),
            communityName: templateData.communityName,
            ...(user.community_level_user && {
              logoUrl: community.brand_themed_logo,
              color: community.brand_color,
            }),
          };

          const template = loadTemplate(templateData.templateName);
          const html = render(template, htmlData);
          await this.emailQueue.add('email', {
            to: user.email,
            subject: 'Successful withdrawal from wallet',
            body: html,
          });
        }

        break;
      }
      case 'payout.failed': {
        const payout = payload.data.object;
        const user_id = payout.metadata.user_id;

        const transaction = await this.transactions.get(
          payout.metadata.transaction_id,
        );

        if (transaction) {
          if (
            transaction.metadata.idempotency_key ===
            payload.request.idempotency_key
          ) {
            break;
          }

          if (payout.amount === transaction.amount) {
            await this.transactions.update(transaction.id, {
              status: TransactionStatus.FAILED,
              metadata: {
                ...transaction.metadata,
                idempotency_key: payload.request.idempotency_key,
              },
            });

            const wallet = await this.wallet.getOrCreate(
              user_id,
              Currency.USD,
              WalletType.FIAT,
            );

            await this.wallet.credit({
              id: wallet.id,
              amount: transaction.amount,
              fee: transaction.fee,
              currency: transaction.currency,
              community_id: transaction.community_id,
              payment_method: PaymentMethod.INTERNAL,
              payment_type: PaymentType.REVERSAL,
              reference: generateTransactionRef('rev'),
            });
          }

          await this.notificationQueue.add('send_notification_job', {
            user_id: transaction.user_id,
            message: '',
          });

          const user = await this.user.describe(transaction.user_id);
          const community = await this.community.get(user.community_id);

          const templateData = {
            communityName: user.community_level_user
              ? community.name
              : 'Makerverse',
            templateName: user.community_level_user
              ? 'makerverse/failed-withdrawal'
              : 'community/failed-withdrawal',
            redisData: {
              email: user.email,
              first_name: user.first_name,
              community_name: user.community_level_user
                ? community.name
                : 'Makerverse',
              community_id: null,
            },
          };

          if (user.community_level_user) {
            templateData.redisData.community_id = community.id;
          }

          const beneficiary = await this.beneficiary.get(
            transaction.beneficiary_id,
            user.id,
          );

          const htmlData = {
            fname: user.first_name,
            transactionRef: transaction.reference,
            dateTime: formatDateTime(transaction.created_at),
            currency: Currency.USD,
            beneficiaryName: beneficiary.bank_name,
            bankName: beneficiary?.bank_name,
            withdrawMethod: transaction.payment_method,
            fee: transaction.fee.toString(),
            amountReceived:
              beneficiary.currency === Currency.USD
                ? transaction.amount.toString()
                : String(transaction.amount * transaction.conversion_rate),
            communityName: templateData.communityName,
            ...(user.community_level_user && {
              logoUrl: community.brand_themed_logo,
              color: community.brand_color,
            }),
          };

          const template = loadTemplate(templateData.templateName);
          const html = render(template, htmlData);
          await this.emailQueue.add('email', {
            to: user.email,
            subject: 'Failed withdrawal from wallet',
            body: html,
          });
        }

        break;
      }
    }
    return new SuccessResponseDto();
  }

  @httpPost('/paystack', verifyPaystackRequest)
  public async paystackEventListener(@requestBody() payload: any) {
    switch (payload.event) {
      case 'transfer.success': {
        const transferObj = payload.data;

        const transaction = await this.transactions.getByRef(
          transferObj.reference,
        );

        const idempotencyKey =
          transaction.metadata?.idempotency_key?.[payload.event];

        if (idempotencyKey === transferObj.reference) break;

        if (transaction) {
          if (
            transferObj.amount === transaction.amount &&
            transferObj.status === 'success'
          ) {
            await this.transactions.update(transaction.id, {
              status: TransactionStatus.SUCCESSFUL,
              metadata: {
                ...transaction.metadata,
                idempotency_key: {
                  ...(transaction.metadata?.idempotency_key ?? {}),
                  [payload.event]: transferObj.reference,
                },
              },
            });
          }

          await this.notificationQueue.add('send_notification_job', {
            user_id: transaction.user_id,
            message: `You just successfully initiated a withdrawal of ${Currency.USD}${transaction.amount} from you wallet`,
          });

          const user = await this.user.describe(transaction.user_id);
          const community = await this.community.get(user.community_id);

          const templateData = {
            communityName: user.community_level_user
              ? community.name
              : 'Makerverse',
            templateName: user.community_level_user
              ? 'makerverse/successful-withdrawal'
              : 'community/successful-withdrawal',
            redisData: {
              email: user.email,
              first_name: user.first_name,
              community_name: user.community_level_user
                ? community.name
                : 'Makerverse',
              community_id: null,
            },
          };

          if (user.community_level_user) {
            templateData.redisData.community_id = community.id;
          }

          const beneficiary = await this.beneficiary.get(
            transaction.beneficiary_id,
            user.id,
          );

          const htmlData = {
            fname: user.first_name,
            transactionRef: transaction.reference,
            dateTime: formatDateTime(transaction.created_at),
            currency: Currency.USD,
            beneficiaryName: beneficiary.bank_name,
            bankName: beneficiary?.bank_name,
            withdrawMethod: transaction.payment_method,
            fee: transaction.fee.toString(),
            amountReceived:
              beneficiary.currency === Currency.USD
                ? transaction.amount.toString()
                : String(transaction.amount * transaction.conversion_rate),
            communityName: templateData.communityName,
            ...(user.community_level_user && {
              logoUrl: community.brand_themed_logo,
              color: community.brand_color,
            }),
          };

          const template = loadTemplate(templateData.templateName);
          const html = render(template, htmlData);
          await this.emailQueue.add('email', {
            to: user.email,
            subject: 'Successful withdrawal from wallet',
            body: html,
          });
        }
        break;
      }
      case 'transfer.failed':
      case 'transfer.reversed': {
        const transferObj = payload.data;

        const transaction = await this.transactions.getByRef(
          transferObj.reference,
        );

        const idempotencyKey =
          transaction.metadata?.idempotency_key?.[payload.event];

        if (idempotencyKey === transferObj.reference) break;

        if (transaction) {
          if (transferObj.amount === transaction.amount) {
            await this.transactions.update(transaction.id, {
              status: TransactionStatus.FAILED,
              metadata: {
                ...transaction.metadata,
                idempotency_key: {
                  ...(transaction.metadata?.idempotency_key ?? {}),
                  [payload.event]: transferObj.reference,
                },
              },
            });
          }

          const creditAmountUsd =
            transaction.amount ??
            (transaction.conversion_rate
              ? transaction.amount / transaction.conversion_rate
              : transaction.amount);

          const wallet = await this.wallet.getOrCreate(
            transaction.user_id,
            Currency.USD,
            WalletType.FIAT,
          );

          await this.wallet.credit({
            id: wallet.id,
            amount: creditAmountUsd,
            currency: Currency.USD,
            community_id: transaction.community_id,
            payment_method: PaymentMethod.INTERNAL,
            payment_type: PaymentType.SUBSCRIPTION,
            reference: crypto.randomUUID(),
            description: 'Reversal for failed transfer',
          });

          await this.notificationQueue.add('send_notification_job', {
            user_id: transaction.user_id,
            message: `Withdrawal of ${transaction.currency}${transaction.amount} failed and has been reversed back to your wallet`,
          });

          const user = await this.user.describe(transaction.user_id);
          const community = await this.community.get(user.community_id);

          const templateData = {
            communityName: user.community_level_user
              ? community.name
              : 'Makerverse',
            templateName: user.community_level_user
              ? 'makerverse/successful-withdrawal'
              : 'community/successful-withdrawal',
            redisData: {
              email: user.email,
              first_name: user.first_name,
              community_name: user.community_level_user
                ? community.name
                : 'Makerverse',
              community_id: null,
            },
          };

          if (user.community_level_user) {
            templateData.redisData.community_id = community.id;
          }

          const beneficiary = await this.beneficiary.get(
            transaction.beneficiary_id,
            user.id,
          );

          const htmlData = {
            fname: user.first_name,
            transactionRef: transaction.reference,
            dateTime: formatDateTime(transaction.created_at),
            currency: Currency.USD,
            beneficiaryName: beneficiary.bank_name,
            bankName: beneficiary?.bank_name,
            withdrawMethod: transaction.payment_method,
            fee: transaction.fee.toString(),
            amountReceived:
              beneficiary.currency === Currency.USD
                ? transaction.amount.toString()
                : String(transaction.amount * transaction.conversion_rate),
            communityName: templateData.communityName,
            ...(user.community_level_user && {
              logoUrl: community.brand_themed_logo,
              color: community.brand_color,
            }),
          };

          const template = loadTemplate(templateData.templateName);
          const html = render(template, htmlData);
          await this.emailQueue.add('email', {
            to: user.email,
            subject: 'Failed withdrawal from wallet',
            body: html,
          });
        }
        break;
      }
    }
    return new SuccessResponseDto();
  }
}
