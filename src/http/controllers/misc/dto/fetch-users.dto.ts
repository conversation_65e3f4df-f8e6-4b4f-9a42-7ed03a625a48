import { PaginatedQueryDto } from '@app/http/dtos/paginated-query.dto';
import { UserCategory } from '@app/services/user/entities/user.entity';
import Joi from 'joi';

export class FetchUsersDto extends PaginatedQueryDto {
  username?: string;
  category?: UserCategory;

  static validationSchema = PaginatedQueryDto.validationSchema.concat(
    Joi.object({
      username: Joi.string().optional(),
      category: Joi.string()
        .valid(...Object.values(UserCategory))
        .optional(),
    }),
  );
}
