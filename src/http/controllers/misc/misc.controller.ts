import {
  controller,
  httpGet,
  httpPost,
  queryParam,
  request as httpReq,
  requestBody,
  requestParam,
} from 'inversify-express-utils';
import countryPhoneCodeList from '@app/misc/country-phone-code.json';
import {
  HttpResponseDto,
  SuccessResponseDto,
} from '@app/http/dtos/http-response.dto';
import {
  MIDDLEWARE_TOKENS,
  MODULE_TOKENS,
  SERVICE_TOKENS,
} from '@app/internal/ioc/tokens';
import { inject } from 'inversify';
import { ResendOtpDto } from './dto/resend-otp.dto';
import { autoValidate } from '@app/http/middlewares/validation.middleware';
import { ApplicationError } from '@app/internal/errors';
import { RedisStore } from '@app/internal/redis/store';
import { prefixKey } from '@app/utils/prefix-key';
import { StatusCodes } from 'http-status-codes';
import { generateOtp } from '@app/utils/otp-utils';
import { DURATION } from '@app/internal/enums';
import { UsernameCheckQueryDto } from './dto/username-check.dto';
import { UserService } from '@app/services/user/user.service';
import { VerifyOtpDto } from '../auth/dto/verify-otp.dto';
import { inCategory } from '@app/http/middlewares/user-category.middleware';
import { UserCategory } from '@app/services/user/entities/user.entity';
import { RequestWithClaims } from '@app/internal/types';
import { FetchUsersDto } from './dto/fetch-users.dto';
import { JobQueueManager } from '@app/internal/bull';
import { emailQueue, SendEmailJob } from '@app/mq/jobs';
import { SpotifyClient } from '@app/modules/spotify';
import { IdDto } from '@app/http/dtos/id.dto';
import { camelToSnakeCase } from '@app/utils/camel-to-snake-case';
import { Revelator } from '@app/modules/revelator';
import dspIcons from '@app/misc/dsp-icons.json';
import { loadTemplate, render } from '@app/utils/template.utils';
import { FetchCreatorMonetizationPolicyQueryDto } from '@app/http/controllers/misc/dto/fetch-creator-monetization-policy';
import { hasPermissions } from '@app/http/middlewares/permissions.middleware';
import { PermissionResource } from '@app/utils/permissions.utils';
import { CommunityNameCheckQueryDto } from './dto/community-name-check.dto';
import { CommunityService } from '@app/services/community/community.service';
import { env } from '@app/config/env';
import distributorIds from '@app/misc/distributor-ids.json';
import deliveryTypes from '@app/misc/delivery-type.json';
import releaseTypes from '@app/misc/release-type.json';
import { Stripe } from '@app/modules/stripe';
import { safeJsonParse } from '@app/utils/object';
import { BanksListQueryDto } from './dto/query-bank-list';
import bankList from '@app/misc/bank-list.json';
import { ResolveAccountDto } from './dto/resolve-bank-account';
import { Paystack } from '@app/modules/paystack';
import { ValidateAccountDto } from './dto/validate-account';
import { GetExchangeRateQuoteDto } from './dto/exchange-rate-quote.dto';

const communityOwnerSubPlansKey = 'app:co_sub_plans';

@controller('')
export class MiscController {
  private readonly emailQueue =
    this.jobQueueManager.getQueue<SendEmailJob>(emailQueue);
  constructor(
    @inject(MODULE_TOKENS.Revelator)
    private readonly revelator: Revelator,
    @inject(MODULE_TOKENS.JobQueueManager)
    private readonly jobQueueManager: JobQueueManager,
    @inject(MODULE_TOKENS.RedisStore)
    private readonly redisStore: RedisStore,
    @inject(SERVICE_TOKENS.UserService)
    private readonly user: UserService,
    @inject(MODULE_TOKENS.Spotify) private readonly spotify: SpotifyClient,
    @inject(MODULE_TOKENS.Stripe) private readonly stripe: Stripe,
    @inject(MODULE_TOKENS.Paystack) private readonly paystack: Paystack,
    @inject(SERVICE_TOKENS.CommunityService)
    private readonly community: CommunityService,
  ) {}

  @httpGet('/community/owner/subscription/plans')
  public async fetchCommunityOwnerPlans() {
    let plans: Record<string, any>[] = await this.redisStore
      .get(communityOwnerSubPlansKey)
      .then(safeJsonParse);

    if (plans == null) {
      const products = await this.stripe.searchProductsWithPricing({
        query: "-metadata['plan_type']:'generic' AND active:'true'",
        active: true,
      });

      plans = products.data.map((product) => {
        const metadata: Record<string, any> = Object.keys(
          product.metadata,
        ).reduce((acc, key) => {
          acc[key] = safeJsonParse(product.metadata[key]);
          return acc;
        }, {});

        return {
          id: product.id,
          name: product.name,
          default_price: product.default_price,
          description: product.description,
          metadata,
          prices:
            metadata.tier === 4
              ? []
              : product.prices.map((price) => {
                  let interval: string;

                  if (price.type === 'one_time') {
                    interval = price.type;
                  } else if (price.type === 'recurring') {
                    switch (price.recurring.interval) {
                      case 'day':
                        interval = 'daily';
                        break;
                      case 'week':
                        interval = 'weekly';
                        break;
                      case 'month':
                        interval = 'monthly';
                        break;
                      case 'year':
                        interval = 'annually';
                        break;
                    }
                  }

                  return {
                    id: price.id,
                    nickname: price.nickname,
                    product: price.product,
                    interval,
                    currency: price.currency,
                    unit_amount: price.unit_amount / 100,
                    unit_amount_decimal: price.unit_amount_decimal,
                  };
                }),
        };
      });

      await this.redisStore.set(
        communityOwnerSubPlansKey,
        JSON.stringify(plans),
        DURATION.DAYS,
      );
    }

    return new SuccessResponseDto({
      data: plans,
    });
  }

  @httpGet('/analytics/distributor/list')
  public fetchDistributionAnalyticsStoreSelection() {
    return new SuccessResponseDto({ data: distributorIds });
  }

  @httpGet('/country/phone-code/list')
  public getCountryPhoneCodeList() {
    return new HttpResponseDto({
      message: 'success',
      data: countryPhoneCodeList,
    });
  }

  @httpGet(
    '/username/check',
    autoValidate(UsernameCheckQueryDto.validateSchema, 'query'),
  )
  public async checkAvailable(
    @queryParam() { username }: UsernameCheckQueryDto,
  ) {
    const user = await this.user.getByUsername(username);

    if (user != null) {
      throw new ApplicationError(StatusCodes.CONFLICT, 'Username is taken');
    }

    return new SuccessResponseDto();
  }

  @httpGet('/community/check')
  public async checkCommunity(
    @queryParam() { community_name }: CommunityNameCheckQueryDto,
  ) {
    const community = await this.community.queryByName(community_name);

    if (community) {
      throw new ApplicationError(
        StatusCodes.CONFLICT,
        'Community name is taken',
      );
    }

    return new SuccessResponseDto();
  }

  @httpGet(
    '/users',
    autoValidate(FetchUsersDto.validationSchema, 'query'),
    MIDDLEWARE_TOKENS.AuthMiddleware,
  )
  public async fetchAllUsers(
    @queryParam()
    { username, category, page_number, result_per_page }: FetchUsersDto,
    @httpReq() { claim }: RequestWithClaims,
  ) {
    const community_id = claim.community_id;
    const users = await this.user.all(community_id, {
      username,
      category,
      result_per_page,
      page_number,
      selectFields: [
        'id',
        'first_name',
        'last_name',
        'display_name',
        'username',
        'bio',
        'url',
        'accounts',
        'collaborators',
        'profile_picture',
        'cover_picture',
        'category',
        'country',
        'follower_count',
        'following_count',
      ],
    });

    return new SuccessResponseDto({ data: users });
  }

  @httpPost('/otp/verify', autoValidate(VerifyOtpDto.validationSchema))
  public async verifyOtp(@requestBody() payload: VerifyOtpDto) {
    const otpVerificationKey = prefixKey(payload.otp_token, payload.otp);

    const metadata = await this.redisStore.get<{
      email: string;
      community_name: string;
    }>(otpVerificationKey);

    if (metadata == null) {
      throw new ApplicationError(StatusCodes.BAD_REQUEST, 'invalid otp');
    }

    return new SuccessResponseDto();
  }

  @httpPost('/otp/resend', autoValidate(ResendOtpDto.validationSchema))
  public async resendOtp(@requestBody() payload: ResendOtpDto) {
    const metadata = await this.redisStore.get<
      Partial<{
        community_id: string;
        email: string;
        phone: string;
        community_name: string;
        first_name: string;
      }>
    >(payload.otp_token);

    if (metadata == null) {
      throw new ApplicationError(
        StatusCodes.GONE,
        'Cannot resend otp at this time, Please try again later',
      );
    }

    const otp = generateOtp(6);
    let htmlData;
    let template;

    if (metadata?.community_id) {
      const community = await this.community.get(metadata?.community_id);
      htmlData = {
        fname: metadata.first_name,
        otp,
        communityName: metadata.community_name,
        logoUrl: community?.brand_themed_logo,
        color: community?.brand_color,
      };

      template = loadTemplate('community/otp');
    } else {
      htmlData = {
        fname: metadata.first_name,
        otp,
        communityName: metadata.community_name,
      };

      template = loadTemplate('makerverse/otp');
    }

    const html = render(template, htmlData);

    await this.emailQueue.add('email', {
      to: metadata.email,
      subject: 'Otp Resend ',
      body: html,
    });

    // create verification key making otp itself act as a private key
    const otpVerificationKey = prefixKey(payload.otp_token, otp);

    await this.redisStore.set(
      otpVerificationKey,
      {
        community_id: metadata?.community_id,
        email: metadata?.email,
        community_name: metadata?.community_name,
        first_name: metadata?.first_name,
      },
      5 * DURATION.MINUTES,
    );

    return new SuccessResponseDto({ data: { otp_token: payload.otp_token } });
  }

  @httpGet('/dsps/channel/list')
  public channelList() {
    return new SuccessResponseDto({ data: deliveryTypes });
  }

  @httpGet('/dsps/format/list')
  public formatList() {
    return new SuccessResponseDto({ data: releaseTypes });
  }

  @httpGet(
    '/dsps/store/list',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([
      UserCategory.COMMUNITY_OWNER,
      UserCategory.CREATOR,
      UserCategory.TEAM_MEMBER,
    ]),
  )
  async storeList(@httpReq() { claim }: RequestWithClaims) {
    const user = await this.user.getById(claim?.team_id ?? claim.id);

    let partnerUserId = user.revelator_id;
    let partnerUserEmail = user.email;
    let partnerEnterpriseName = user.enterprise_name;

    const roleDesc = await this.user.describe(claim.id);

    if (roleDesc.community_level_user) {
      partnerUserId = env.revelator_parent_account_id;
      partnerUserEmail = env.revelator_parent_account_id;
      partnerEnterpriseName = env.revelator_enterprise_name;
    }

    const stores = await this.revelator
      .listStores(partnerUserId)
      .then((s) =>
        s
          .filter((ds) => ds.isActive)
          .sort((a, b) => a.name.localeCompare(b.name)),
      )
      .then((s) => camelToSnakeCase<Record<string, any>[]>(s));

    for (const store of stores) {
      const dspIcon = dspIcons.find(
        (i) => i.distributor_store_id === store.distributor_store_id,
      );

      if (dspIcon) {
        store['icon'] = dspIcon.icon;
      }
    }

    const trackRetailPriceList = await this.revelator.listTrackRetailPriceTiers(
      partnerUserId,
      partnerUserEmail,
      partnerEnterpriseName,
    );

    for (const store of stores) {
      store.track_retail_price_tiers = [];

      for (const trackRetailPrice of trackRetailPriceList) {
        if (
          trackRetailPrice.distributorStoreId === store.distributor_store_id
        ) {
          store.track_retail_price_tiers.push(
            camelToSnakeCase(trackRetailPrice),
          );
        }
      }
    }

    return new SuccessResponseDto({
      data: stores,
    });
  }

  @httpGet('/dsps/language/list')
  async languageList() {
    const data = await this.revelator
      .listLanguages()
      .then((d) => camelToSnakeCase(d));

    return new SuccessResponseDto({
      data,
    });
  }

  @httpGet('/dsps/genre/list')
  async getGenre() {
    const data: any[] = await this.revelator
      .listGenres()
      .then((d) => camelToSnakeCase(d));

    const genreList = [...data];

    if (data?.length > 0) {
      for (const genre of data) {
        if (genre.parent_id != null) {
          const parentGenreIdx = genreList.findIndex(
            (p) => p.music_style_id === genre.parent_id,
          );

          const parentGenre = genreList[parentGenreIdx];

          if (!parentGenre?.sub_genres) {
            parentGenre.sub_genres = [genre];
          } else {
            parentGenre.sub_genres.push(genre);
          }

          const idx = genreList.findIndex(
            (g) => g.music_style_id === genre.music_style_id,
          );

          genreList.splice(idx, 1);
        } else {
          genre.sub_genres = [];
        }
      }
    }

    return new SuccessResponseDto({
      data: genreList,
    });
  }

  @httpGet('/dsps/track-property')
  async trackProperyList() {
    const data = await this.revelator
      .listTrackProperties()
      .then((d) => camelToSnakeCase(d));

    return new SuccessResponseDto({
      data,
    });
  }

  @httpGet('/dsps/contributor/list')
  async contributorRoleList() {
    const data = await this.revelator
      .listContributorRoles()
      .then((d) => camelToSnakeCase(d));

    return new SuccessResponseDto({
      data,
    });
  }

  @httpGet('/dsps/publisher/type')
  getPublisherType() {
    return new SuccessResponseDto({
      data: [
        { value: 1, description: 'Copyright control (self-published)' },
        { value: 2, description: 'Published (managed by a publisher)' },
        { value: 3, description: 'Public domain (no publisher)' },
      ],
    });
  }

  @httpGet('/dsps/timezone/list')
  async timezoneList() {
    const data = await this.revelator
      .listTimezones()
      .then((d) => camelToSnakeCase(d));

    return new SuccessResponseDto({
      data,
    });
  }

  @httpGet(
    '/dsps/monetization-policy/list',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([
      UserCategory.COMMUNITY_OWNER,
      UserCategory.CREATOR,
      UserCategory.TEAM_MEMBER,
    ]),
    hasPermissions([
      {
        resource: PermissionResource.ContractManagement,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
  )
  async getMonetizationPolicies(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { creator_id }: FetchCreatorMonetizationPolicyQueryDto,
  ) {
    const user = await this.user.getById(claim?.team_id ?? claim.id);

    let partnerUserId = user.revelator_id;

    let partnerUserEmail = user.email;

    let partnerEnterpriseName = user.enterprise_name;

    const roleDesc = await this.user.describe(claim.id);

    if (roleDesc.community_level_user) {
      if (!creator_id) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          "'creator_id' is required for community owners",
        );
      }

      const creator = await this.user.get(creator_id, claim.community_id);

      if (!creator) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          "invalid 'creator_id'",
        );
      }

      partnerUserId = creator.revelator_id;
      partnerUserEmail = creator.email;
      partnerEnterpriseName = `${creator.first_name} ${creator.last_name}`;
    }

    if (roleDesc.category === UserCategory.TEAM_MEMBER) {
      partnerUserId = roleDesc.team_owner.revelator_id;
      partnerUserEmail = roleDesc.team_owner.email;
      const { first_name, last_name } = roleDesc.team_owner;
      partnerEnterpriseName = `${first_name} ${last_name}`;
    }

    const storePolicyList = await this.revelator
      .listMonetizationPolicies(
        partnerUserId,
      )
      .then((d) => camelToSnakeCase(d));

    const policies = [];

    const policyMap = {};

    for (const storePolicy of storePolicyList) {
      if (!policyMap[storePolicy.distributor_store_id]) {
        policyMap[storePolicy.distributor_store_id] = {
          distributor_store_id: storePolicy.distributor_store_id,
          policies: [],
        };

        policies.push(policyMap[storePolicy.distributor_store_id]);
      }

      policyMap[storePolicy.distributor_store_id].policies.push({
        name: storePolicy.name,
        policy_id: storePolicy.policy_id,
        order_num: storePolicy.order_num,
      });
    }

    return new SuccessResponseDto({
      data: policies,
    });
  }

  @httpGet('/dsps/country/list')
  async countryList() {
    const data = await this.revelator
      .listCountries()
      .then((d) => camelToSnakeCase(d));

    return new SuccessResponseDto({
      data,
    });
  }

  @httpGet('/spotify/preview')
  async getPreview(@queryParam('link') link: string) {
    return this.spotify.getPreview(link);
  }

  @httpGet(
    '/spotify/artist/:id/top-tracks',
    autoValidate(IdDto.validationSchema, 'params'),
  )
  async getArtistTopTracks(@requestParam() { id }: IdDto) {
    return this.spotify.getArtistTopTracks(id);
  }

  @httpGet('/bank-list', MIDDLEWARE_TOKENS.AuthMiddleware)
  async getBankList(@queryParam() { country_code }: BanksListQueryDto) {
    const banks = await bankList[country_code];
    return new HttpResponseDto({
      message: 'success',
      data: banks,
    });
  }

  @httpPost('/bank/resolve', MIDDLEWARE_TOKENS.AuthMiddleware)
  async resolveAccountNumber(@requestBody() payload: ResolveAccountDto) {
    const resolve = await this.paystack.resolveAccountNumber(payload);

    return new HttpResponseDto({
      message: 'success',
      data: resolve,
    });
  }

  @httpPost('/bank/validate', MIDDLEWARE_TOKENS.AuthMiddleware)
  async validateAccount(@requestBody() payload: ValidateAccountDto) {
    const validate = await this.paystack.validateAccount(payload);

    return new HttpResponseDto({
      message: 'success',
      data: validate,
    });
  }

  @httpGet('/exchange-rate/quote')
  async getQuote(@queryParam() payload: GetExchangeRateQuoteDto) {
    const quote = await this.stripe.createFxQuote({
      to_currency: payload.to,
      from_currencies: [payload.from],
      lock_duration: 'none',
    });

    const rateBlock = quote.rates[payload.from.toLowerCase()];
    const baseRate = rateBlock.rate_details.base_rate;
    const fxFeeRate = rateBlock.rate_details.fx_fee_rate;

    const data = {
      from_currency: payload.from,
      to_currency: payload.to,

      amount_from: payload.amount,
      amount_to: payload.amount * baseRate,

      base_rate: baseRate,
      fx_fee_rate: fxFeeRate,
    };

    return new HttpResponseDto({
      message: 'success',
      data,
    });
  }
}
