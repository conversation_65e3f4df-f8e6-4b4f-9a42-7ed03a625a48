import { SuccessResponseDto } from '@app/http/dtos/http-response.dto';
import { connectedArtistAccount } from '@app/http/middlewares/account.middleware';
import { inCategory } from '@app/http/middlewares/user-category.middleware';
import { autoValidate } from '@app/http/middlewares/validation.middleware';
import {
  MIDDLEWARE_TOKENS,
  MODULE_TOKENS,
  SERVICE_TOKENS,
} from '@app/internal/ioc/tokens';
import { Claim, RequestWithClaims } from '@app/internal/types';
import {
  ChannelStreamPath,
  EngagementChannels,
  MusicChannels,
  Vibrate,
  VibrateChannels,
} from '@app/modules/vibrate';
import { UserCategory } from '@app/services/user/entities/user.entity';
import { inject } from 'inversify';
import {
  controller,
  httpGet,
  queryParam,
  request,
} from 'inversify-express-utils';
import { omit, reduce } from 'lodash';
import { GetStreamEngagementQueryDto } from './dto/get-stream-engagement.dto';
import { GetSocialEngagementQueryDto } from './dto/get-social-engagement.dto';
import { GetActivePlaylistQueryDto } from './dto/get-active-playlist.dto';
import { GetPlaylistReachQueryDto } from './dto/get-playlist-reach.dto';
import { GetTopTracksQueryDto } from './dto/get-top-tracks.dto';
import { GetEngagementInLocationQueryDto } from './dto/get-engagements-in-location.dto';
import { GetTopPlaylistsQueryDto } from './dto/get-top-playlists.dto';
import { GetTopPlaylistedTracksQueryDto } from './dto/get-top-playlisted-tracks.dto';
import { GetVideosQueryDto } from './dto/get-videos.dto';
import { UserDescription, UserService } from '@app/services/user/user.service';
import { hasPermissions } from '@app/http/middlewares/permissions.middleware';
import { PermissionResource } from '@app/utils/permissions.utils';
import {
  endOfMonth,
  format,
  isWithinInterval,
  parseISO,
  startOfMonth,
  subMonths,
} from 'date-fns';
import { calculatePercentageChange } from '@app/utils/fanbase.utils';
import { ApplicationError } from '@app/internal/errors';
import { StatusCodes } from 'http-status-codes';
import { OptionalIdFiltersDto } from '@app/http/dtos/id.dto';

@controller('/engagement')
export class EngagementController {
  constructor(
    @inject(MODULE_TOKENS.Vibrate) private readonly vibrate: Vibrate,
    @inject(SERVICE_TOKENS.UserService) private readonly user: UserService,
  ) {}

  private async fetchVibrateIds(
    claim: Claim,
    user_id?: string,
  ): Promise<string[]> {
    const roleDesc = await this.simplifyProxyQuery(claim, user_id);

    if (roleDesc.community_level_user) {
      const communityUsers = await this.user.fetchVibrateUsers(
        roleDesc.community_id,
      );

      return communityUsers.map((user) => user.vibrate_id);
    } else if (roleDesc.vibrate_id) return [roleDesc.vibrate_id];

    return [];
  }

  @httpGet('/platform/list')
  public async platformList() {
    return {
      stream_analytics: [
        VibrateChannels.AIRPLAY,
        VibrateChannels.SHAZAM,
        VibrateChannels.SOUNDCLOUD,
        VibrateChannels.SPOTIFY,
        VibrateChannels.YOUTUBE,
      ],
      social_engagements: [
        VibrateChannels.INSTAGRAM,
        VibrateChannels.YOUTUBE,
        VibrateChannels.TIKTOK,
        VibrateChannels.TWITTER,
      ],
      active_playlists: [VibrateChannels.SPOTIFY, VibrateChannels.APPLE],
      playlist_reach: [VibrateChannels.SPOTIFY],
      engagement_in_locations: [
        VibrateChannels.SPOTIFY,
        VibrateChannels.AIRPLAY,
        VibrateChannels.YOUTUBE,
      ],
      top_playlists: [VibrateChannels.SPOTIFY, VibrateChannels.APPLE],
      top_tracks: [
        VibrateChannels.SPOTIFY,
        VibrateChannels.SOUNDCLOUD,
        VibrateChannels.YOUTUBE,
        VibrateChannels.SHAZAM,
      ],
      videos: [VibrateChannels.YOUTUBE],
    };
  }

  @httpGet(
    '/streams/total',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
    inCategory([
      UserCategory.CREATOR,
      UserCategory.COMMUNITY_OWNER,
      UserCategory.TEAM_MEMBER,
    ]),
    connectedArtistAccount,
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
  )
  public async getTotalStreams(
    @request() { claim }: RequestWithClaims,
    @queryParam() { user_id }: OptionalIdFiltersDto,
  ) {
    const vibrateIds = await this.fetchVibrateIds(claim, user_id);
    const channels = omit(EngagementChannels, ['DEEZER']);

    const currentDate = new Date();
    const currentMonthStart = startOfMonth(currentDate);
    const currentMonthEnd = endOfMonth(currentDate);

    const previousMonthStart = startOfMonth(subMonths(currentDate, 1));
    const previousMonthEnd = endOfMonth(subMonths(currentDate, 1));

    const promises: Promise<
      Record<
        string,
        {
          total: number;
          previousTotal: number;
          count: number;
          percentageChange: number;
        }
      >
    >[] = [];

    for (const vibrateId of vibrateIds) {
      for (const key in channels) {
        const channel = channels[key as keyof typeof channels];
        const channelStreamPath = ChannelStreamPath[key];

        const responsePromise = Promise.all([
          // Fetch current month data
          this.vibrate
            .getStreamData({
              channel,
              uuid: vibrateId,
              channelStreamPath,
              query: {
                from: format(currentMonthStart, 'yyyy-MM-dd'),
                to: format(currentMonthEnd, 'yyyy-MM-dd'),
              },
            })
            .then((res) => res?.data?.data),
          // Fetch previous month data
          this.vibrate
            .getStreamData({
              channel,
              uuid: vibrateId,
              channelStreamPath,
              query: {
                from: format(previousMonthStart, 'yyyy-MM-dd'),
                to: format(previousMonthEnd, 'yyyy-MM-dd'),
              },
            })
            .then((res) => res?.data?.data),
        ]).then(([currentData, previousData]) => {
          const currentStreamNumbers = Object.entries<number>(currentData)
            .filter(([dateStr]) => {
              const date = parseISO(dateStr);
              return isWithinInterval(date, {
                start: format(currentMonthStart, 'yyyy-MM-dd'),
                end: format(currentMonthEnd, 'yyyy-MM-dd'),
              });
            })
            .map(([, value]) => value);

          const previousStreamNumbers = Object.entries<number>(previousData)
            .filter(([dateStr]) => {
              const date = parseISO(dateStr);
              return isWithinInterval(date, {
                start: format(previousMonthStart, 'yyyy-MM-dd'),
                end: format(previousMonthEnd, 'yyyy-MM-dd'),
              });
            })
            .map(([, value]) => value);

          const currentTotal =
            currentStreamNumbers.length > 0
              ? Math.round(
                  currentStreamNumbers.reduce((acc, val) => acc + val, 0) /
                    currentStreamNumbers.length,
                )
              : 0;

          const previousTotal =
            previousStreamNumbers.length > 0
              ? Math.round(
                  previousStreamNumbers.reduce((acc, val) => acc + val, 0) /
                    previousStreamNumbers.length,
                )
              : 0;

          let count = currentTotal;
          let percentageChange = calculatePercentageChange(
            currentTotal,
            previousTotal,
          );

          // If there's no data for the current month, use previous month's data
          if (currentTotal === 0 && previousTotal > 0) {
            count = previousTotal;
            percentageChange = 0; // No change since we're using the same month's data
          }

          return {
            [channel]: {
              total: currentTotal,
              previousTotal: previousTotal,
              count,
              percentageChange,
            },
          };
        });

        promises.push(responsePromise);
      }
    }

    const allPlatformTotals = await Promise.all(promises);

    // Combine results for all users
    const combinedTotals = allPlatformTotals.reduce(
      (acc, curr) => {
        for (const [channel, data] of Object.entries(curr)) {
          if (!acc[channel]) {
            acc[channel] = {
              total: 0,
              previousTotal: 0,
              count: 0,
              percentageChange: 0,
            };
          }
          acc[channel].total += data?.total || 0;
          acc[channel].previousTotal += data?.previousTotal || 0;
        }
        return acc;
      },
      {} as Record<
        string,
        {
          total: number;
          previousTotal: number;
          count: number;
          percentageChange: number;
        }
      >,
    );

    // Now compute the overall count and percentage change for each channel
    for (const channel of Object.keys(combinedTotals)) {
      const channelData = combinedTotals[channel];

      const currentTotal = channelData?.total;
      const previousTotal = channelData?.previousTotal;

      let count = currentTotal;
      let percentageChange = calculatePercentageChange(
        currentTotal,
        previousTotal,
      );

      // If there's no data for the current month, use previous month's data
      if (currentTotal === 0 && previousTotal > 0) {
        count = previousTotal;
        percentageChange = 0;
      }

      channelData.count = count;
      channelData.percentageChange = percentageChange;
    }

    const formattedData = Object.entries(combinedTotals).map(
      ([key, { count, percentageChange }]) => {
        return {
          [key.toLowerCase()]: {
            count,
            percentageChange,
          },
        };
      },
    );

    formattedData?.sort((a, b) => {
      const keyA = Object.keys(a)[0];
      const keyB = Object.keys(b)[0];
      return b[keyB].count - a[keyA].count;
    });

    return new SuccessResponseDto({ data: formattedData });
  }

  @httpGet(
    '/streams/analytics',
    autoValidate(GetStreamEngagementQueryDto.validationSchema, 'query'),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([
      UserCategory.CREATOR,
      UserCategory.COMMUNITY_OWNER,
      UserCategory.TEAM_MEMBER,
    ]),
    connectedArtistAccount,
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
  )
  public async getStream(
    @request() { claim }: RequestWithClaims,
    @queryParam() { platform, ...range }: GetStreamEngagementQueryDto,
    @queryParam() { user_id }: OptionalIdFiltersDto,
  ) {
    const vibrateIds = await this.fetchVibrateIds(claim, user_id);

    if (vibrateIds.length === 0) {
      return new SuccessResponseDto({ data: null });
    }

    const today = new Date();

    const { from, to } = range;

    const channelStreamPath = ChannelStreamPath[platform.toUpperCase()];

    const allStreamAnalyticsData = await Promise.all(
      vibrateIds.map((vibrateId) =>
        this.vibrate
          .getStreamData({
            channel: platform as string as MusicChannels,
            uuid: vibrateId,
            channelStreamPath,
            query: {
              from: format(from ?? subMonths(today, 1), 'yyyy-MM-dd'),
              to: format(to ?? today, 'yyyy-MM-dd'),
            },
          })
          .then((res) => res?.data?.data),
      ),
    );

    const combinedStreamAnalyticsData = allStreamAnalyticsData?.reduce(
      (acc, curr) => {
        Object.entries(curr).forEach(([date, value]) => {
          acc[date] = (acc[date] || 0) + (value as number);
        });
        return acc;
      },
      {} as Record<string, number>,
    );

    const dates = Object.keys(combinedStreamAnalyticsData).sort();
    if (dates.length === 0) {
      return new SuccessResponseDto({
        data: {
          analytics: {},
          week: { amount: 0, percentage: 0, daily: 0 },
          month: { amount: 0, percentage: 0, daily: 0 },
          threeMonths: { amount: 0, percentage: 0, daily: 0 },
        },
      });
    }

    let weekData = { amount: 0, percentage: 0, daily: 0 };
    if (dates.length >= 2) {
      const currentWeekValue =
        combinedStreamAnalyticsData[dates[dates.length - 1]];
      const previousWeekValue =
        combinedStreamAnalyticsData[dates[dates.length - 2]];
      const weekPercentage = calculatePercentageChange(
        currentWeekValue,
        previousWeekValue,
      );
      weekData = {
        amount: Math.round(currentWeekValue),
        percentage: weekPercentage,
        daily: Math.round(currentWeekValue / 7),
      };
    } else {
      const currentWeekValue =
        combinedStreamAnalyticsData[dates[dates.length - 1]];
      weekData = {
        amount: Math.round(currentWeekValue),
        percentage: 0,
        daily: Math.round(currentWeekValue / 7),
      };
    }

    let monthData = { amount: 0, percentage: 0, daily: 0 };
    if (dates.length >= 4) {
      const currentMonthPoints = dates.slice(-4);
      const currentMonthSum = currentMonthPoints.reduce(
        (sum, date) => sum + combinedStreamAnalyticsData[date],
        0,
      );
      const currentMonthAvg = currentMonthSum / currentMonthPoints.length;
      let prevMonthAvg = 0;
      if (dates.length >= 8) {
        const prevMonthPoints = dates.slice(-8, -4);
        const prevMonthSum = prevMonthPoints.reduce(
          (sum, date) => sum + combinedStreamAnalyticsData[date],
          0,
        );
        prevMonthAvg = prevMonthSum / prevMonthPoints.length;
      }
      const monthPercentage = calculatePercentageChange(
        currentMonthAvg,
        prevMonthAvg,
      );
      monthData = {
        amount: Math.round(currentMonthAvg),
        percentage: monthPercentage,
        daily: Math.round(currentMonthAvg / 7),
      };
    }

    let threeMonthsData = { amount: 0, percentage: 0, daily: 0 };
    if (dates.length >= 12) {
      const currentThreeMonthPoints = dates.slice(-12);
      const currentThreeMonthSum = currentThreeMonthPoints.reduce(
        (sum, date) => sum + combinedStreamAnalyticsData[date],
        0,
      );
      const currentThreeMonthAvg =
        currentThreeMonthSum / currentThreeMonthPoints.length;
      let prevThreeMonthAvg = 0;
      if (dates.length >= 24) {
        const prevThreeMonthPoints = dates.slice(-24, -12);
        const prevThreeMonthSum = prevThreeMonthPoints.reduce(
          (sum, date) => sum + combinedStreamAnalyticsData[date],
          0,
        );
        prevThreeMonthAvg = prevThreeMonthSum / prevThreeMonthPoints.length;
      }
      const threeMonthsPercentage = calculatePercentageChange(
        currentThreeMonthAvg,
        prevThreeMonthAvg,
      );
      threeMonthsData = {
        amount: Math.round(currentThreeMonthAvg),
        percentage: threeMonthsPercentage,
        daily: Math.round(currentThreeMonthAvg / 7),
      };
    }

    return new SuccessResponseDto({
      data: {
        analytics: combinedStreamAnalyticsData,
        week: weekData,
        month: monthData,
        threeMonths: threeMonthsData,
      },
    });
  }

  @httpGet(
    '/social',
    autoValidate(GetSocialEngagementQueryDto.validationSchema, 'query'),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([
      UserCategory.CREATOR,
      UserCategory.COMMUNITY_OWNER,
      UserCategory.TEAM_MEMBER,
    ]),
    connectedArtistAccount,
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
  )
  public async getSocialEngagementMetrics(
    @request() { claim }: RequestWithClaims,
    @queryParam() { platform, ...range }: GetSocialEngagementQueryDto,
    @queryParam() { user_id }: OptionalIdFiltersDto,
  ) {
    const vibrateIds = await this.fetchVibrateIds(claim, user_id);

    const allEngagements = await Promise.all(
      vibrateIds.map((vibrateId) =>
        this.vibrate
          .getSocialEngagement({
            uuid: vibrateId,
            platform,
            query: Object.keys(range).length > 0 ? range : null,
          })
          .then((res) => res?.data?.data),
      ),
    );

    const combinedEngagements = allEngagements.reduce((acc, curr) => {
      for (const [key, value] of Object.entries(curr)) {
        acc[key] = (acc[key] || 0) + (value as number);
      }
      return acc;
    }, {} as Record<string, number>);

    const total = Object.values(combinedEngagements).reduce(
      (sum, value) => +sum + +value,
      0,
    );

    return new SuccessResponseDto({
      data: { total_likes: total, engagements: combinedEngagements },
    });
  }

  @httpGet(
    '/playlist',
    autoValidate(GetActivePlaylistQueryDto.validationSchema, 'query'),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([
      UserCategory.CREATOR,
      UserCategory.COMMUNITY_OWNER,
      UserCategory.TEAM_MEMBER,
    ]),
    connectedArtistAccount,
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
  )
  public async getPlaylists(
    @request() { claim }: RequestWithClaims,
    @queryParam() { platform, ...range }: GetActivePlaylistQueryDto,
    @queryParam() { user_id }: OptionalIdFiltersDto,
  ) {
    const vibrateIds = await this.fetchVibrateIds(claim, user_id);

    const allPlaylistsData = await Promise.all(
      vibrateIds.map((vibrateId) =>
        this.vibrate
          .getActivePlaylist({
            uuid: vibrateId,
            platform,
            query: Object.keys(range).length > 0 ? range : null,
          })
          .then((res) => res?.data?.data),
      ),
    );

    const aggregation: any[] = [];

    for (const datapoint of allPlaylistsData) {
      for (const { uuid, ...rest } of datapoint) {
        aggregation.push({ track_id: uuid, ...rest });
      }
    }

    if (vibrateIds.length > 1) {
      aggregation.sort((a, b) => b.active_playlists - a.active_playlists);
    }

    return new SuccessResponseDto({ data: aggregation });
  }

  @httpGet(
    '/playlist/reach',
    autoValidate(GetPlaylistReachQueryDto.validationSchema, 'query'),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([
      UserCategory.CREATOR,
      UserCategory.COMMUNITY_OWNER,
      UserCategory.TEAM_MEMBER,
    ]),
    connectedArtistAccount,
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
  )
  public async getPlaylistReach(
    @request() { claim }: RequestWithClaims,
    @queryParam() { platform, ...range }: GetPlaylistReachQueryDto,
    @queryParam() { user_id }: OptionalIdFiltersDto,
  ) {
    const vibrateIds = await this.fetchVibrateIds(claim, user_id);

    const allReachData = await Promise.all(
      vibrateIds.map((vibrateId) =>
        this.vibrate
          .getPlaylistReach({
            uuid: vibrateId,
            platform,
            query: Object.keys(range).length > 0 ? range : null,
          })
          .then((res) => res?.data?.data),
      ),
    );

    const combinedReachData = allReachData?.reduce((acc, curr) => {
      for (const [date, value] of Object.entries(curr)) {
        if (!acc[date]) acc[date] = 0;
        acc[date] += value as number;
      }
      return acc;
    }, {} as Record<string, number>);

    return new SuccessResponseDto({ data: combinedReachData });
  }

  @httpGet(
    '/location/heatmap',
    autoValidate(GetEngagementInLocationQueryDto.validationSchema, 'query'),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([
      UserCategory.CREATOR,
      UserCategory.COMMUNITY_OWNER,
      UserCategory.TEAM_MEMBER,
    ]),
    connectedArtistAccount,
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
  )
  public async getEngagementsInLocations(
    @request() { claim }: RequestWithClaims,
    @queryParam() { platform }: GetEngagementInLocationQueryDto,
    @queryParam() { user_id }: OptionalIdFiltersDto,
  ) {
    const vibrateIds = await this.fetchVibrateIds(claim, user_id);

    const allLocationData = await Promise.all(
      vibrateIds.map((vibrateId) =>
        this.vibrate
          .getMetricsInLocation({
            uuid: vibrateId,
            platform,
          })
          .then((res) => res?.data?.data),
      ),
    );

    const combinedLocationData = allLocationData?.reduce((acc, data) => {
      let result: Record<string, number> = {};

      switch (platform) {
        case VibrateChannels.AIRPLAY:
          result = reduce(
            data,
            (accumulator, val, key) => {
              accumulator[key] = (accumulator[key] || 0) + val['spins'];
              return accumulator;
            },
            {} as Record<string, number>,
          );
          break;
        case VibrateChannels.SPOTIFY:
          result = data['by-country'];
          break;
        case VibrateChannels.YOUTUBE:
          result = reduce(
            data['by-country'],
            (accumulator, val, key) => {
              accumulator[key] = (accumulator[key] || 0) + val['views_1m'];
              return accumulator;
            },
            {} as Record<string, number>,
          );
          break;
      }

      for (const [key, value] of Object.entries(result)) {
        acc[key] = (acc[key] || 0) + value;
      }

      return acc;
    }, {} as Record<string, number>);

    return new SuccessResponseDto({ data: combinedLocationData });
  }

  @httpGet(
    '/playlist/top',
    autoValidate(GetTopPlaylistsQueryDto.validationSchema, 'query'),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([
      UserCategory.CREATOR,
      UserCategory.COMMUNITY_OWNER,
      UserCategory.TEAM_MEMBER,
    ]),
    connectedArtistAccount,
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
  )
  public async getTopPlaylists(
    @request() { claim }: RequestWithClaims,
    @queryParam() { platform, ...paginationQuery }: GetTopPlaylistsQueryDto,
    @queryParam() { user_id }: OptionalIdFiltersDto,
  ) {
    const vibrateIds = await this.fetchVibrateIds(claim, user_id);

    const allPlaylistsData = await Promise.all(
      vibrateIds.map((vibrateId) =>
        this.vibrate
          .getTopPlaylists({
            uuid: vibrateId,
            platform,
            query:
              Object.keys(paginationQuery).length > 0 ? paginationQuery : null,
          })
          .then(({ data, pagination }) => ({
            ...pagination,
            result: data?.data,
          })),
      ),
    );

    const combinedPlaylistsData = allPlaylistsData?.reduce(
      (acc, curr) => {
        acc.result = [...acc.result, ...curr.result];
        acc.total += curr.total;
        return acc;
      },
      { result: [], total: 0 },
    );

    // Sort and limit the combined results
    combinedPlaylistsData?.result?.sort((a, b) => b.followers - a.followers);
    combinedPlaylistsData.result = combinedPlaylistsData?.result.slice(
      0,
      paginationQuery.limit || 10,
    );

    return new SuccessResponseDto({ data: combinedPlaylistsData });
  }

  @httpGet(
    '/tracks/top',
    autoValidate(GetTopTracksQueryDto.validationSchema, 'query'),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([
      UserCategory.CREATOR,
      UserCategory.COMMUNITY_OWNER,
      UserCategory.TEAM_MEMBER,
    ]),
    connectedArtistAccount,
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
  )
  public async getTopTracks(
    @request() { claim }: RequestWithClaims,
    @queryParam() { platform, ...range }: GetTopTracksQueryDto,
    @queryParam() { user_id }: OptionalIdFiltersDto,
  ) {
    const vibrateIds = await this.fetchVibrateIds(claim, user_id);

    const allTracksData = await Promise.all(
      vibrateIds.map((vibrateId) =>
        this.vibrate
          .getTopTracks({
            uuid: vibrateId,
            platform,
            query: Object.keys(range).length > 0 ? range : null,
          })
          .then((res) => res?.data?.data),
      ),
    );

    const combinedTracksData = allTracksData?.reduce((acc, curr) => {
      for (const track of curr) {
        const existingTrack = acc.find((t) => t.id === track.id);
        if (existingTrack) {
          existingTrack.streams += track.streams;
        } else {
          acc.push({ ...track });
        }
      }
      return acc;
    }, []);

    combinedTracksData?.sort((a, b) => b.streams - a.streams);

    return new SuccessResponseDto({ data: combinedTracksData });
  }

  @httpGet(
    '/playlisted-tracks/top',
    autoValidate(GetTopPlaylistedTracksQueryDto.validationSchema, 'query'),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([
      UserCategory.CREATOR,
      UserCategory.COMMUNITY_OWNER,
      UserCategory.TEAM_MEMBER,
    ]),
    connectedArtistAccount,
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
  )
  public async getTopPlaylistedTracks(
    @request() { claim }: RequestWithClaims,
    @queryParam() { user_id }: OptionalIdFiltersDto,
    @queryParam()
    { platform, ...paginationQuery }: GetTopPlaylistedTracksQueryDto,
  ) {
    const vibrateIds = await this.fetchVibrateIds(claim, user_id);

    if (vibrateIds.length > 1 && Object.keys(paginationQuery).length > 0) {
      delete paginationQuery.offset;
      delete paginationQuery.limit;
    }

    const allPlaylistedTracksData = await Promise.all(
      vibrateIds.map((vibrateId) =>
        this.vibrate.getTopPlaylistTracks({
          uuid: vibrateId,
          platform,
          query:
            Object.keys(paginationQuery).length > 0 ? paginationQuery : null,
        }),
      ),
    );

    const aggregation = { total: 0, result: [] };

    for (const { data, pagination } of allPlaylistedTracksData) {
      aggregation.total += pagination.total;
      aggregation.result.push(...data?.data);
    }

    if (vibrateIds.length > 0) {
      aggregation.result.sort(
        (a, b) =>
          b[paginationQuery.sort ?? 'playlist_reach'] -
          a[paginationQuery.sort ?? 'playlist_reach'],
      );

      aggregation.result = aggregation.result.slice(
        paginationQuery.offset ?? 0,
        paginationQuery.limit ?? 10,
      );
    }

    return new SuccessResponseDto({ data: aggregation });
  }

  @httpGet(
    '/videos',
    autoValidate(GetVideosQueryDto.validationSchema, 'query'),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([
      UserCategory.CREATOR,
      UserCategory.COMMUNITY_OWNER,
      UserCategory.TEAM_MEMBER,
    ]),
    connectedArtistAccount,
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
  )
  public async getVideos(
    @request() { claim }: RequestWithClaims,
    @queryParam() { user_id }: OptionalIdFiltersDto,
    @queryParam() { platform, ...paginationQuery }: GetVideosQueryDto,
  ) {
    const vibrateIds = await this.fetchVibrateIds(claim, user_id);

    if (vibrateIds.length > 1 && Object.keys(paginationQuery).length > 0) {
      delete paginationQuery.offset;
      delete paginationQuery.limit;
    }

    const allVideosData = await Promise.all(
      vibrateIds.map((vibrateId) =>
        this.vibrate.getVideos({
          uuid: vibrateId,
          platform,
          query:
            Object.keys(paginationQuery).length > 0 ? paginationQuery : null,
        }),
      ),
    );

    const aggregation = { total: 0, result: [] };

    for (const { data, pagination } of allVideosData) {
      aggregation.total += pagination.total;
      aggregation.result.push(...data?.data);
    }

    if (vibrateIds.length > 0) {
      aggregation.result.sort((a, b) => b['views_1w'] - a['views_1w']);

      aggregation.result = aggregation.result.slice(
        paginationQuery.offset ?? 0,
        paginationQuery.limit ?? 10,
      );
    }

    return new SuccessResponseDto({ data: aggregation });
  }

  @httpGet(
    '/stats',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
    inCategory([
      UserCategory.CREATOR,
      UserCategory.COMMUNITY_OWNER,
      UserCategory.TEAM_MEMBER,
    ]),
    connectedArtistAccount,
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
  )
  public async engagementStats(
    @request() { claim }: RequestWithClaims,
    @queryParam() { user_id }: OptionalIdFiltersDto,
  ) {
    const currentDate = new Date();
    currentDate.setHours(0, 0, 0, 0);

    const currentMonthStart = startOfMonth(currentDate);
    const currentMonthEnd = endOfMonth(currentDate);

    const previousMonthStart = startOfMonth(subMonths(currentDate, 1));
    const previousMonthEnd = endOfMonth(subMonths(currentDate, 1));

    const query = {
      from: format(previousMonthStart, 'yyyy-MM-dd'),
      to: format(currentMonthEnd, 'yyyy-MM-dd'),
    };

    const vibrateIds = await this.fetchVibrateIds(claim, user_id);

    if (vibrateIds.length === 0) {
      return new SuccessResponseDto({ data: null });
    }

    const combinedListeners: Record<string, number> = {};
    const combinedFollowers: Record<string, number> = {};
    const combinedStreams: Record<string, number> = {};
    const combinedViews: Record<string, number> = {};
    const combinedSubscribers: Record<string, number> = {};

    for (const vibrateId of vibrateIds) {
      const [
        spotifyListenersResponse,
        spotifyFollowersResponse,
        spotifyStreamsResponse,
        youtubeViewsResponse,
        youtubeSubscribersResponse,
      ] = await Promise.all([
        this.vibrate.getSpotifyListeners({ uuid: vibrateId, query }),
        this.vibrate.getFanBaseData({
          channel: VibrateChannels.SPOTIFY,
          uuid: vibrateId,
          query,
        }),
        this.vibrate.getStreamData({
          channel: MusicChannels.SPOTIFY,
          channelStreamPath: ChannelStreamPath.SPOTIFY,
          uuid: vibrateId,
          query: {
            from: format(previousMonthStart, 'yyyy-MM-dd'),
            to: format(previousMonthEnd, 'yyyy-MM-dd'),
          },
        }),
        this.vibrate.getStreamData({
          channel: MusicChannels.YOUTUBE,
          channelStreamPath: ChannelStreamPath.YOUTUBE,
          uuid: vibrateId,
          query: {
            from: format(previousMonthStart, 'yyyy-MM-dd'),
            to: format(previousMonthEnd, 'yyyy-MM-dd'),
          },
        }),
        this.vibrate.getFanBaseData({
          channel: VibrateChannels.YOUTUBE,
          uuid: vibrateId,
          query,
        }),
      ]);

      const listeners = spotifyListenersResponse.data?.data || {};
      const followers = spotifyFollowersResponse.data?.data || {};
      const streams = spotifyStreamsResponse.data?.data || {};
      const views = youtubeViewsResponse.data?.data || {};
      const subscribers = youtubeSubscribersResponse.data?.data || {};

      for (const [date, value] of Object.entries(listeners)) {
        combinedListeners[date] = (combinedListeners[date] || 0) + +value;
      }
      for (const [date, value] of Object.entries(followers)) {
        combinedFollowers[date] = (combinedFollowers[date] || 0) + +value;
      }
      for (const [date, value] of Object.entries(streams)) {
        combinedStreams[date] = (combinedStreams[date] || 0) + +value;
      }
      for (const [date, value] of Object.entries(views)) {
        combinedViews[date] = (combinedViews[date] || 0) + +value;
      }
      for (const [date, value] of Object.entries(subscribers)) {
        combinedSubscribers[date] = (combinedSubscribers[date] || 0) + +value;
      }
    }

    const sumValuesInRange = (
      data: Record<string, number>,
      startDate: Date,
      endDate: Date,
    ): { sum: number; count: number } => {
      let sum = 0;
      let count = 0;

      for (const [dateStr, value] of Object.entries(data)) {
        const date = parseISO(dateStr);
        if (isWithinInterval(date, { start: startDate, end: endDate })) {
          sum += value;
          count++;
        }
      }

      return { sum, count };
    };

    // Calculate averages for current month
    const { sum: currentListenersSum, count: currentListenersCount } =
      sumValuesInRange(combinedListeners, currentMonthStart, currentMonthEnd);
    let currentMonthListeners =
      currentListenersCount > 0
        ? currentListenersSum / currentListenersCount
        : 0;

    const { sum: currentFollowersSum, count: currentFollowersCount } =
      sumValuesInRange(combinedFollowers, currentMonthStart, currentMonthEnd);
    let currentMonthFollowers =
      currentFollowersCount > 0
        ? currentFollowersSum / currentFollowersCount
        : 0;

    const { sum: currentStreamsSum, count: currentStreamsCount } =
      sumValuesInRange(combinedStreams, currentMonthStart, currentMonthEnd);
    let currentMonthStreams =
      currentStreamsCount > 0 ? currentStreamsSum / currentStreamsCount : 0;

    const { sum: currentViewsSum, count: currentViewsCount } = sumValuesInRange(
      combinedViews,
      currentMonthStart,
      currentMonthEnd,
    );
    let currentMonthViews =
      currentViewsCount > 0 ? currentViewsSum / currentViewsCount : 0;

    const { sum: currentSubscribersSum, count: currentSubscribersCount } =
      sumValuesInRange(combinedSubscribers, currentMonthStart, currentMonthEnd);
    let currentMonthSubscribers =
      currentSubscribersCount > 0
        ? currentSubscribersSum / currentSubscribersCount
        : 0;

    // Calculate averages for previous month
    const { sum: previousListenersSum, count: previousListenersCount } =
      sumValuesInRange(combinedListeners, previousMonthStart, previousMonthEnd);
    const previousMonthListeners =
      previousListenersCount > 0
        ? previousListenersSum / previousListenersCount
        : 0;

    const { sum: previousFollowersSum, count: previousFollowersCount } =
      sumValuesInRange(combinedFollowers, previousMonthStart, previousMonthEnd);
    const previousMonthFollowers =
      previousFollowersCount > 0
        ? previousFollowersSum / previousFollowersCount
        : 0;

    const { sum: previousStreamsSum, count: previousStreamsCount } =
      sumValuesInRange(combinedStreams, previousMonthStart, previousMonthEnd);
    const previousMonthStreams =
      previousStreamsCount > 0 ? previousStreamsSum / previousStreamsCount : 0;

    const { sum: previousViewsSum, count: previousViewsCount } =
      sumValuesInRange(combinedViews, previousMonthStart, previousMonthEnd);
    const previousMonthViews =
      previousViewsCount > 0 ? previousViewsSum / previousViewsCount : 0;

    const { sum: previousSubscribersSum, count: previousSubscribersCount } =
      sumValuesInRange(
        combinedSubscribers,
        previousMonthStart,
        previousMonthEnd,
      );
    const previousMonthSubscribers =
      previousSubscribersCount > 0
        ? previousSubscribersSum / previousSubscribersCount
        : 0;

    let listenerPercentageChange = calculatePercentageChange(
      currentMonthListeners,
      previousMonthListeners,
    );
    let viewsPercentageChange = calculatePercentageChange(
      currentMonthViews,
      previousMonthViews,
    );

    // If current month has no data, use previous month's data and set percentage change to 0
    if (currentListenersCount === 0 && previousListenersCount > 0) {
      currentMonthListeners = previousMonthListeners;
      listenerPercentageChange = 0;
    }

    if (currentFollowersCount === 0 && previousFollowersCount > 0) {
      currentMonthFollowers = previousMonthFollowers;
    }

    if (currentStreamsCount === 0 && previousStreamsCount > 0) {
      currentMonthStreams = previousMonthStreams;
    }

    if (currentViewsCount === 0 && previousViewsCount > 0) {
      currentMonthViews = previousMonthViews;
      viewsPercentageChange = 0;
    }

    if (currentSubscribersCount === 0 && previousSubscribersCount > 0) {
      currentMonthSubscribers = previousMonthSubscribers;
    }

    const currentMonthListenerPerFollower =
      currentMonthFollowers !== 0
        ? currentMonthListeners / currentMonthFollowers
        : 0;
    const previousMonthListenerPerFollower =
      previousMonthFollowers !== 0
        ? previousMonthListeners / previousMonthFollowers
        : 0;
    const listenerPerFollowerPercentageChange = calculatePercentageChange(
      currentMonthListenerPerFollower,
      previousMonthListenerPerFollower,
    );

    const currentMonthStreamsPerListener =
      currentMonthListeners !== 0
        ? currentMonthStreams / currentMonthListeners
        : 0;
    const previousMonthStreamsPerListener =
      previousMonthListeners !== 0
        ? previousMonthStreams / previousMonthListeners
        : 0;
    const streamsPerListenerPercentageChange = calculatePercentageChange(
      currentMonthStreamsPerListener,
      previousMonthStreamsPerListener,
    );

    const currentMonthViewsPerSubscriber =
      currentMonthSubscribers !== 0
        ? currentMonthViews / currentMonthSubscribers
        : 0;
    const previousMonthViewsPerSubscriber =
      previousMonthSubscribers !== 0
        ? previousMonthViews / previousMonthSubscribers
        : 0;
    const viewsPerSubscriberPercentageChange = calculatePercentageChange(
      currentMonthViewsPerSubscriber,
      previousMonthViewsPerSubscriber,
    );

    const responseData = {
      spotify_monthly_listeners: {
        amount: Math.round(currentMonthListeners),
        percentage_change: listenerPercentageChange,
      },
      spotify_listener_per_follower: {
        amount: parseFloat(currentMonthListenerPerFollower.toFixed(2)),
        percentage_change: listenerPerFollowerPercentageChange,
      },
      spotify_streams_per_listener: {
        amount: parseFloat(currentMonthStreamsPerListener.toFixed(2)),
        percentage_change: streamsPerListenerPercentageChange,
      },
      youtube_monthly_views: {
        amount: Math.round(currentMonthViews),
        percentage_change: viewsPercentageChange,
      },
      youtube_views_per_subscriber: {
        amount: parseFloat(currentMonthViewsPerSubscriber.toFixed(2)),
        percentage_change: viewsPerSubscriberPercentageChange,
      },
    };

    return new SuccessResponseDto({ data: responseData });
  }

  private async simplifyProxyQuery(claim: Claim, user_id?: string) {
    const roleDesc = await this.user.describe(claim.team_id ?? claim.id);

    const isProxyRequest =
      user_id && (claim.admin_auth || roleDesc?.community_level_user);

    let userDesc: UserDescription;

    if (isProxyRequest) {
      userDesc = await this.user.describe(user_id);

      if (
        !userDesc ||
        (!claim.admin_auth && userDesc.community_id !== claim.community_id)
      ) {
        throw new ApplicationError(StatusCodes.NOT_FOUND, 'User not found');
      }
    } else if (!isProxyRequest && claim.admin_auth) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        "'user_id' is required",
      );
    } else {
      userDesc = roleDesc;
    }

    return userDesc;
  }
}
