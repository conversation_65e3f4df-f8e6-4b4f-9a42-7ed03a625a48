import { SuccessResponseDto } from '@app/http/dtos/http-response.dto';
import { inCategory } from '@app/http/middlewares/user-category.middleware';
import {
  MIDDLEWARE_TOKENS,
  MODULE_TOKENS,
  SERVICE_TOKENS,
} from '@app/internal/ioc/tokens';
import { Claim, RequestWithClaims } from '@app/internal/types';
import { VibrateChannels, VibrateClient } from '@app/modules/vibrate';
import { UserCategory } from '@app/services/user/entities/user.entity';
import { inject } from 'inversify';
import {
  controller,
  httpGet,
  queryParam,
  request as httpReq,
} from 'inversify-express-utils';
import { AnalyticsDto } from './dto/analytics.dto';
import {
  AgeData,
  DemographicData,
  DemographicDto,
  GenderData,
} from './dto/demographic.dto';
import { CountryAudienceDto } from './dto/country-audience.dto';
import countries from '@app/misc/countries.json';
import cities from '@app/misc/cities.json';
import { connectedArtistAccount } from '@app/http/middlewares/account.middleware';
import { format, subMonths } from 'date-fns';
import { autoValidate } from '@app/http/middlewares/validation.middleware';
import { ApplicationError } from '@app/internal/errors';
import { StatusCodes } from 'http-status-codes';
import { UserDescription, UserService } from '@app/services/user/user.service';
import { hasPermissions } from '@app/http/middlewares/permissions.middleware';
import { PermissionResource } from '@app/utils/permissions.utils';
import { calculatePercentageChange } from '@app/utils/fanbase.utils';
import { calculateGrowth } from '@app/utils/analytics-growth.utils';
import { OptionalIdFiltersDto } from '@app/http/dtos/id.dto';

@controller('/fanbase')
export class FanbaseController {
  private readonly fanbase = {
    demographic: ['instagram', 'youtube', 'tiktok'],
    analytics: [
      'spotify',
      'soundcloud',
      'youtube',
      'deezer',
      'facebook',
      'instagram',
      'twitter',
      'tiktok',
    ],
    countryAudience: ['instagram', 'spotify', 'tiktok', 'youtube'],
    cityAudience: ['instagram', 'spotify', 'youtube'],
    streams: ['spotify', 'soundcloud', 'youtube'],
  };

  constructor(
    @inject(MODULE_TOKENS.Vibrate) private readonly vibrate: VibrateClient,
    @inject(SERVICE_TOKENS.UserService)
    private readonly user: UserService,
  ) {}

  private async fetchVibrateIds(
    claim: Claim,
    user_id?: string,
  ): Promise<string[]> {
    const roleDesc = await this.simplifyProxyQuery(claim, user_id);

    if (roleDesc.community_level_user) {
      const communityUsers = await this.user.fetchVibrateUsers(
        roleDesc.community_id,
      );

      return communityUsers.map((user) => user.vibrate_id);
    } else if (roleDesc.vibrate_id) return [roleDesc.vibrate_id];

    return [];
  }

  @httpGet(
    '/rank',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([
      UserCategory.COMMUNITY_OWNER,
      UserCategory.CREATOR,
      UserCategory.TEAM_MEMBER,
    ]),
    connectedArtistAccount,
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
  )
  public async rank(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { user_id }: OptionalIdFiltersDto,
  ) {
    const [vibrate_id] = await this.fetchVibrateIds(claim, user_id);

    if (claim.team_role === 'owner') {
      const data = await this.vibrate.getFanbaseRank(vibrate_id);
      const rankData = data?.data?.data;

      return new SuccessResponseDto({ data: rankData });
    }

    return new SuccessResponseDto();
  }

  @httpGet(
    '/total',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([
      UserCategory.COMMUNITY_OWNER,
      UserCategory.CREATOR,
      UserCategory.TEAM_MEMBER,
    ]),
    connectedArtistAccount,
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
  )
  public async totalFanbase(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { user_id }: OptionalIdFiltersDto,
  ) {
    const vibrateIds = await this.fetchVibrateIds(claim, user_id);
    const platforms: Array<
      Record<string, { count: number; percentageIncrease: number }>
    > = [];
    const defaultPlatforms = [
      { spotify: { count: 0, percentageIncrease: 0 } },
      { soundcloud: { count: 0, percentageIncrease: 0 } },
      { youtube: { count: 0, percentageIncrease: 0 } },
      { deezer: { count: 0, percentageIncrease: 0 } },
      { facebook: { count: 0, percentageIncrease: 0 } },
      { instagram: { count: 0, percentageIncrease: 0 } },
      { twitter: { count: 0, percentageIncrease: 0 } },
      { tiktok: { count: 0, percentageIncrease: 0 } },
    ];

    if (vibrateIds.length === 0) {
      return new SuccessResponseDto({ data: defaultPlatforms });
    }

    const fanbaseAnalytics = this.fanbase.analytics as VibrateChannels[];

    const fanbasePromises = fanbaseAnalytics.map(async (platform) => {
      let totalCurrentCount = 0;
      let totalLastMonthCount = 0;

      for (const vibrateId of vibrateIds) {
        const data = await this.vibrate.getFanBaseData({
          channel: platform,
          uuid: vibrateId,
        });

        const historicData = data?.data?.data;
        const dates = Object.keys(historicData).sort();
        if (dates.length === 0) continue;

        // Get current count from the latest date
        const currentCount = historicData[dates[dates.length - 1]] || 0;

        // Calculate the date 30 days ago from the latest date
        const latestDate = new Date(dates[dates.length - 1]);
        const thirtyDaysAgo = new Date(latestDate);
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

        // Find the closest date to 30 days ago
        const lastMonthDate = dates.reduce((closest, date) => {
          const currentDiff = Math.abs(
            new Date(date).getTime() - thirtyDaysAgo.getTime(),
          );
          const closestDiff = Math.abs(
            new Date(closest).getTime() - thirtyDaysAgo.getTime(),
          );
          return currentDiff < closestDiff ? date : closest;
        }, dates[0]);

        const lastMonthCount = historicData[lastMonthDate] || currentCount;

        totalCurrentCount += currentCount;
        totalLastMonthCount += lastMonthCount;
      }

      const percentageIncrease =
        totalLastMonthCount > 0
          ? ((totalCurrentCount - totalLastMonthCount) / totalLastMonthCount) *
            100
          : 0;

      return {
        [platform]: {
          count: totalCurrentCount,
          percentageIncrease: Math.round(percentageIncrease * 100) / 100,
        },
      };
    });

    const resolvedPlatforms = await Promise.all(fanbasePromises);
    platforms.push(...resolvedPlatforms);

    const sortedPlatforms = platforms.sort((a, b) => {
      const keyA = Object.keys(a)[0];
      const keyB = Object.keys(b)[0];
      return b[keyB].count - a[keyA].count;
    });

    return new SuccessResponseDto({ data: sortedPlatforms });
  }

  @httpGet(
    '/analytics',
    autoValidate(AnalyticsDto.validationSchema, 'query'),
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([
      UserCategory.CREATOR,
      UserCategory.COMMUNITY_OWNER,
      UserCategory.TEAM_MEMBER,
    ]),
    connectedArtistAccount,
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
  )
  public async analytics(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() query: AnalyticsDto,
    @queryParam() { user_id }: OptionalIdFiltersDto,
  ): Promise<SuccessResponseDto> {
    const vibrateIds = await this.fetchVibrateIds(claim, user_id);

    const platforms = Array.isArray(query.platforms)
      ? query.platforms
      : [query.platforms];

    if (vibrateIds.length === 0) {
      return new SuccessResponseDto({
        data: {
          analytics: {},
          week: { amount: 0, percentage: 0, daily: 0 },
          month: { amount: 0, percentage: 0, daily: 0 },
          threeMonths: { amount: 0, percentage: 0, daily: 0 },
        },
      });
    }

    const currentDate = new Date();
    currentDate.setHours(0, 0, 0, 0);

    const fromDate = query.from
      ? new Date(query.from)
      : subMonths(currentDate, 6);
    const toDate = query.to ? new Date(query.to) : currentDate;

    const vibrateQuery = {
      from: format(fromDate, 'yyyy-MM-dd'),
      to: format(toDate, 'yyyy-MM-dd'),
    };

    const fetchAnalytics = async (userVibrateId: string) => {
      return Promise.all(
        platforms.map((platform) =>
          this.vibrate.getFanBaseData({
            channel: platform,
            uuid: userVibrateId,
            query: vibrateQuery,
          }),
        ),
      );
    };

    const analyticsDataArray = await Promise.all(
      vibrateIds.map((vibrateId) => fetchAnalytics(vibrateId)),
    );

    const analyticsData = analyticsDataArray.flat();

    const analyticsResult = analyticsData?.reduce((acc, data) => {
      Object.entries(data?.data?.data).forEach(([key, value]) => {
        acc[key] = (acc[key] || 0) + value;
      });
      return acc;
    }, {} as Record<string, number>);

    const dates = Object.keys(analyticsResult).sort();
    if (dates.length === 0) {
      return new SuccessResponseDto({
        data: {
          analytics: {},
          week: { amount: 0, percentage: 0, daily: 0 },
          month: { amount: 0, percentage: 0, daily: 0 },
          threeMonths: { amount: 0, percentage: 0, daily: 0 },
        },
      });
    }

    const lastIndex = dates.length - 1;
    const weekGrowth = calculateGrowth(
      analyticsResult,
      dates,
      lastIndex,
      lastIndex - 1,
    );

    const prevWeekGrowth = calculateGrowth(
      analyticsResult,
      dates,
      lastIndex - 1,
      lastIndex - 2,
    );

    const weekData = {
      amount: weekGrowth,
      percentage: calculatePercentageChange(weekGrowth, prevWeekGrowth),
      daily: weekGrowth / 7,
    };

    const currentMonthStart = new Date(dates[lastIndex]);
    currentMonthStart.setDate(1);
    const monthStartIndex = dates.findIndex(
      (date) => new Date(date) >= currentMonthStart,
    );

    const monthGrowth = calculateGrowth(
      analyticsResult,
      dates,
      lastIndex,
      monthStartIndex,
    );

    const prevMonthStart = new Date(currentMonthStart);
    prevMonthStart.setMonth(prevMonthStart.getMonth() - 1);
    const prevMonthStartIndex = dates.findIndex(
      (date) => new Date(date) >= prevMonthStart,
    );

    const prevMonthGrowth = calculateGrowth(
      analyticsResult,
      dates,
      monthStartIndex - 1,
      prevMonthStartIndex,
    );

    const daysInMonth = new Date(
      currentMonthStart.getFullYear(),
      currentMonthStart.getMonth() + 1,
      0,
    ).getDate();

    const monthData = {
      amount: monthGrowth,
      percentage: calculatePercentageChange(monthGrowth, prevMonthGrowth),
      daily: monthGrowth / daysInMonth,
    };

    const threeMonthsStart = new Date(dates[lastIndex]);
    threeMonthsStart.setMonth(threeMonthsStart.getMonth() - 2);
    threeMonthsStart.setDate(1);
    const threeMonthsStartIndex = dates.findIndex(
      (date) => new Date(date) >= threeMonthsStart,
    );

    const threeMonthsGrowth = calculateGrowth(
      analyticsResult,
      dates,
      lastIndex,
      threeMonthsStartIndex,
    );

    const prevThreeMonthsStart = new Date(threeMonthsStart);
    prevThreeMonthsStart.setMonth(prevThreeMonthsStart.getMonth() - 3);
    const prevThreeMonthsStartIndex = dates.findIndex(
      (date) => new Date(date) >= prevThreeMonthsStart,
    );

    const prevThreeMonthsGrowth = calculateGrowth(
      analyticsResult,
      dates,
      threeMonthsStartIndex - 1,
      prevThreeMonthsStartIndex,
    );

    // Calculate total days in the last three months
    let totalDays = 0;
    for (let i = 0; i < 3; i++) {
      const monthDate = new Date(dates[lastIndex]);
      monthDate.setMonth(monthDate.getMonth() - i);
      monthDate.setDate(1);
      totalDays += new Date(
        monthDate.getFullYear(),
        monthDate.getMonth() + 1,
        0,
      ).getDate();
    }

    const threeMonthsData = {
      amount: threeMonthsGrowth,
      percentage: calculatePercentageChange(
        threeMonthsGrowth,
        prevThreeMonthsGrowth,
      ),
      daily: threeMonthsGrowth / totalDays,
    };

    return new SuccessResponseDto({
      data: {
        analytics: analyticsResult,
        week: weekData,
        month: monthData,
        threeMonths: threeMonthsData,
      },
    });
  }

  @httpGet(
    '/demographic',
    autoValidate(DemographicDto.validationSchema, 'query'),
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([
      UserCategory.CREATOR,
      UserCategory.COMMUNITY_OWNER,
      UserCategory.TEAM_MEMBER,
    ]),
    connectedArtistAccount,
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
  )
  public async demographic(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() query: DemographicDto,
    @queryParam() { user_id }: OptionalIdFiltersDto,
  ) {
    const vibrateIds = await this.fetchVibrateIds(claim, user_id);

    const platforms = Array.isArray(query.platforms)
      ? query.platforms
      : [query.platforms];

    if (vibrateIds.length === 0) {
      return new SuccessResponseDto({ data: { gender: {}, age: {} } });
    }

    let maleTotal = 0;
    let femaleTotal = 0;
    const age: Record<
      string,
      { male: { total: number }; female: { total: number } }
    > = {};

    const demographicPromises = vibrateIds.flatMap((userId) =>
      platforms.map(async (platform) => {
        const data = await this.vibrate.getFanBaseDemographic({
          uuid: userId,
          platform,
        });

        const demoData = data?.data?.data as DemographicData;
        const byGender = demoData['by-gender'] as GenderData;
        const byAge = demoData['by-age'] as AgeData;

        if (byGender) {
          maleTotal += byGender.male?.total || 0;
          femaleTotal += byGender.female?.total || 0;
        }

        if (byAge) {
          Object.entries(byAge).forEach(([key, value]) => {
            if (!age[key]) {
              age[key] = {
                female: { total: value.female?.total || 0 },
                male: { total: value.male?.total || 0 },
              };
            } else {
              age[key].female.total += value.female?.total || 0;
              age[key].male.total += value.male?.total || 0;
            }
          });
        }
      }),
    );

    await Promise.all(demographicPromises);

    const total = maleTotal + femaleTotal;
    const malePercentage = total ? (100 * maleTotal) / total : 0;
    const femalePercentage = total ? (100 * femaleTotal) / total : 0;

    const gender = {
      male: {
        total: maleTotal,
        percentage: parseFloat(malePercentage.toFixed(2)),
      },
      female: {
        total: femaleTotal,
        percentage: parseFloat(femalePercentage.toFixed(2)),
      },
    };

    return new SuccessResponseDto({ data: { gender, age } });
  }

  @httpGet(
    '/platforms',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([
      UserCategory.CREATOR,
      UserCategory.COMMUNITY_OWNER,
      UserCategory.TEAM_MEMBER,
    ]),
    connectedArtistAccount,
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
  )
  public async platforms() {
    const platforms = this.fanbase;
    return new SuccessResponseDto({ data: platforms });
  }

  @httpGet(
    '/country/audience',
    autoValidate(CountryAudienceDto.validationSchema, 'query'),
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([
      UserCategory.CREATOR,
      UserCategory.COMMUNITY_OWNER,
      UserCategory.TEAM_MEMBER,
    ]),
    connectedArtistAccount,
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
  )
  public async countryAudience(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() query: CountryAudienceDto,
    @queryParam() { user_id }: OptionalIdFiltersDto,
  ) {
    const { platform } = query;
    const vibrateIds = await this.fetchVibrateIds(claim, user_id);

    if (vibrateIds.length === 0) {
      return new SuccessResponseDto({
        data: { africa: {}, others: {}, countries: [] },
      });
    }

    const countryAudiencePromises = vibrateIds.map(async (vibrateId) => {
      const data = await this.vibrate.getFanBaseDemographic({
        uuid: vibrateId,
        platform,
      });

      return data?.data?.data as any;
    });

    const resolvedCountryAudience = await Promise.all(countryAudiencePromises);

    const platformAudienceCountriesMap: { [countryCode: string]: any } = {};

    for (const audienceData of resolvedCountryAudience) {
      const audienceCountries: { [countryCode: string]: any } =
        platform === 'spotify'
          ? (audienceData as any)
          : audienceData?.['by-country'] || {};

      for (const [countryCode, audience] of Object.entries(audienceCountries)) {
        const audienceEntry = audience as any;

        if (!platformAudienceCountriesMap[countryCode]) {
          platformAudienceCountriesMap[countryCode] = { ...audienceEntry };
        } else {
          platformAudienceCountriesMap[countryCode].count =
            (platformAudienceCountriesMap[countryCode].count || 0) +
            (audienceEntry.count || 0);
        }
      }
    }

    const platformAudienceCountries = Object.values(
      platformAudienceCountriesMap,
    );

    const { africaCount, otherCount, countryResult } =
      this.processCountryAudience(platformAudienceCountries, platform);

    const totalCount = africaCount + otherCount;
    const africaPercent =
      totalCount > 0
        ? parseFloat(((100 * africaCount) / totalCount).toFixed(2))
        : 0;
    const otherPercent =
      totalCount > 0
        ? parseFloat(((100 * otherCount) / totalCount).toFixed(2))
        : 0;

    return new SuccessResponseDto({
      data: {
        africa: { count: africaCount, percentage: africaPercent },
        others: { count: otherCount, percentage: otherPercent },
        countries: countryResult,
      },
    });
  }

  @httpGet(
    '/city/audience',
    autoValidate(CountryAudienceDto.validationSchema, 'query'),
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([
      UserCategory.CREATOR,
      UserCategory.COMMUNITY_OWNER,
      UserCategory.TEAM_MEMBER,
    ]),
    connectedArtistAccount,
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
  )
  public async cityAudience(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() query: CountryAudienceDto,
    @queryParam() { user_id }: OptionalIdFiltersDto,
  ) {
    const { platform } = query;
    const vibrateIds = await this.fetchVibrateIds(claim, user_id);

    if (vibrateIds.length === 0) {
      return new SuccessResponseDto({ data: { cities: [] } });
    }

    const cityAudiencePromises = vibrateIds.map(async (vibrateId) => {
      const data = await this.vibrate.getFanBaseDemographic({
        uuid: vibrateId,
        platform,
      });

      return data?.data?.data;
    });

    const audienceDataArray = await Promise.all(cityAudiencePromises);

    const platformAudienceCities = [];
    for (const audienceData of audienceDataArray) {
      let cityData;
      if (platform === 'spotify') {
        cityData = this.computeSpotifyCityAudience(audienceData);
      } else if (platform === 'youtube' || platform === 'instagram') {
        cityData = this.computeYoutubeCityAudience(audienceData['by-city']);
      } else {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          `Invalid platform ${platform} for city audience`,
        );
      }
      platformAudienceCities.push(...cityData);
    }

    const combinedCities = this.combineCityData(platformAudienceCities);

    return new SuccessResponseDto({ data: { cities: combinedCities } });
  }

  private combineCityData(cities: any[]) {
    const cityMap = new Map();
    for (const city of cities) {
      const key = city.city.toLowerCase();
      if (cityMap.has(key)) {
        const existing = cityMap.get(key);
        existing.spotify_followers += city.spotify_followers || 0;
        existing.youtube_followers += city.youtube_followers || 0;
        existing.views_1m += city.views_1m || 0;
      } else {
        cityMap.set(key, { ...city });
      }
    }
    return Array.from(cityMap.values());
  }

  private processCountryAudience(audienceCountries: any[], platform: string) {
    let africaCount = 0;
    let otherCount = 0;
    const countryResult: any[] = [];

    if (platform === 'youtube') {
      for (const countryCode in audienceCountries) {
        const audienceCountry = audienceCountries[countryCode];
        const theCountry = countries.find(
          (country) => country.code.toLowerCase() === countryCode.toLowerCase(),
        );

        if (theCountry) {
          audienceCountry.country_name = theCountry.name;
          audienceCountry.country_code = countryCode;

          countryResult.push({ ...audienceCountry, country_code: countryCode });

          if (theCountry.region.toLowerCase() === 'africa') {
            africaCount += 1;
          } else {
            otherCount += 1;
          }
        }
      }
    } else {
      for (const audienceCountry of audienceCountries) {
        const countryCode = audienceCountry?.country_code;

        const theCountry = countries.find(
          (country) =>
            country.code.toLowerCase() === countryCode?.toLowerCase(),
        );

        if (theCountry) {
          audienceCountry.country_name = theCountry.name;

          countryResult.push(audienceCountry);

          if (theCountry.region.toLowerCase() === 'africa') {
            africaCount += 1;
          } else {
            otherCount += 1;
          }
        }
      }
    }

    return { africaCount, otherCount, countryResult };
  }

  private computeSpotifyCityAudience(rawData: any) {
    const updatedCities = rawData?.map((city: any) => {
      const lastDataValue: any = Object.values(city?.data).pop();
      const theCity = cities.find(
        (x) => x.City.toLowerCase() === city?.city.toLowerCase(),
      );
      return {
        ...city,
        spotify_followers: lastDataValue,
        data: undefined,
        coordinates: {
          lat: theCity?.Latitude,
          lng: theCity?.Longitude,
        },
      };
    });

    const totalFollowers = updatedCities.reduce(
      (sum: number, city: any) => sum + city?.spotify_followers,
      0,
    );

    const result = [];

    for (const uc of updatedCities) {
      const percentage = parseFloat(
        ((100 * uc.spotify_followers) / totalFollowers).toFixed(2),
      );
      result.push({
        ...uc,
        spotify_followers_pct: percentage,
      });
    }

    return result;
  }

  private computeYoutubeCityAudience(rawData: any) {
    const totalFollowers = rawData?.reduce(
      (sum: number, city: any) => sum + city.views_1m,
      0,
    );

    const result = [];

    for (const uc of rawData) {
      const percentage = parseFloat(
        ((100 * uc.views_1m) / totalFollowers).toFixed(2),
      );
      result.push({
        ...uc,
        youtube_followers_pct: percentage,
        youtube_followers: uc.views_1m,
      });
    }

    return result;
  }

  private async simplifyProxyQuery(claim: Claim, user_id?: string) {
    const roleDesc = await this.user.describe(claim.team_id ?? claim.id);

    const isProxyRequest =
      user_id && (claim.admin_auth || roleDesc?.community_level_user);

    let userDesc: UserDescription;

    if (isProxyRequest) {
      userDesc = await this.user.describe(user_id);

      if (
        !userDesc ||
        (!claim.admin_auth && userDesc.community_id !== claim.community_id)
      ) {
        throw new ApplicationError(StatusCodes.NOT_FOUND, 'User not found');
      }
    } else if (!isProxyRequest && claim.admin_auth) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        "'user_id' is required",
      );
    } else {
      userDesc = roleDesc;
    }

    return userDesc;
  }
}
