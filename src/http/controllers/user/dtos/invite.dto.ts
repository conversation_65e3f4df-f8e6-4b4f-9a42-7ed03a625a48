import { Role } from '@app/services/user/entities/user.entity';
import <PERSON><PERSON> from 'joi';

export class InviteDto {
  email: string;
  role: Extract<Role, Role.ADMIN | Role.COMMUNITY_MANAGER | Role.FINANCE>;

  static validationSchema = Joi.object<InviteDto, true>({
    email: Joi.string().email().required().lowercase(),
    role: Joi.string()
      .valid(Role.ADMIN, Role.COMMUNITY_MANAGER, Role.FINANCE)
      .required(),
  });
}
