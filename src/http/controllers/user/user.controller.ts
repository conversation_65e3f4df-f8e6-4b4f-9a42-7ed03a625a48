import {
  controller,
  httpGet,
  httpPatch,
  httpPost,
  request as httpReq,
  queryParam,
  requestBody,
  requestParam,
} from 'inversify-express-utils';
import { inject } from 'inversify';
import {
  MIDDLEWARE_TOKENS,
  MODULE_TOKENS,
  SERVICE_TOKENS,
} from '@app/internal/ioc/tokens';
import { UserService } from '@app/services/user/user.service';
import { Claim, RequestWithClaims } from '@app/internal/types';
import { SuccessResponseDto } from '@app/http/dtos/http-response.dto';
import { omit, pick } from 'lodash';
import { autoValidate } from '@app/http/middlewares/validation.middleware';
import { UpdateProfileDto } from './dtos/update-profile.dto';
import {
  applyFileFilter,
  FileMiddleware,
  fileTypeFilter,
} from '@app/http/middlewares/file.middleware';
import { DURATION, FileSize } from '@app/internal/enums';
import { UpdateFanPageDto } from './dtos/update-fan-page.dto';
import { extractFilename, extractKeyFromS3Url } from '@app/utils/file.utils';
import { S3 } from '@app/modules/s3';
import { ApplicationError } from '@app/internal/errors';
import { StatusCodes } from 'http-status-codes';
import {
  User,
  UserCategory,
  UserStatus,
} from '@app/services/user/entities/user.entity';
import { IdDto, OptionalIdDto } from '@app/http/dtos/id.dto';
import * as path from 'path';
import { IP2C } from '@app/modules/ip2c';
import { CommunityService } from '@app/services/community/community.service';
import { inCategory } from '@app/http/middlewares/user-category.middleware';
import { InviteDto } from './dtos/invite.dto';
import {
  assignPermissions,
  PermissionResource,
} from '@app/utils/permissions.utils';
import { InviteService } from '@app/services/invite/invite.service';
import moment from 'moment';
import { loadTemplate, render } from '@app/utils/template.utils';
import { JobQueueManager } from '@app/internal/bull';
import {
  emailQueue,
  SendEmailJob,
  sendNotificationJob,
  SendNotificationJob,
} from '@app/mq/jobs';
import { AcceptInviteDto } from './dtos/accept-invite.dto';
import { hashPassword } from '@app/utils/bcrypt-utils';
import { TokenAuth } from '@app/internal/token/auth';
import { TeamQueryDto } from '../community/dto/team.dto';
import { generateUsername } from '@app/utils/post.utils';
import { parseFromUserAgent } from '@app/utils/device-detector-utils';
import { Request } from 'express';
import { syncCatch } from '@app/utils/sync-catch';
import { Community } from '@app/services/community/entities/community.entity';
import { notificationMilestones } from '@app/services/notification/entity/notification.entity';
import { TransactionService } from '@app/services/transaction/transaction.service';
import { hasPermissions } from '@app/http/middlewares/permissions.middleware';
import { UserTransactionsQueryDto } from './dtos/get-transactions.dto';
import { adminOnlyAuth } from '@app/http/middlewares/auth.middleware';
import { UserOverviewDto } from './dtos/overview.dto';
import { Period } from '@app/utils/period.utils';
import { SubscriptionService } from '@app/services/subscription/subscription.service';
import { Stripe } from '@app/modules/stripe';
import { Revelator } from '@app/modules/revelator';
import { PaginatedQueryDto } from '@app/http/dtos/paginated-query.dto';
import {
  Invitation,
  InviteStatus,
} from '@app/services/invite/entities/invite.entity';
import AdmZip from 'adm-zip';

@controller('/user')
export class UserController {
  private readonly emailQueue =
    this.jobQueueManager.getQueue<SendEmailJob>(emailQueue);

  private readonly notificationQueue =
    this.jobQueueManager.getQueue<SendNotificationJob>(sendNotificationJob);

  constructor(
    @inject(MODULE_TOKENS.IP2C) private readonly ip2c: IP2C,
    @inject(MODULE_TOKENS.TokenAuth) private readonly tokenAuth: TokenAuth,
    @inject(SERVICE_TOKENS.UserService)
    private readonly user: UserService,
    @inject(SERVICE_TOKENS.CommunityService)
    private readonly community: CommunityService,
    @inject(MODULE_TOKENS.S3) private readonly s3: S3,
    @inject(SERVICE_TOKENS.inviteService)
    private readonly invite: InviteService,
    @inject(MODULE_TOKENS.JobQueueManager)
    private readonly jobQueueManager: JobQueueManager,
    @inject(SERVICE_TOKENS.TransactionService)
    private readonly transactions: TransactionService,
    @inject(SERVICE_TOKENS.SubscriptionService)
    private readonly subscription: SubscriptionService,
    @inject(MODULE_TOKENS.Stripe) private readonly stripe: Stripe,
    @inject(MODULE_TOKENS.Revelator) private readonly revelator: Revelator,
  ) {}

  @httpGet('/profile', MIDDLEWARE_TOKENS.AuthMiddleware)
  public async getUser(@httpReq() { claim }: RequestWithClaims) {
    const [community, user] = await Promise.all([
      this.community.get(claim.community_id),
      this.user.getById(claim?.team_id ?? claim.id),
    ]);

    const isTeamMember = !!claim.team_id;

    return new SuccessResponseDto({
      data: {
        ...omit(user, ['password']),
        community,
        is_team_member: isTeamMember,
      },
    });
  }

  @httpGet(
    '/:id/overview',
    adminOnlyAuth,
    MIDDLEWARE_TOKENS.AuthMiddleware,
    autoValidate(IdDto.validationSchema, 'params'),
    autoValidate(UserOverviewDto.validationSchema, 'query'),
  )
  public async overview(
    @requestParam() { id }: IdDto,
    @queryParam()
    {
      period,
      from: fromParam,
      to: toParam,
      status,
      page_number,
      result_per_page,
    }: UserOverviewDto,
  ) {
    let fromDate: Date | undefined = fromParam;
    let toDate: Date | undefined = toParam;

    if (period) {
      const date_range = Period(period);
      fromDate = date_range.from;
      toDate = date_range.to;
    }

    const overview = await this.user.overview(id, {
      status,
      from: fromDate,
      to: toDate,
      page_number,
      result_per_page,
    });

    return new SuccessResponseDto({ data: overview });
  }

  @httpGet(
    '/profile/view/:id',
    MIDDLEWARE_TOKENS.OptionalAuthMiddleware,
    autoValidate(IdDto.validationSchema, 'params'),
  )
  public async viewProfile(
    @requestParam() { id }: IdDto,
    @httpReq() req: RequestWithClaims,
  ) {
    const { claim } = req;

    const user = await this.user.getById(id);

    if (!user) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Profile not found');
    }

    const ip = <string>req.headers['x-real-ip'] ?? req.ip;

    const ipMetadata = await this.ip2c.queryIP(ip);

    const userAgentData = parseFromUserAgent(req.headers?.['user-agent']);

    await this.user.viewProfile(id, {
      viewer_id: claim?.id,
      metadata: { ip, country: ipMetadata.isoCountryCode, ...userAgentData },
    });

    const profileViews = await this.user.getProfileViews(id);
    if (notificationMilestones.includes(profileViews)) {
      await this.notificationQueue.add('send_notification_job', {
        user_id: id,
        message: `🎉 Your fan page has been visited ${profileViews} times! Keep engaging your audience to grow even more.`,
      });
    }

    return new SuccessResponseDto();
  }

  @httpGet(
    '/profile/:id',
    MIDDLEWARE_TOKENS.OptionalAuthMiddleware,
    autoValidate(IdDto.validationSchema, 'params'),
  )
  public async getPublicProfile(
    @httpReq() req: Request,
    @requestParam() { id: idOrUsername }: IdDto,
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { id }: OptionalIdDto,
  ) {
    let community: Community;

    if (id) {
      community = await this.community.get(id);
    } else {
      const origin = req.headers['origin'];

      const [clientUrl, urlParseErr] = syncCatch(() => new URL(origin));

      if (urlParseErr != null) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          'Invalid Request Header',
        );
      }

      community = await this.community
        .find({
          custom_domain: clientUrl.hostname,
          custom_domain_active: true,
        })
        .then((d) => d[0]);
    }

    if (!community) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Community not found');
    }

    const profile = await this.user.getProfile(idOrUsername, {
      user_viewing_profile: claim?.id,
      community_id: community.id,
    });

    if (!profile) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Profile not found');
    }

    return new SuccessResponseDto({ data: profile });
  }

  @httpPatch(
    '/profile',
    autoValidate(UpdateProfileDto.validationSchema),
    MIDDLEWARE_TOKENS.AuthMiddleware,
  )
  public async updateProfile(
    @httpReq() req: RequestWithClaims,
    @requestBody() payload: UpdateProfileDto,
  ) {
    const claim = req.claim;

    let user = await this.user.getById(claim?.team_id ?? claim.id);

    if (!user)
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'User not found');

    if (payload.phone && payload.phone != user.phone) {
      const userWithPhoneNumber = await this.user.getByPhone(payload.phone);

      if (userWithPhoneNumber) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          'An account already exists with this phone number',
        );
      }
    }

    user = await this.user.update(claim?.team_id ?? claim.id, payload);

    return new SuccessResponseDto({ data: omit(user, ['password']) });
  }

  @httpPatch(
    '/fan-page',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    autoValidate(UpdateFanPageDto.validationSchema),
    FileMiddleware(
      'fields',
      {
        fields: [
          { name: 'profile_picture', maxCount: 1 },
          { name: 'cover_picture', maxCount: 1 },
        ],
      },
      {
        limits: { fileSize: 5 * FileSize.MB },
        fileFilter: applyFileFilter([
          fileTypeFilter(['.jpg', '.png', '.jpeg']),
        ]),
      },
    ),
  )
  public async updateFanPage(
    @httpReq() req: RequestWithClaims,
    @requestBody() body: UpdateFanPageDto,
  ): Promise<SuccessResponseDto> {
    const claim = req.claim;
    const files = req.files as {
      profile_picture?: Express.Multer.File[];
      cover_picture?: Express.Multer.File[];
    };

    const user = await this.user.getById(claim.id);

    const { collaborators, accounts, ...rest } = body;
    const payload: any = { ...rest };

    const noBodyChanges = Object.keys(payload).length === 0;
    const noFilesUploaded =
      !files?.profile_picture?.length && !files?.cover_picture?.length;

    if (noBodyChanges && noFilesUploaded) {
      return new SuccessResponseDto({ data: omit(user, ['password']) });
    }

    await this.handleProfilePictureUpdate(
      files?.profile_picture?.[0],
      user,
      payload,
    );

    await this.handleCoverPictureUpdate(
      files?.cover_picture?.[0],
      user,
      payload,
    );

    if (body.username) {
      await this.validateUsername(body.username);
    } else {
      payload.username = !user.username ? generateUsername() : user.username;
    }

    if (accounts !== undefined) {
      payload.accounts = JSON.stringify(accounts);
    }

    if (collaborators !== undefined) {
      await this.validateCollaborators(collaborators);
      payload.collaborators = collaborators;
    }

    const updatedUser = await this.user.update(claim.id, payload);

    return new SuccessResponseDto({ data: omit(updatedUser, ['password']) });
  }

  @httpPost(
    '/:id/follow',
    autoValidate(IdDto.validationSchema, 'params'),
    MIDDLEWARE_TOKENS.AuthMiddleware,
  )
  public async followUser(@httpReq() req: RequestWithClaims) {
    const { id } = req.params;
    const claim = req.claim;

    if (claim.id === id) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'You cannot follow yourself',
      );
    }

    const follow = await this.user.toggleFollow(id, claim?.team_id ?? claim.id);

    const user = await this.user.getById(id);

    if (follow) {
      const currentUser = await this.user.getById(claim.id);
      await this.notificationQueue.add('send_notification_job', {
        user_id: id,
        message: `👋 ${currentUser.username} just followed you!`,
        metadata: {
          user: { ...pick(currentUser, 'username', 'profile_picture') },
        },
      });
    }

    if (notificationMilestones.includes(user.follower_count)) {
      await this.notificationQueue.add('send_notification_job', {
        user_id: id,
        message: `🎉 You've reached ${user.follower_count} followers! Your audience is growing.`,
      });
    }

    return new SuccessResponseDto();
  }

  @httpPost(
    '/invites',
    autoValidate(InviteDto.validationSchema),
    MIDDLEWARE_TOKENS.AuthMiddleware,
    MIDDLEWARE_TOKENS.SubscriptionCheckMiddleware,
    inCategory([UserCategory.CREATOR]),
  )
  public async inviteTeamMember(
    @httpReq() { claim }: RequestWithClaims,
    @requestBody() payload: InviteDto,
  ) {
    if (claim.team_role !== 'owner') {
      throw new ApplicationError(
        StatusCodes.UNAUTHORIZED,
        'You do not have the permissions to perform this action',
      );
    }

    if (claim.email === payload.email) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'You cannot invite yourself',
      );
    }

    const sub = await this.subscription.getCurrentSubscription(claim.id);

    const product = await this.stripe.retrieveProduct(sub.metadata.product_id);

    const maxTeamMembers =
      product.metadata?.usage_limits?.max_team_members ?? 0;

    const currentCount = await this.user.teamMemberCount(
      claim.team_id ?? claim.id,
    );

    if (currentCount + 1 > maxTeamMembers) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        `You have reached the team-member limit (${maxTeamMembers}) for your current plan.`,
      );
    }

    const existingUser = await this.user.find({
      email: payload.email,
      category: [
        UserCategory.CREATOR,
        UserCategory.BRAND,
        UserCategory.FAN,
        UserCategory.TEAM_MEMBER,
      ],
    });
    if (existingUser) {
      throw new ApplicationError(
        StatusCodes.CONFLICT,
        'User already belongs to a team or community',
      );
    }

    const community = await this.community.get(claim.community_id);

    if (!community || !community.custom_domain_active) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Community not active');
    }

    const permissions = assignPermissions(payload.role);

    const existingInvite = await this.invite.find({
      community_id: community.id,
      email: payload.email,
      status: InviteStatus.pending,
    });

    const newExpiry = moment().add(5, 'days').toDate();

    let invite: Invitation;

    if (existingInvite) {
      if (moment(existingInvite.expiry).isAfter(moment())) {
        throw new ApplicationError(
          StatusCodes.CONFLICT,
          'An active invitation already exists for this email',
        );
      } else {
        invite = await this.invite.update(existingInvite.id, {
          expiry: newExpiry,
          permissions,
        });
      }
    } else {
      invite = await this.invite.create({
        community_id: claim.community_id,
        email: payload.email,
        expiry: newExpiry,
        team_id: claim.id,
        role: payload.role,
        permissions,
        status: InviteStatus.pending,
      });
    }

    const accountOwner = await this.user.find({
      community_id: claim.community_id,
      email: claim.email,
    });

    const url = `https://${community.custom_domain}/invite/${invite.id}`;
    const template = loadTemplate('community/account-invite');
    const htmlData = {
      accountName: accountOwner.first_name,
      communityName: community.name,
      role: payload.role,
      url,
      logoUrl: community.brand_themed_logo,
      color: community.brand_color,
    };
    const html = render(template, htmlData);

    await this.emailQueue.add('email', {
      to: payload.email,
      subject: 'Team Invite 🎶',
      body: html,
    });

    return new SuccessResponseDto();
  }

  @httpPost(
    '/invites/:id/resend',
    autoValidate(IdDto.validationSchema, 'params'),
    MIDDLEWARE_TOKENS.AuthMiddleware,
    MIDDLEWARE_TOKENS.SubscriptionCheckMiddleware,
    inCategory([UserCategory.CREATOR]),
  )
  public async resendInvite(
    @httpReq() { claim }: RequestWithClaims,
    @requestParam() { id }: IdDto,
  ) {
    const invite = await this.invite.get(id);
    if (!invite || invite.team_id !== (claim.team_id ?? claim.id)) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Invitation not found');
    }

    if (invite.status === 'accepted') {
      throw new ApplicationError(
        StatusCodes.CONFLICT,
        'This invitation has already been accepted',
      );
    }

    if (moment(invite.expiry).isAfter(moment())) {
      throw new ApplicationError(
        StatusCodes.CONFLICT,
        'An active invitation already exists for this email. You can resend only after it expires.',
      );
    }

    invite.expiry = moment().add(5, 'days').toDate();
    await this.invite.update(invite.id, { expiry: invite.expiry });

    const community = await this.community.get(invite.community_id);

    const user = await this.user.getById(invite.team_id);

    const url = `https://${community.custom_domain}/invite/${invite.id}`;
    const template = loadTemplate('community/account-invite');
    const html = render(template, {
      accountName: user.first_name,
      communityName: community.name,
      role: invite.role,
      url,
      logoUrl: community.brand_themed_logo,
      color: community.brand_color,
    });

    await this.emailQueue.add('email', {
      to: invite.email,
      subject: 'Team Invite 🎶 (Resent)',
      body: html,
    });

    return new SuccessResponseDto();
  }

  @httpPost(
    '/invites/:id/accept',
    autoValidate(AcceptInviteDto.validationSchema),
    autoValidate(IdDto.validationSchema, 'params'),
  )
  public async acceptInvite(
    @requestParam() { id }: IdDto,
    @requestBody() payload: AcceptInviteDto,
  ) {
    const validInvite = await this.invite.get(id);
    if (
      moment().isAfter(validInvite.expiry) ||
      validInvite.status === 'accepted'
    ) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'This invite has expired/accepted',
      );
    }

    const password = await hashPassword(payload.password);

    const community = await this.community.get(validInvite.community_id);

    const user = await this.user.create({
      first_name: payload.first_name,
      last_name: payload.last_name,
      email: validInvite.email,
      role: validInvite.role,
      category: UserCategory.TEAM_MEMBER,
      permissions: validInvite.permissions,
      password,
      community_id: validInvite.community_id,
      team_id: validInvite.team_id,
      email_verified: true,
    });

    await this.invite.update(validInvite.id, {
      status: InviteStatus.accepted,
    });

    const template = loadTemplate('community/welcome-invitee');
    const htmlData = {
      fname: user.first_name,
      communityName: community.name,
      url: `https://${community.custom_domain}/login`,
      logoUrl: community.brand_themed_logo,
      color: community.brand_color,
    };
    const html = render(template, htmlData);

    await this.emailQueue.add('email', {
      to: user.email,
      subject: 'Welcome Onboard 🎶🚀',
      body: html,
    });

    const tokenPayload: Claim = {
      id: user.id,
      email: user.email,
      team_role: user.role,
      permissions: user.permissions,
      community_id: user.community_id,
      category: user.category,
      team_id: user?.team_id,
    };

    const token = await this.tokenAuth.generate(tokenPayload, DURATION.DAYS);

    return new SuccessResponseDto({
      data: { token, ...omit(user, ['password']) },
    });
  }

  @httpPatch(
    '/users/:id/toggle-suspend',
    autoValidate(IdDto.validationSchema, 'params'),
    MIDDLEWARE_TOKENS.AuthMiddleware,
    MIDDLEWARE_TOKENS.SubscriptionCheckMiddleware,
    inCategory([UserCategory.CREATOR]),
  )
  public async toggleSuspendUser(
    @httpReq() { claim }: RequestWithClaims,
    @requestParam() { id }: IdDto,
  ) {
    if (claim.id === id) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'You cannot change your own suspension state',
      );
    }

    const user = await this.user.getById(id);
    if (!user) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'User not found');
    }

    const teamMember = await this.user.getById(user.team_id);
    if (
      (user.category !== UserCategory.TEAM_MEMBER && !teamMember) ||
      teamMember.id !== claim.id
    ) {
      throw new ApplicationError(
        StatusCodes.FORBIDDEN,
        'You can only suspend your team members',
      );
    }

    const nextStatus =
      user.status === UserStatus.suspended
        ? UserStatus.active
        : UserStatus.suspended;

    await this.user.update(id, { status: nextStatus });

    const community = await this.community.get(user.community_id);

    const template = loadTemplate(
      nextStatus === UserStatus.suspended
        ? 'community/account-suspended'
        : 'community/account-reactivated',
    );
    const html = render(template, {
      fname: user.first_name,
      communityName: community.name,
      logoUrl: community.brand_themed_logo,
      color: community.brand_color,
    });

    await this.emailQueue.add('email', {
      to: user.email,
      subject:
        nextStatus === UserStatus.suspended
          ? 'Account Suspension Notice'
          : 'Account Reactivated ✅',
      body: html,
    });

    await this.notificationQueue.add('send_notification_job', {
      user_id: user.id,
      message: `👋 Your account has been ${
        nextStatus === UserStatus.suspended ? 'suspended' : 'reactivated'
      } by your admin`,
    });

    return new SuccessResponseDto();
  }

  @httpGet(
    '/invites',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([UserCategory.CREATOR, UserCategory.TEAM_MEMBER]),
  )
  public async invitees(@httpReq() { claim }: RequestWithClaims) {
    const invitees = await this.invite.find({
      community_id: claim.community_id,
      team_id: claim?.team_id ?? claim.id,
    });

    return new SuccessResponseDto({ data: invitees });
  }

  @httpGet(
    '/invites/:id',
    autoValidate(IdDto.validationSchema, 'params'),
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([UserCategory.CREATOR, UserCategory.TEAM_MEMBER]),
  )
  public async inviteeDetails(@requestParam() { id }: IdDto) {
    const invite = await this.invite.get(id);

    return new SuccessResponseDto({ data: invite });
  }

  @httpGet(
    '/team-members',
    autoValidate(TeamQueryDto.validationSchema, 'query'),
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([UserCategory.CREATOR, UserCategory.TEAM_MEMBER]),
  )
  public async teamMembers(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam()
    { search_term, status, result_per_page, cursor }: TeamQueryDto,
  ) {
    const teamMembers = await this.user.teamMembers(
      claim?.team_id ?? claim.id,
      {
        search_term,
        status,
        cursor,
        result_per_page,
      },
    );

    return new SuccessResponseDto({ data: teamMembers });
  }

  @httpPatch(
    '/invites/:id/change-permissions',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    MIDDLEWARE_TOKENS.SubscriptionCheckMiddleware,
    autoValidate(IdDto.validationSchema, 'params'),
    autoValidate(InviteDto.validationSchema),
    inCategory([UserCategory.CREATOR]),
  )
  public async updateTeamMemberPermissions(
    @httpReq() { claim }: RequestWithClaims,
    @requestParam() { id }: IdDto,
    @requestBody() payload: InviteDto,
  ) {
    const user = await this.user.getById(id);
    if (!user) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'User not found');
    }

    const teamMember = await this.user.getById(user.team_id);
    if (
      (user.category !== UserCategory.TEAM_MEMBER && !teamMember) ||
      teamMember.id !== claim.id
    ) {
      throw new ApplicationError(
        StatusCodes.FORBIDDEN,
        'You can only update your team members permissions',
      );
    }

    if (assignPermissions(payload.role) === user.permissions) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'The user is already assigned the same permissions',
      );
    }

    await this.user.update(id, {
      permissions: assignPermissions(payload.role),
      role: payload.role,
    });

    const community = await this.community.get(user.community_id);

    const template = loadTemplate('community/updated-permissions');

    const html = render(template, {
      fname: user.first_name,
      communityName: community.name,
      role: payload.role,
      logoUrl: community.brand_themed_logo,
      color: community.brand_color,
    });

    await this.emailQueue.add('email', {
      to: user.email,
      subject: 'Updated Permissions 📝',
      body: html,
    });

    await this.notificationQueue.add('send_notification_job', {
      user_id: user.id,
      message: `👋 Your permissions have been updated to ${payload.role} by your admin`,
    });

    return new SuccessResponseDto();
  }

  @httpGet(
    '/transactions',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([UserCategory.CREATOR, UserCategory.TEAM_MEMBER]),
    autoValidate(UserTransactionsQueryDto.validationSchema, 'query'),
    hasPermissions([
      {
        resource: PermissionResource.RevenueManagement,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
  )
  public async getUserTransactions(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam()
    {
      status,
      payment_type,
      type,
      page_number,
      result_per_page,
    }: UserTransactionsQueryDto,
  ) {
    const transactions = await this.transactions.getTransactions(
      claim.id,
      'user',
      {
        status,
        payment_type,
        type,
        page_number,
        result_per_page,
      },
    );

    return new SuccessResponseDto({ data: transactions });
  }

  @httpGet(
    '/wallet',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([UserCategory.CREATOR, UserCategory.TEAM_MEMBER]),
    hasPermissions([
      {
        resource: PermissionResource.RevenueManagement,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
  )
  public async walletDetails(@httpReq() { claim }: RequestWithClaims) {
    const wallet = await this.user.getWalletDetails(claim?.team_id ?? claim.id);

    return new SuccessResponseDto({ data: wallet });
  }

  @httpGet(
    '/statements',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([UserCategory.CREATOR, UserCategory.TEAM_MEMBER]),
    hasPermissions([
      {
        resource: PermissionResource.RevenueManagement,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
    autoValidate(PaginatedQueryDto.validationSchema, 'query'),
  )
  public async getStatements(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { result_per_page = 10, page_number = 1 }: PaginatedQueryDto,
  ) {
    const user = await this.user.getById(claim?.team_id ?? claim.id);

    const statements = await this.revelator.retrieveStatements(
      user.revelator_id,
      user.email,
      user.enterprise_name,
      {
        enterpriseId: user.enterprise_id,
        pageNumber: page_number,
        pageSize: result_per_page,
      },
    );

    return new SuccessResponseDto({ data: statements });
  }

  @httpGet(
    '/statements/:id',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([UserCategory.CREATOR, UserCategory.TEAM_MEMBER]),
    hasPermissions([
      {
        resource: PermissionResource.RevenueManagement,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
    autoValidate(IdDto.validationSchema, 'params'),
  )
  public async getStatement(
    @httpReq() { claim }: RequestWithClaims,
    @requestParam() { id }: IdDto,
  ) {
    const user = await this.user.getById(claim?.team_id ?? claim.id);

    const statement = await this.revelator.retrieveStatement(
      user.revelator_id,
      user.email,
      user.enterprise_name,
      id,
    );

    return new SuccessResponseDto({ data: statement });
  }

  @httpGet(
    '/statements/:id/download',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([UserCategory.CREATOR, UserCategory.TEAM_MEMBER]),
    hasPermissions([
      {
        resource: PermissionResource.RevenueManagement,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
    autoValidate(IdDto.validationSchema, 'params'),
  )
  public async downloadStatement(
    @httpReq() { claim }: RequestWithClaims,
    @requestParam() { id }: IdDto,
  ) {
    const user = await this.user.getById(claim?.team_id ?? claim.id);

    const statement = await this.revelator.downloadStatement(
      user.revelator_id,
      user.email,
      user.enterprise_name,
      id,
    );

    const zip = new AdmZip(statement);
    const csvEntry = zip.getEntries().find((e) => e.entryName.endsWith('.csv'));

    if (!csvEntry) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'CSV file not found in zip',
      );
    }

    const csvBuffer = csvEntry.getData();
    const filename = csvEntry.entryName || `statement-${id}.csv`;

    await this.emailQueue.add('email', {
      to: user.email,
      subject: 'Your statement is ready for download',
      body: `<p>Hi ${user.first_name},</p>
            <p>Your Royalty statement is attached below.</p>`,
      attachments: [
        {
          filename,
          content: csvBuffer,
          contentType: 'text/csv',
        },
      ],
    });

    return new SuccessResponseDto();
  }

  private async handleProfilePictureUpdate(
    profilePicture: Express.Multer.File | undefined,
    user: User,
    payload: any,
  ) {
    if (profilePicture) {
      if (user.profile_picture) {
        const key = extractKeyFromS3Url(user.profile_picture);
        await this.s3.deleteFile(key);
      }

      const fileExtension = path.extname(profilePicture.originalname);

      const timestampedFilename = `${extractFilename(
        profilePicture.originalname,
      )}_${new Date().toISOString()}${fileExtension}`;

      payload.profile_picture = await this.s3.upload({
        Body: profilePicture.buffer,
        Key: `${user.id}/pictures/${timestampedFilename}`,
        ContentType: `image/${profilePicture.mimetype.split('/')[1]}`,
      });
    }
  }

  @httpGet(
    '/:id',
    adminOnlyAuth,
    MIDDLEWARE_TOKENS.AuthMiddleware,
    autoValidate(IdDto.validationSchema, 'params'),
  )
  public async fetchUser(@requestParam() { id }: IdDto) {
    const user = await this.user.getById(id);

    if (!user) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'User not found');
    }

    return new SuccessResponseDto({ data: omit(user, 'password') });
  }

  private async handleCoverPictureUpdate(
    coverPicture: Express.Multer.File | undefined,
    user: User,
    payload: any,
  ) {
    if (coverPicture) {
      if (user.cover_picture) {
        const key = extractKeyFromS3Url(user.cover_picture);
        await this.s3.deleteFile(key);
      }

      const fileExtension = path.extname(coverPicture.originalname);

      const timestampedFilename = `${extractFilename(
        coverPicture.originalname,
      )}_${new Date().toISOString()}${fileExtension}`;

      payload.cover_picture = await this.s3.upload({
        Body: coverPicture.buffer,
        Key: `${user.id}/pictures/${timestampedFilename}`,
        ContentType: `image/${coverPicture.mimetype.split('/')[1]}`,
      });
    }
  }

  private async validateUsername(username: string | undefined) {
    if (username) {
      const userWithUsername = await this.user.getByUsername(username);
      if (userWithUsername) {
        throw new ApplicationError(StatusCodes.CONFLICT, 'Username is taken');
      }
    }
  }

  private async validateCollaborators(collaborators: string[]) {
    if (collaborators?.length > 0) {
      const invalidCollaborators = await this.findInvalidCollaborators(
        collaborators,
      );

      if (invalidCollaborators?.length > 0) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          `The following collaborators do not exist: ${invalidCollaborators.join(
            ', ',
          )}`,
        );
      }
    }
  }

  private async findInvalidCollaborators(
    collaborators: string[],
  ): Promise<string[]> {
    const ops = [];
    const invalidCollaborators = [];

    for (const collaborator of collaborators) {
      ops.push(
        this.user.getByUsername(collaborator).then((user) => {
          if (!user) {
            invalidCollaborators.push(collaborator);
          }
        }),
      );
    }

    await Promise.all(ops);

    return invalidCollaborators;
  }
}
