import {
  MIDDLEWARE_TOKENS,
  MODULE_TOKENS,
  SERVICE_TOKENS,
} from '@app/internal/ioc/tokens';
import { Claim, RequestWithClaims } from '@app/internal/types';
import { PostService } from '@app/services/post/post.service';
import { UserDescription, UserService } from '@app/services/user/user.service';
import {
  controller,
  httpGet,
  queryParam,
  request as httpReq,
} from 'inversify-express-utils';
import { inject } from 'inversify';
import { PerformanceDto } from './dto/performance.dto';
import { SuccessResponseDto } from '@app/http/dtos/http-response.dto';
import { CommunityService } from '@app/services/community/community.service';
import { ApplicationError } from '@app/internal/errors';
import { StatusCodes } from 'http-status-codes';
import { hasPermissions } from '@app/http/middlewares/permissions.middleware';
import { PermissionResource } from '@app/utils/permissions.utils';
import {
  AnalyticsFilter,
  Analytics<PERSON>uery,
  Revelator,
} from '@app/modules/revelator';
import { subMonths } from 'date-fns';
import { autoValidate } from '@app/http/middlewares/validation.middleware';
import { StreamGrowthDto } from '@app/http/controllers/analytics/dto/stream-growth.dto';
import distributorIds from '@app/misc/distributor-ids.json';
import { PaginatedQueryWithDateRangeDto } from '@app/http/dtos/paginated-query.dto';
import { ReleaseService } from '@app/services/release/release.service';
import { ArtistService } from '@app/services/artist/artist.service';
import countryPhoneCode from '@app/misc/country-phone-code.json';
import { Release } from '@app/services/release/entities/release.entity';
import { Artist } from '@app/services/artist/entity/artist.entity';
import { env } from '@app/config/env';
import { PeriodDto } from './dto/period.dto';
import { inCategory } from '@app/http/middlewares/user-category.middleware';
import { UserCategory } from '@app/services/user/entities/user.entity';
import { TopCountriesDto } from '@app/http/controllers/analytics/dto/top-countries.dto';
import { CatalogAnalyticsFilterDto } from '@app/http/controllers/analytics/dto/catalog-analytics-filter.dto';
import { TrackService } from '@app/services/track/track.service';
import { OptionalIdFiltersDto } from '@app/http/dtos/id.dto';
import { AnalyticsService } from '@app/services/analytics/analytics.service';
import { CommunitySubscriberStatSummaryDto } from '@app/http/controllers/analytics/dto/community-subscriber-stat-summary.dto';
import { SubscriberGrowthTrendDto } from '@app/http/controllers/analytics/dto/subscriber-growth-trend.dto';
import { SubscriberDemographicsDto } from '@app/http/controllers/analytics/dto/subscriber-demographics.dto';
import { SubscriberCountryAnalyticsDto } from '@app/http/controllers/analytics/dto/subscriber-country-analytics.dto';

@controller('/analytics')
export class AnalyticsController {
  constructor(
    @inject(SERVICE_TOKENS.AnalyticsService)
    private readonly analytics: AnalyticsService,
    @inject(SERVICE_TOKENS.ArtistService)
    private readonly artist: ArtistService,
    @inject(SERVICE_TOKENS.ReleaseService)
    private readonly release: ReleaseService,
    @inject(SERVICE_TOKENS.TrackService)
    private readonly track: TrackService,
    @inject(SERVICE_TOKENS.PostService)
    private readonly post: PostService,
    @inject(MODULE_TOKENS.Revelator) private readonly revelator: Revelator,
    @inject(SERVICE_TOKENS.UserService) private readonly user: UserService,
    @inject(SERVICE_TOKENS.CommunityService)
    private readonly community: CommunityService,
  ) {}

  @httpGet(
    '/community/creator',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
  )
  public async communityChannel(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { user_id }: OptionalIdFiltersDto,
  ) {
    const userDesc = await this.simplifyProxyQuery(claim, user_id);

    let communityChannel: Record<string, any>;

    if (userDesc.community_level_user) {
      communityChannel = await this.community.communityChannel(
        userDesc.community_id,
      );
    } else {
      communityChannel = await this.user.userCommunityChannel(
        userDesc.team_owner?.id ?? userDesc.id,
      );
    }

    return new SuccessResponseDto({ data: communityChannel });
  }

  @httpGet(
    '/user/growth',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
  )
  public async userGrowth(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { period, from, to }: PeriodDto,
    @queryParam() { user_id }: OptionalIdFiltersDto,
  ) {
    const userDesc = await this.simplifyProxyQuery(claim, user_id);

    let growthStats: Record<string, any>;

    if (userDesc.community_level_user) {
      growthStats = await this.community.clientsGrowth(
        userDesc.community_id,
        period,
        from,
        to,
      );
    } else {
      growthStats = await this.user.followersGrowth(
        userDesc.team_owner?.id ?? userDesc.id,
        period,
        from,
        to,
      );
    }

    return new SuccessResponseDto({ data: growthStats });
  }

  @httpGet(
    '/community/creator/visitors',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    autoValidate(PeriodDto.validationSchema, 'query'),
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
  )
  public async communityVisitors(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { period, from, to }: PeriodDto,
    @queryParam() { user_id }: OptionalIdFiltersDto,
  ) {
    let profileViewStats: Record<string, any>;

    let profileViewerDeviceStats: Record<string, any>;

    const userDesc = await this.simplifyProxyQuery(claim, user_id);

    if (userDesc.community_level_user) {
      profileViewStats = await this.community.profileViewStats(
        userDesc.community_id,
        period,
        from,
        to,
      );

      profileViewerDeviceStats = await this.community.profileViewerDeviceStats(
        userDesc.community_id,
        period,
        from,
        to,
      );
    } else {
      profileViewStats = await this.user.profileViewStats(
        userDesc.team_owner?.id ?? userDesc.id,
        period,
        from,
        to,
      );

      profileViewerDeviceStats = await this.user.profileViewerDeviceStats(
        userDesc.team_owner?.id ?? userDesc.id,
        period,
        from,
        to,
      );
    }

    return new SuccessResponseDto({
      data: {
        geolocation_stats: profileViewStats,
        viewers_device_stats: profileViewerDeviceStats,
      },
    });
  }

  @httpGet(
    '/content/engagement',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    autoValidate(PeriodDto.validationSchema, 'query'),
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
  )
  public async contentEngagement(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { period, from, to }: PeriodDto,
    @queryParam() { user_id }: OptionalIdFiltersDto,
  ) {
    const userDesc = await this.simplifyProxyQuery(claim, user_id);

    let contentEngagementStats: Record<string, any>;

    if (userDesc.community_level_user) {
      contentEngagementStats = await this.community.contentEngagement(
        userDesc.community_id,
        period,
        from,
        to,
      );
    } else {
      contentEngagementStats = await this.post.contentEngagement(
        userDesc.team_owner?.id ?? userDesc.id,
        period,
        from,
        to,
      );
    }

    return new SuccessResponseDto({ data: contentEngagementStats });
  }

  @httpGet(
    '/content/performance',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
    autoValidate(PerformanceDto.validationSchema, 'query'),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
  )
  public async contentPerformance(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() payload: PerformanceDto,
    @queryParam() { user_id }: OptionalIdFiltersDto,
  ) {
    const userDesc = await this.simplifyProxyQuery(claim, user_id);

    let contentPerformanceStats: Record<string, any>;

    if (userDesc.community_level_user) {
      contentPerformanceStats = await this.community.contentPerformance(
        userDesc.community_id,
        payload,
      );
    } else {
      contentPerformanceStats = await this.post.contentPerformance(
        userDesc.team_owner?.id ?? userDesc.id,
        payload,
      );
    }

    return new SuccessResponseDto({ data: contentPerformanceStats });
  }

  @httpGet(
    '/content/top',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    autoValidate(PeriodDto.validationSchema, 'query'),
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
  )
  public async topContent(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { period, from, to }: PeriodDto,
    @queryParam() { user_id }: OptionalIdFiltersDto,
  ) {
    const userDesc = await this.simplifyProxyQuery(claim, user_id);

    let topPostStats: any;

    if (userDesc.community_level_user) {
      topPostStats = await this.community.topPosts(
        userDesc.community_id,
        period,
        from,
        to,
      );
    } else {
      topPostStats = await this.post.topPosts(
        userDesc.team_owner?.id ?? userDesc.id,
        period,
        from,
        to,
      );
    }

    return new SuccessResponseDto({ data: topPostStats });
  }

  @httpGet(
    '/distribution/streams/total',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    MIDDLEWARE_TOKENS.SubscriptionCheckMiddleware,
    inCategory([
      UserCategory.COMMUNITY_OWNER,
      UserCategory.CREATOR,
      UserCategory.TEAM_MEMBER,
    ]),
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
    autoValidate(CatalogAnalyticsFilterDto.validationSchema, 'query'),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
  )
  public async totalStreams(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { release_id, track_id }: CatalogAnalyticsFilterDto,
    @queryParam() { user_id }: OptionalIdFiltersDto,
  ) {
    const today = new Date();
    const allTime = new Date('1975-01-01');

    const roleDesc = await this.simplifyProxyQuery(claim, user_id);

    let partnerUserId = roleDesc.revelator_id;

    const prevRecordsQuery: AnalyticsQuery = {
      metrics: 'trends',
      aggregation: 'byDistributor',
      filters: {
        fromDate: allTime,
        toDate: subMonths(today, 1),
        pageNumber: 1,
        pageSize: 1,
        orderByProperty: 'streamsCount',
        orderByDescending: true,
      },
    };

    const currRecordsQuery: AnalyticsQuery = {
      metrics: 'trends',
      aggregation: 'byDistributor',
      filters: {
        fromDate: allTime,
        toDate: today,
        pageNumber: 1,
        pageSize: 8,
        orderByProperty: 'streamsCount',
        orderByDescending: true,
      },
    };

    const emptyStreamRecords = [
      {
        distributor_id: 377,
        distributor_name: 'TikTok',
        streams: 0,
        stream_growth_rate: 0,
        percentage_stream: 0,
        icon: 'https://makerverse-prod.s3.eu-west-1.amazonaws.com/assets/tiktok.svg',
      },
      {
        distributor_id: 10,
        distributor_name: 'Spotify',
        streams: 0,
        stream_growth_rate: 0,
        percentage_stream: 0,
        icon: 'https://makerverse-prod.s3.eu-west-1.amazonaws.com/assets/spotify.svg',
      },
      {
        distributor_id: 61,
        distributor_name: 'Apple Music',
        streams: 0,
        stream_growth_rate: 0,
        percentage_stream: 0,
        icon: 'https://makerverse-prod.s3.eu-west-1.amazonaws.com/assets/apple-music.svg',
      },
      {
        distributor_id: 11,
        distributor_name: 'Deezer',
        streams: 0,
        stream_growth_rate: 0,
        percentage_stream: 0,
        icon: 'https://makerverse-prod.s3.eu-west-1.amazonaws.com/assets/deezer.svg',
      },
      {
        distributor_id: 359,
        distributor_name: 'Amazon Music Unlimited',
        streams: 0,
        stream_growth_rate: 0,
        percentage_stream: 0,
        icon: 'https://makerverse-prod.s3.eu-west-1.amazonaws.com/assets/amazon.svg',
      },
      {
        distributor_id: 85,
        distributor_name: 'SoundCloud',
        streams: 0,
        stream_growth_rate: 0,
        percentage_stream: 0,
        icon: 'https://makerverse-prod.s3.eu-west-1.amazonaws.com/assets/soundcloud.svg',
      },
      {
        distributor_id: 73,
        distributor_name: 'Amazon Prime',
        streams: 0,
        stream_growth_rate: 0,
        percentage_stream: 0,
        icon: 'https://makerverse-prod.s3.eu-west-1.amazonaws.com/assets/amazon-prime.svg',
      },
      {
        distributor_id: 451,
        distributor_name: 'Amazon Music Ad Supported',
        streams: 0,
        stream_growth_rate: 0,
        percentage_stream: 0,
        icon: 'https://makerverse-prod.s3.eu-west-1.amazonaws.com/assets/amazon.svg',
      },
    ];

    if (release_id) {
      const release = await this.release.getById(release_id);

      if (!release) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          'Invalid "release_id"',
        );
      }

      prevRecordsQuery.filters.releaseIds = [
        Number(release.revelator_release_id),
      ];
      currRecordsQuery.filters.releaseIds = [
        Number(release.revelator_release_id),
      ];
    }

    if (roleDesc.community_level_user) {
      partnerUserId = env.revelator_parent_account_id;

      const communityRevArtists = await this.artist.getRevArtists(
        roleDesc.community_id,
      );

      const artistIds = communityRevArtists.map((c) => Number(c.artist_rev_id));

      prevRecordsQuery.filters.artistIds = artistIds;
      currRecordsQuery.filters.artistIds = artistIds;
    }

    if (track_id) {
      const track = await this.track.getById(track_id);

      if (!track) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          'Invalid "track_id"',
        );
      }

      const { result: releases } = await this.track.getTrackReleases(track_id, {
        page_number: 1,
        result_per_page: 1,
      });

      if (releases.length < 1) {
        return new SuccessResponseDto({
          data: {
            total_streams: 0,
            stream_growth_rate: 0,
            stream_data: emptyStreamRecords,
          },
        });
      }

      const releaseContent = await this.revelator.getReleaseContent(
        partnerUserId,
        Number(releases[0].revelator_release_id),
      );

      const trackContent = releaseContent.tracks.find(
        (t) => (t.wav.fileId = track.audio_metadata.file_id),
      );

      prevRecordsQuery.filters.trackIds = [trackContent.trackId];
      currRecordsQuery.filters.trackIds = [trackContent.trackId];
    }

    const prevRecord = await this.revelator.analytics(
      partnerUserId,
      prevRecordsQuery,
    );

    const currRecord = await this.revelator.analytics(
      partnerUserId,
      currRecordsQuery,
    );

    const total_streams = currRecord.totals.streamsCount;

    let stream_growth_rate =
      prevRecord.totals.streamsCount === 0
        ? 0
        : ((currRecord.totals.streamsCount - prevRecord.totals.streamsCount) /
            prevRecord.totals.streamsCount) *
          100;

    if (
      prevRecord.totals.streamsCount === 0 &&
      currRecord.totals.streamsCount === 0
    ) {
      stream_growth_rate = 0;
    } else {
      stream_growth_rate = Number(stream_growth_rate.toFixed(4));
    }

    const stream_data: typeof emptyStreamRecords = [];

    for (const dataIdx in emptyStreamRecords) {
      const emptyRecord = emptyStreamRecords[dataIdx];

      const streamRecord = currRecord.items.find(
        (i) => i.metadata.distributorId === emptyRecord.distributor_id,
      );

      if (!streamRecord) {
        stream_data.push(emptyRecord);
        continue;
      }

      const prevStreamData = prevRecord.items.find(
        (pi) =>
          pi.metadata.distributorId === streamRecord.metadata.distributorId,
      );

      const prevStreamsCount = prevStreamData?.streamsCount ?? 0;

      const stream_growth_rate =
        prevStreamsCount === 0
          ? 100
          : ((streamRecord.streamsCount - prevStreamsCount) /
              prevStreamsCount) *
            100;

      stream_data.push({
        distributor_id: streamRecord.metadata.distributorId,
        distributor_name: streamRecord.metadata.distributorName,
        streams: streamRecord.streamsCount,
        stream_growth_rate: Number(stream_growth_rate.toFixed(4)),
        percentage_stream: Number(
          (
            (streamRecord.streamsCount / currRecord.totals.streamsCount) *
            100
          ).toFixed(4),
        ),
        icon: distributorIds.find(
          (d) => d.distributor_id === streamRecord.metadata.distributorId,
        )?.icon,
      });

      if (stream_data.length === emptyStreamRecords.length) {
        stream_data.sort((a, b) => b.streams - a.streams);
      }
    }

    return new SuccessResponseDto({
      data: { total_streams, stream_growth_rate, stream_data },
    });
  }

  @httpGet(
    '/distribution/streams/top-dsp',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    MIDDLEWARE_TOKENS.SubscriptionCheckMiddleware,
    autoValidate(PaginatedQueryWithDateRangeDto.validationSchema, 'query'),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
    inCategory([
      UserCategory.COMMUNITY_OWNER,
      UserCategory.CREATOR,
      UserCategory.TEAM_MEMBER,
    ]),
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
  )
  public async topDsp(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { user_id }: OptionalIdFiltersDto,
    @queryParam()
    {
      page_number = 1,
      result_per_page = 10,
      from = new Date('1975-01-01'),
      to = new Date(),
      release_id,
      track_id,
    }: PaginatedQueryWithDateRangeDto,
  ) {
    const analyticsQuery: AnalyticsQuery = {
      metrics: 'trends',
      aggregation: 'byDistributor',
      filters: {
        pageNumber: page_number,
        pageSize: result_per_page,
        orderByProperty: 'streamsCount',
        orderByDescending: true,
        fromDate: from,
        toDate: to,
      },
    };

    if (release_id) {
      const release = await this.release.getById(release_id);

      if (!release) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          'Invalid "release_id"',
        );
      }

      analyticsQuery.filters.releaseIds = [
        Number(release.revelator_release_id),
      ];
    }

    const roleDesc = await this.simplifyProxyQuery(claim, user_id);

    let partnerUserId = roleDesc.revelator_id;

    if (roleDesc.community_level_user) {
      partnerUserId = env.revelator_parent_account_id;

      const communityRevArtists = await this.artist.getRevArtists(
        roleDesc.community_id,
      );

      analyticsQuery.filters.artistIds = communityRevArtists.map((c) =>
        Number(c.artist_rev_id),
      );
    }

    if (track_id) {
      const track = await this.track.getById(track_id);

      if (!track) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          'Invalid "track_id"',
        );
      }

      const { result: releases } = await this.track.getTrackReleases(track_id, {
        page_number: 1,
        result_per_page: 1,
      });

      if (releases.length > 0) {
        return new SuccessResponseDto({
          data: {
            total_streams: 0,
            total_number_of_pages: 1,
            current_page: 1,
            result_per_page: 10,
            total: 0,
            result: [],
          },
        });
      }

      const releaseContent = await this.revelator.getReleaseContent(
        partnerUserId,
        Number(releases[0].revelator_release_id),
      );

      const trackContent = releaseContent.tracks.find(
        (t) => (t.wav.fileId = track.audio_metadata.file_id),
      );

      analyticsQuery.filters.trackIds = [trackContent.trackId];
    }

    const res = await this.revelator.analytics(partnerUserId, analyticsQuery);

    const result = res.items.map((i) => ({
      distributor_id: i.metadata.distributorId,
      distributor_name: i.metadata.distributorName,
      streams: i.streamsCount,
    }));

    return new SuccessResponseDto({
      data: {
        total_streams: res.totals.streamsCount,
        total_number_of_pages: Math.ceil(res.totalItemsCount / result_per_page),
        current_page: res.pageNumber,
        result_per_page,
        total: res.totalItemsCount,
        result,
      },
    });
  }

  @httpGet(
    '/distribution/streams/growth',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    MIDDLEWARE_TOKENS.SubscriptionCheckMiddleware,
    autoValidate(StreamGrowthDto.validationSchema, 'query'),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
    inCategory([
      UserCategory.COMMUNITY_OWNER,
      UserCategory.CREATOR,
      UserCategory.TEAM_MEMBER,
    ]),
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
  )
  public async streamGrowthTrend(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { distributor_id, release_id, track_id }: StreamGrowthDto,
    @queryParam() { user_id }: OptionalIdFiltersDto,
  ) {
    const today = new Date();

    const roleDesc = await this.simplifyProxyQuery(claim, user_id);

    let partnerUserId = roleDesc.revelator_id;

    const filters: AnalyticsFilter = {
      dateGranularity: 'Weekly',
      fromDate: subMonths(today, 1),
      toDate: today,
    };

    if (roleDesc.community_level_user) {
      partnerUserId = env.revelator_parent_account_id;

      const communityRevArtists = await this.artist.getRevArtists(
        roleDesc.community_id,
      );

      filters.artistIds = communityRevArtists.map((c) =>
        Number(c.artist_rev_id),
      );
    }

    if (distributor_id != null) {
      filters.distributorIds = [distributor_id];
    }

    if (release_id) {
      const release = await this.release.getById(release_id);

      if (!release) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          'Invalid "release_id"',
        );
      }

      filters.releaseIds = [Number(release.revelator_release_id)];
    }

    if (track_id) {
      const track = await this.track.getById(track_id);

      if (!track) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          'Invalid "track_id"',
        );
      }

      const { result: releases } = await this.track.getTrackReleases(track_id, {
        page_number: 1,
        result_per_page: 1,
      });

      if (releases.length < 1) {
        return new SuccessResponseDto({
          data: {
            total_streams: 0,
            stream_data: [],
          },
        });
      }

      const releaseContent = await this.revelator.getReleaseContent(
        partnerUserId,
        Number(releases[0].revelator_release_id),
      );

      const trackContent = releaseContent.tracks.find(
        (t) => (t.wav.fileId = track.audio_metadata.file_id),
      );

      filters.trackIds = [trackContent.trackId];
    }

    const res = await this.revelator.analytics(partnerUserId, {
      metrics: 'consumption',
      aggregation: 'byDate',
      filters,
    });

    const total_streams = res.totals.streamsCount;

    const stream_data =
      res.items[0]?.metrics.map((m: any) => ({
        date: m.eventDate,
        streams: m.streamsCount,
      })) ?? [];

    return new SuccessResponseDto({ data: { total_streams, stream_data } });
  }

  @httpGet(
    '/distribution/streams/top-tracks',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    MIDDLEWARE_TOKENS.SubscriptionCheckMiddleware,
    autoValidate(PaginatedQueryWithDateRangeDto.validationSchema, 'query'),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
    inCategory([
      UserCategory.COMMUNITY_OWNER,
      UserCategory.CREATOR,
      UserCategory.TEAM_MEMBER,
    ]),
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
  )
  public async topTracks(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { user_id }: OptionalIdFiltersDto,
    @queryParam()
    {
      page_number = 1,
      result_per_page = 10,
      from,
      to,
      release_id,
    }: PaginatedQueryWithDateRangeDto,
  ) {
    const roleDesc = await this.simplifyProxyQuery(claim, user_id);

    let partnerUserId = roleDesc.revelator_id;

    const analyticsQuery: AnalyticsQuery = {
      metrics: 'trends',
      aggregation: 'byTrack',
      filters: {
        pageNumber: page_number,
        pageSize: result_per_page,
        orderByProperty: 'streamsCount',
        orderByDescending: true,
        fromDate: from,
        toDate: to,
      },
    };

    if (release_id) {
      const release = await this.release.getById(release_id);

      if (!release) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          'Invalid "release_id"',
        );
      }

      analyticsQuery.filters.releaseIds = [
        Number(release.revelator_release_id),
      ];
    }

    if (roleDesc.community_level_user) {
      partnerUserId = env.revelator_parent_account_id;

      const communityRevArtists = await this.artist.getRevArtists(
        roleDesc.community_id,
      );

      analyticsQuery.filters.artistIds = communityRevArtists.map((c) =>
        Number(c.artist_rev_id),
      );
    }

    const res = await this.revelator.analytics(partnerUserId, analyticsQuery);

    const revelatorIdList: string[] = [];

    for (const resultItem of res.items) {
      revelatorIdList.push(resultItem.metadata.releaseId.toString());
    }

    let releases: Release[];

    if (revelatorIdList.length > 0) {
      releases = await this.release.findManyWithRevelatorId(revelatorIdList);
    }

    const result = res.items.map((i) => {
      const release = releases.find(
        (r) => r.revelator_release_id === i.metadata.releaseId,
      );

      return {
        cover_art: release?.cover_art ?? null,
        release_name: i.metadata.releaseName,
        track_name: i.metadata.trackName,
        artist_name: i.metadata.artistName,
        streams: i.streamsCount,
        percentage_stream: Number(
          ((i.streamsCount / res.totals.streamsCount) * 100).toFixed(2),
        ),
        release_date: release?.updated_at,
      };
    });

    return new SuccessResponseDto({
      data: {
        total_streams: res.totals.streamsCount,
        total_number_of_pages: Math.ceil(res.totalItemsCount / result_per_page),
        current_page: res.pageNumber,
        result_per_page,
        total: res.totalItemsCount,
        result,
      },
    });
  }

  @httpGet(
    '/distribution/streams/top-releases',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    MIDDLEWARE_TOKENS.SubscriptionCheckMiddleware,
    autoValidate(PaginatedQueryWithDateRangeDto.validationSchema, 'query'),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
    inCategory([
      UserCategory.COMMUNITY_OWNER,
      UserCategory.CREATOR,
      UserCategory.TEAM_MEMBER,
    ]),
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
  )
  public async topReleases(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { user_id }: OptionalIdFiltersDto,
    @queryParam()
    {
      page_number = 1,
      result_per_page = 10,
      from,
      to,
      track_id,
    }: PaginatedQueryWithDateRangeDto,
  ) {
    const roleDesc = await this.simplifyProxyQuery(claim, user_id);

    let partnerUserId = roleDesc.revelator_id;

    const analyticsQuery: AnalyticsQuery = {
      metrics: 'trends',
      aggregation: 'byRelease',
      filters: {
        pageNumber: page_number,
        pageSize: result_per_page,
        orderByProperty: 'streamsCount',
        orderByDescending: true,
        fromDate: from,
        toDate: to,
      },
    };

    if (roleDesc.community_level_user) {
      partnerUserId = env.revelator_parent_account_id;

      const communityRevArtists = await this.artist.getRevArtists(
        roleDesc.community_id,
      );

      analyticsQuery.filters.artistIds = communityRevArtists.map((c) =>
        Number(c.artist_rev_id),
      );
    }

    if (track_id) {
      const track = await this.track.getById(track_id);

      if (!track) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          'Invalid "track_id"',
        );
      }

      const { result: releases } = await this.track.getTrackReleases(track_id, {
        page_number: 1,
        result_per_page: 1,
      });

      if (releases.length < 1) {
        return new SuccessResponseDto({
          data: {
            total_streams: 0,
            total_number_of_pages: 1,
            current_page: 1,
            result_per_page: 10,
            total: 0,
            result: [],
          },
        });
      }

      const releaseContent = await this.revelator.getReleaseContent(
        partnerUserId,
        Number(releases[0].revelator_release_id),
      );

      const trackContent = releaseContent.tracks.find(
        (t) => (t.wav.fileId = track.audio_metadata.file_id),
      );

      analyticsQuery.filters.trackIds = [trackContent.trackId];
    }

    const res = await this.revelator.analytics(partnerUserId, analyticsQuery);

    const revelatorIdList: string[] = [];

    for (const resultItem of res.items) {
      revelatorIdList.push(resultItem.metadata.releaseId.toString());
    }

    let releases: Release[];

    if (revelatorIdList.length > 0) {
      releases = await this.release.findManyWithRevelatorId(revelatorIdList);
    }

    const result = res.items.map((i) => {
      const release = releases.find(
        (r) => r.revelator_release_id === i.metadata.releaseId,
      );

      return {
        cover_art: release?.cover_art ?? null,
        release_name: i.metadata.releaseName,
        artist_name: i.metadata.artistName,
        streams: i.streamsCount,
        percentage_stream: Number(
          ((i.streamsCount / res.totals.streamsCount) * 100).toFixed(2),
        ),
        release_date: release?.updated_at,
      };
    });

    return new SuccessResponseDto({
      data: {
        total_streams: res.totals.streamsCount,
        total_number_of_pages: Math.ceil(res.totalItemsCount / result_per_page),
        current_page: res.pageNumber,
        result_per_page,
        total: res.totalItemsCount,
        result,
      },
    });
  }

  @httpGet(
    '/distribution/streams/top-artists',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    MIDDLEWARE_TOKENS.SubscriptionCheckMiddleware,
    autoValidate(PaginatedQueryWithDateRangeDto.validationSchema, 'query'),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
    inCategory([
      UserCategory.COMMUNITY_OWNER,
      UserCategory.CREATOR,
      UserCategory.TEAM_MEMBER,
    ]),
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
  )
  public async topArtists(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { user_id }: OptionalIdFiltersDto,
    @queryParam()
    {
      page_number = 1,
      result_per_page = 10,
      from,
      to,
      release_id,
      track_id,
    }: PaginatedQueryWithDateRangeDto,
  ) {
    const roleDesc = await this.simplifyProxyQuery(claim, user_id);

    let partnerUserId = roleDesc.revelator_id;

    const analyticsQuery: AnalyticsQuery = {
      metrics: 'trends',
      aggregation: 'byArtist',
      filters: {
        pageNumber: page_number,
        pageSize: result_per_page,
        orderByProperty: 'streamsCount',
        orderByDescending: true,
        fromDate: from,
        toDate: to,
      },
    };

    if (release_id) {
      const release = await this.release.getById(release_id);

      if (!release) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          'Invalid "release_id"',
        );
      }

      analyticsQuery.filters.releaseIds = [
        Number(release.revelator_release_id),
      ];
    }

    if (roleDesc.community_level_user) {
      partnerUserId = env.revelator_parent_account_id;

      const communityRevArtists = await this.artist.getRevArtists(
        roleDesc.community_id,
      );

      analyticsQuery.filters.artistIds = communityRevArtists.map((c) =>
        Number(c.artist_rev_id),
      );
    }

    if (track_id) {
      const track = await this.track.getById(track_id);

      if (!track) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          'Invalid "track_id"',
        );
      }

      const { result: releases } = await this.track.getTrackReleases(track_id, {
        page_number: 1,
        result_per_page: 1,
      });

      if (releases.length < 1) {
        return new SuccessResponseDto({
          data: {
            total_streams: 0,
            total_number_of_pages: 1,
            current_page: 1,
            result_per_page: 10,
            total: 0,
            result: [],
          },
        });
      }

      const releaseContent = await this.revelator.getReleaseContent(
        partnerUserId,
        Number(releases[0].revelator_release_id),
      );

      const trackContent = releaseContent.tracks.find(
        (t) => (t.wav.fileId = track.audio_metadata.file_id),
      );

      analyticsQuery.filters.trackIds = [trackContent.trackId];
    }

    const res = await this.revelator.analytics(partnerUserId, analyticsQuery);

    const revelatorArtistIdList: string[] = [];

    for (const resultItem of res.items) {
      revelatorArtistIdList.push(resultItem.metadata.artistId.toString());
    }

    let artists: Artist[];

    if (revelatorArtistIdList.length > 0) {
      artists = await this.artist.getManyByRevelatorId(revelatorArtistIdList);
    }

    const result = res.items.map((i) => {
      const artist = artists.find((a) => a.revelator_id == i.metadata.artistId);

      return {
        image: artist?.image ?? null,
        artist_name: i.metadata.artistName,
        streams: i.streamsCount,
        percentage_stream: Number(
          ((i.streamsCount / res.totals.streamsCount) * 100).toFixed(2),
        ),
      };
    });

    return new SuccessResponseDto({
      data: {
        total_streams: res.totals.streamsCount,
        total_number_of_pages: Math.ceil(res.totalItemsCount / result_per_page),
        current_page: res.pageNumber,
        result_per_page,
        total: res.totalItemsCount,
        result,
      },
    });
  }

  @httpGet(
    '/distribution/streams/top-countries',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    MIDDLEWARE_TOKENS.SubscriptionCheckMiddleware,
    autoValidate(PaginatedQueryWithDateRangeDto.validationSchema, 'query'),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
    inCategory([
      UserCategory.COMMUNITY_OWNER,
      UserCategory.CREATOR,
      UserCategory.TEAM_MEMBER,
    ]),
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
  )
  public async topStreamingCountries(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { user_id }: OptionalIdFiltersDto,
    @queryParam()
    {
      page_number,
      result_per_page,
      from,
      to,
      distributor_id,
      release_id,
      track_id,
    }: TopCountriesDto,
  ) {
    const roleDesc = await this.simplifyProxyQuery(claim, user_id);

    let partnerUserId = roleDesc.revelator_id;

    const analyticsQuery: AnalyticsQuery = {
      metrics: 'trends',
      aggregation: 'byCountry',
      filters: {
        pageNumber: page_number,
        pageSize: result_per_page,
        orderByProperty: 'streamsCount',
        orderByDescending: true,
        fromDate: from,
        toDate: to,
      },
    };

    if (distributor_id) {
      analyticsQuery.filters.distributorIds = [distributor_id];
    }

    if (release_id) {
      const release = await this.release.getById(release_id);

      if (!release) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          'Invalid "release_id"',
        );
      }

      analyticsQuery.filters.releaseIds = [
        Number(release.revelator_release_id),
      ];
    }

    if (roleDesc.community_level_user) {
      partnerUserId = env.revelator_parent_account_id;

      const communityRevArtists = await this.artist.getRevArtists(
        roleDesc.community_id,
      );

      analyticsQuery.filters.artistIds = communityRevArtists.map((c) =>
        Number(c.artist_rev_id),
      );
    }

    if (track_id) {
      const track = await this.track.getById(track_id);

      if (!track) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          'Invalid "track_id"',
        );
      }

      const { result: releases } = await this.track.getTrackReleases(track_id, {
        page_number: 1,
        result_per_page: 1,
      });

      if (releases.length < 1) {
        return new SuccessResponseDto({
          data: {
            total_streams: 0,
            total_number_of_pages: 1,
            current_page: 1,
            result_per_page: 10,
            total: 0,
            result: [],
          },
        });
      }

      const releaseContent = await this.revelator.getReleaseContent(
        partnerUserId,
        Number(releases[0].revelator_release_id),
      );

      const trackContent = releaseContent.tracks.find(
        (t) => (t.wav.fileId = track.audio_metadata.file_id),
      );

      analyticsQuery.filters.trackIds = [trackContent.trackId];
    }

    const res = await this.revelator.analytics(partnerUserId, analyticsQuery);

    const result = res.items.map((i) => {
      const country = countryPhoneCode.find(
        (c) => c.code === i.metadata.iso2Code,
      );

      return {
        country: country?.name ?? 'unknown',
        code: i.metadata.iso2Code ?? null,
        flag: country?.flag ?? null,
        streams: i.streamsCount,
        percentage_stream: Number(
          ((i.streamsCount / res.totals.streamsCount) * 100).toFixed(2),
        ),
      };
    });

    return new SuccessResponseDto({
      data: {
        total_streams: res.totals.streamsCount,
        total_number_of_pages: Math.ceil(res.totalItemsCount / result_per_page),
        current_page: res.pageNumber,
        result_per_page,
        total: res.totalItemsCount,
        result,
      },
    });
  }

  @httpGet(
    '/distribution/revenue/dsp',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    MIDDLEWARE_TOKENS.SubscriptionCheckMiddleware,
    inCategory([
      UserCategory.COMMUNITY_OWNER,
      UserCategory.CREATOR,
      UserCategory.TEAM_MEMBER,
    ]),
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
    autoValidate(CatalogAnalyticsFilterDto.validationSchema, 'query'),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
  )
  public async dspRevenueAnalytics(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { user_id }: OptionalIdFiltersDto,
    @queryParam() { release_id, track_id }: CatalogAnalyticsFilterDto,
  ) {
    const today = new Date();
    const allTime = new Date('1975-01-01');

    const prevRecordQuery: AnalyticsQuery = {
      metrics: 'revenue',
      aggregation: 'byDistributor',
      filters: {
        fromDate: allTime,
        toDate: subMonths(today, 1),
        pageNumber: 1,
        pageSize: 1,
        orderByProperty: 'netRevenue',
        orderByDescending: true,
      },
    };

    const currRecordQuery: AnalyticsQuery = {
      metrics: 'revenue',
      aggregation: 'byDistributor',
      filters: {
        fromDate: allTime,
        toDate: today,
        distributorIds: [10, 61, 114, 86, 11, 393, 85, 364],
        pageNumber: 1,
        pageSize: 8,
        orderByProperty: 'netRevenue',
        orderByDescending: true,
      },
    };

    const emptyRevenueRecords = [
      {
        distributor_id: 10,
        distributor_name: 'Spotify',
        revenue: 0,
        revenue_growth_rate: 0,
        percentage_revenue: 0,
        icon: 'https://makerverse-prod.s3.eu-west-1.amazonaws.com/assets/spotify.svg',
      },

      {
        distributor_id: 61,
        distributor_name: 'Apple Music',
        revenue: 0,
        revenue_growth_rate: 0,
        percentage_revenue: 0,
        icon: 'https://makerverse-prod.s3.eu-west-1.amazonaws.com/assets/apple-music.svg',
      },

      {
        distributor_id: 114,
        distributor_name: 'YouTube Music',
        revenue: 0,
        revenue_growth_rate: 0,
        percentage_revenue: 0,
        icon: 'https://makerverse-prod.s3.eu-west-1.amazonaws.com/assets/youtube-music.svg',
      },

      {
        distributor_id: 86,
        distributor_name: 'Tidal',
        revenue: 0,
        revenue_growth_rate: 0,
        percentage_revenue: 0,
        icon: 'https://makerverse-prod.s3.eu-west-1.amazonaws.com/assets/tidal.svg',
      },

      {
        distributor_id: 11,
        distributor_name: 'Deezer',
        revenue: 0,
        revenue_growth_rate: 0,
        percentage_revenue: 0,
        icon: 'https://makerverse-prod.s3.eu-west-1.amazonaws.com/assets/deezer.svg',
      },

      {
        distributor_id: 393,
        distributor_name: 'Audiomack',
        revenue: 0,
        revenue_growth_rate: 0,
        percentage_revenue: 0,
        icon: 'https://makerverse-prod.s3.eu-west-1.amazonaws.com/assets/audiomack.svg',
      },

      {
        distributor_id: 85,
        distributor_name: 'SoundCloud',
        revenue: 0,
        revenue_growth_rate: 0,
        percentage_revenue: 0,
        icon: 'https://makerverse-prod.s3.eu-west-1.amazonaws.com/assets/soundcloud.svg',
      },

      {
        distributor_id: 364,
        distributor_name: 'YouTube Content ID',
        revenue: 0,
        revenue_growth_rate: 0,
        percentage_revenue: 0,
        icon: 'https://makerverse-prod.s3.eu-west-1.amazonaws.com/assets/youtube.svg',
      },
    ];

    if (release_id) {
      const release = await this.release.getById(release_id);

      if (!release) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          'Invalid "release_id"',
        );
      }

      prevRecordQuery.filters.releaseIds = [
        Number(release.revelator_release_id),
      ];
      currRecordQuery.filters.releaseIds = [
        Number(release.revelator_release_id),
      ];
    }

    const roleDesc = await this.simplifyProxyQuery(claim, user_id);

    let partnerUserId = roleDesc.revelator_id;

    if (roleDesc.community_level_user) {
      partnerUserId = env.revelator_parent_account_id;

      const communityRevArtists = await this.artist.getRevArtists(
        roleDesc.community_id,
      );

      const artistIds = communityRevArtists.map((c) => Number(c.artist_rev_id));

      prevRecordQuery.filters.artistIds = artistIds;
      currRecordQuery.filters.artistIds = artistIds;
    }

    if (track_id) {
      const track = await this.track.getById(track_id);

      if (!track) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          'Invalid "track_id"',
        );
      }

      const { result: releases } = await this.track.getTrackReleases(track_id, {
        page_number: 1,
        result_per_page: 1,
      });

      if (releases.length < 1) {
        return new SuccessResponseDto({
          data: {
            total_revenue: 0,
            revenue_growth_rate: 0,
            revenue_data: emptyRevenueRecords,
          },
        });
      }

      const releaseContent = await this.revelator.getReleaseContent(
        partnerUserId,
        Number(releases[0].revelator_release_id),
      );

      const trackContent = releaseContent.tracks.find(
        (t) => (t.wav.fileId = track.audio_metadata.file_id),
      );

      prevRecordQuery.filters.trackIds = [trackContent.trackId];
      currRecordQuery.filters.trackIds = [trackContent.trackId];
    }

    const prevRecord = await this.revelator.analytics(
      partnerUserId,
      prevRecordQuery,
    );

    const currRecord = await this.revelator.analytics(
      partnerUserId,
      currRecordQuery,
    );

    const total_revenue = currRecord.totals.netRevenue;

    let revenue_growth_rate =
      prevRecord.totals.netRevenue === 0
        ? 100
        : ((currRecord.totals.netRevenue - prevRecord.totals.netRevenue) /
            prevRecord.totals.netRevenue) *
          100;

    if (
      prevRecord.totals.netRevenue === 0 &&
      currRecord.totals.netRevenue === 0
    ) {
      revenue_growth_rate = 0;
    } else {
      revenue_growth_rate = Number(revenue_growth_rate.toFixed(4));
    }

    const revenue_data: typeof emptyRevenueRecords = [];

    for (const dataIdx in emptyRevenueRecords) {
      const emptyRecord = emptyRevenueRecords[dataIdx];

      const revenueRecord = currRecord.items.find(
        (i) => i.metadata.distributorId === emptyRecord.distributor_id,
      );

      if (!revenueRecord) {
        revenue_data.push(emptyRecord);
        continue;
      }

      const prevRevenueData = prevRecord.items.find(
        (pi) =>
          pi.metadata.distributorId === revenueRecord.metadata.distributorId,
      );

      const prevNetRevenue = prevRevenueData?.netRevenue ?? 0;

      const revenue_growth_rate =
        prevNetRevenue === 0 && revenueRecord.netRevenue !== 0
          ? 100
          : ((revenueRecord.netRevenue - prevNetRevenue) / prevNetRevenue) *
            100;

      revenue_data.push({
        distributor_id: revenueRecord.metadata.distributorId,
        distributor_name: revenueRecord.metadata.distributorName,
        revenue: revenueRecord.netRevenue,
        revenue_growth_rate: Number(revenue_growth_rate.toFixed(4)),
        percentage_revenue: Number(
          (
            (revenueRecord.netRevenue / currRecord.totals.netRevenue) *
            100
          ).toFixed(4),
        ),
        icon: distributorIds.find(
          (d) => d.distributor_id === revenueRecord.metadata.distributorId,
        )?.icon,
      });

      if (revenue_data.length === emptyRevenueRecords.length) {
        revenue_data.sort((a, b) => b.revenue - a.revenue);
      }
    }

    revenue_data.sort((a, b) => b.revenue - a.revenue);

    return new SuccessResponseDto({
      data: {
        total_revenue,
        revenue_growth_rate,
        revenue_data,
      },
    });
  }

  @httpGet(
    '/distribution/revenue/top-dsp',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    MIDDLEWARE_TOKENS.SubscriptionCheckMiddleware,
    autoValidate(PaginatedQueryWithDateRangeDto.validationSchema, 'query'),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
    inCategory([
      UserCategory.COMMUNITY_OWNER,
      UserCategory.CREATOR,
      UserCategory.TEAM_MEMBER,
    ]),
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
  )
  public async topSoldToDsp(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { user_id }: OptionalIdFiltersDto,
    @queryParam()
    {
      page_number = 1,
      result_per_page = 10,
      from = new Date('1975-01-01'),
      to = new Date(),
      release_id,
      track_id,
    }: PaginatedQueryWithDateRangeDto,
  ) {
    const analyticsQuery: AnalyticsQuery = {
      metrics: 'revenue',
      aggregation: 'byDistributor',
      filters: {
        pageNumber: page_number,
        pageSize: result_per_page,
        orderByProperty: 'netRevenue',
        orderByDescending: true,
        fromDate: from,
        toDate: to,
      },
    };

    if (release_id) {
      const release = await this.release.getById(release_id);

      if (!release) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          'Invalid "release_id"',
        );
      }

      analyticsQuery.filters.releaseIds = [
        Number(release.revelator_release_id),
      ];
    }

    const roleDesc = await this.simplifyProxyQuery(claim, user_id);

    let partnerUserId = roleDesc.revelator_id;

    if (roleDesc.community_level_user) {
      partnerUserId = env.revelator_parent_account_id;

      const communityRevArtists = await this.artist.getRevArtists(
        roleDesc.community_id,
      );

      analyticsQuery.filters.artistIds = communityRevArtists.map((c) =>
        Number(c.artist_rev_id),
      );
    }

    if (track_id) {
      const track = await this.track.getById(track_id);

      if (!track) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          'Invalid "track_id"',
        );
      }

      const { result: releases } = await this.track.getTrackReleases(track_id, {
        page_number: 1,
        result_per_page: 1,
      });

      if (releases.length > 0) {
        return new SuccessResponseDto({
          data: {
            total_revenue: 0,
            total_number_of_pages: 1,
            current_page: 1,
            result_per_page: 10,
            total: 0,
            result: [],
          },
        });
      }

      const releaseContent = await this.revelator.getReleaseContent(
        partnerUserId,
        Number(releases[0].revelator_release_id),
      );

      const trackContent = releaseContent.tracks.find(
        (t) => (t.wav.fileId = track.audio_metadata.file_id),
      );

      analyticsQuery.filters.trackIds = [trackContent.trackId];
    }

    const res = await this.revelator.analytics(partnerUserId, analyticsQuery);

    const result = res.items.map((i) => ({
      distributor_id: i.metadata.distributorId,
      distributor_name: i.metadata.distributorName,
      streams: i.streamsCount,
      revenue: i.netRevenue,
    }));

    return new SuccessResponseDto({
      data: {
        total_revenue: res.totals.netRevenue,
        total_number_of_pages: Math.ceil(res.totalItemsCount / result_per_page),
        current_page: res.pageNumber,
        result_per_page,
        total: res.totalItemsCount,
        result,
      },
    });
  }

  @httpGet(
    '/distribution/revenue/top-tracks',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    MIDDLEWARE_TOKENS.SubscriptionCheckMiddleware,
    autoValidate(PaginatedQueryWithDateRangeDto.validationSchema, 'query'),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
    inCategory([
      UserCategory.COMMUNITY_OWNER,
      UserCategory.CREATOR,
      UserCategory.TEAM_MEMBER,
    ]),
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
  )
  public async topSellingTracks(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { user_id }: OptionalIdFiltersDto,
    @queryParam()
    {
      page_number = 1,
      result_per_page = 10,
      from = new Date('1975-01-01'),
      to = new Date(),
      release_id,
    }: PaginatedQueryWithDateRangeDto,
  ) {
    const analyticsQuery: AnalyticsQuery = {
      metrics: 'revenue',
      aggregation: 'byTrack',
      filters: {
        pageNumber: page_number,
        pageSize: result_per_page,
        orderByProperty: 'netRevenue',
        orderByDescending: true,
        fromDate: from,
        toDate: to,
      },
    };

    if (release_id) {
      const release = await this.release.getById(release_id);

      if (!release) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          'Invalid "release_id"',
        );
      }

      analyticsQuery.filters.releaseIds = [
        Number(release.revelator_release_id),
      ];
    }

    const roleDesc = await this.simplifyProxyQuery(claim, user_id);

    let partnerUserId = roleDesc.revelator_id;

    if (roleDesc.community_level_user) {
      partnerUserId = env.revelator_parent_account_id;

      const communityRevArtists = await this.artist.getRevArtists(
        roleDesc.community_id,
      );

      analyticsQuery.filters.artistIds = communityRevArtists.map((c) =>
        Number(c.artist_rev_id),
      );
    }

    const res = await this.revelator.analytics(partnerUserId, analyticsQuery);

    const revelatorIdList: string[] = [];

    for (const resultItem of res.items) {
      revelatorIdList.push(resultItem.metadata.releaseId.toString());
    }

    let releases: Release[];

    if (revelatorIdList.length > 0) {
      releases = await this.release.findManyWithRevelatorId(revelatorIdList);
    }

    const result = res.items.map((i) => {
      const release = releases.find(
        (r) => r.revelator_release_id === i.metadata.releaseId,
      );

      return {
        cover_art: release?.cover_art ?? null,
        release_name: i.metadata.releaseName,
        track_name: i.metadata.trackName,
        artist_name: i.metadata.artistName,
        revenue: i.netRevenue,
        percentage_revenue: Number(
          ((i.netRevenue / res.totals.netRevenue) * 100).toFixed(2),
        ),
        release_date: release?.updated_at,
      };
    });

    return new SuccessResponseDto({
      data: {
        total_revenue: res.totals.netRevenue,
        total_number_of_pages: Math.ceil(res.totalItemsCount / result_per_page),
        current_page: res.pageNumber,
        result_per_page,
        total: res.totalItemsCount,
        result,
      },
    });
  }

  @httpGet(
    '/distribution/revenue/top-releases',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    MIDDLEWARE_TOKENS.SubscriptionCheckMiddleware,
    autoValidate(PaginatedQueryWithDateRangeDto.validationSchema, 'query'),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
    inCategory([
      UserCategory.COMMUNITY_OWNER,
      UserCategory.CREATOR,
      UserCategory.TEAM_MEMBER,
    ]),
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
  )
  public async topSellingReleases(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { user_id }: OptionalIdFiltersDto,
    @queryParam()
    {
      page_number = 1,
      result_per_page = 10,
      from = new Date('1975-01-01'),
      to = new Date(),
      track_id,
    }: PaginatedQueryWithDateRangeDto,
  ) {
    const analyticsQuery: AnalyticsQuery = {
      metrics: 'revenue',
      aggregation: 'byRelease',
      filters: {
        pageNumber: page_number,
        pageSize: result_per_page,
        orderByProperty: 'netRevenue',
        orderByDescending: true,
        fromDate: from,
        toDate: to,
      },
    };

    const roleDesc = await this.simplifyProxyQuery(claim, user_id);

    let partnerUserId = roleDesc.revelator_id;

    if (roleDesc.community_level_user) {
      partnerUserId = env.revelator_parent_account_id;

      const communityRevArtists = await this.artist.getRevArtists(
        roleDesc.community_id,
      );

      analyticsQuery.filters.artistIds = communityRevArtists.map((c) =>
        Number(c.artist_rev_id),
      );
    }

    if (track_id) {
      const track = await this.track.getById(track_id);

      if (!track) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          'Invalid "track_id"',
        );
      }

      const { result: releases } = await this.track.getTrackReleases(track_id, {
        page_number: 1,
        result_per_page: 1,
      });

      if (releases.length > 0) {
        return new SuccessResponseDto({
          data: {
            total_revenue: 0,
            total_number_of_pages: 1,
            current_page: 1,
            result_per_page: 10,
            total: 0,
            result: [],
          },
        });
      }

      const releaseContent = await this.revelator.getReleaseContent(
        partnerUserId,
        Number(releases[0].revelator_release_id),
      );

      const trackContent = releaseContent.tracks.find(
        (t) => (t.wav.fileId = track.audio_metadata.file_id),
      );

      analyticsQuery.filters.trackIds = [trackContent.trackId];
    }

    const res = await this.revelator.analytics(partnerUserId, analyticsQuery);

    const revelatorIdList: string[] = [];

    for (const resultItem of res.items) {
      revelatorIdList.push(resultItem.metadata.releaseId.toString());
    }

    let releases: Release[];

    if (revelatorIdList.length > 0) {
      releases = await this.release.findManyWithRevelatorId(revelatorIdList);
    }

    const result = res.items.map((i) => {
      const release = releases.find(
        (r) => r.revelator_release_id === i.metadata.releaseId,
      );

      return {
        cover_art: release?.cover_art ?? null,
        release_name: i.metadata.releaseName,
        artist_name: i.metadata.artistName,
        revenue: i.netRevenue,
        percentage_revenue: Number(
          ((i.netRevenue / res.totals.netRevenue) * 100).toFixed(2),
        ),
        release_date: release?.updated_at,
      };
    });

    return new SuccessResponseDto({
      data: {
        total_revenue: res.totals.netRevenue,
        total_number_of_pages: Math.ceil(res.totalItemsCount / result_per_page),
        current_page: res.pageNumber,
        result_per_page,
        total: res.totalItemsCount,
        result,
      },
    });
  }

  @httpGet(
    '/distribution/revenue/top-artists',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    MIDDLEWARE_TOKENS.SubscriptionCheckMiddleware,
    autoValidate(PaginatedQueryWithDateRangeDto.validationSchema, 'query'),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
    inCategory([
      UserCategory.COMMUNITY_OWNER,
      UserCategory.CREATOR,
      UserCategory.TEAM_MEMBER,
    ]),
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
  )
  public async topSellingArtists(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { user_id }: OptionalIdFiltersDto,
    @queryParam()
    {
      page_number = 1,
      result_per_page = 10,
      from = new Date('1975-01-01'),
      to = new Date(),
      release_id,
      track_id,
    }: PaginatedQueryWithDateRangeDto,
  ) {
    const analyticsQuery: AnalyticsQuery = {
      metrics: 'revenue',
      aggregation: 'byArtist',
      filters: {
        pageNumber: page_number,
        pageSize: result_per_page,
        orderByProperty: 'netRevenue',
        orderByDescending: true,
        fromDate: from,
        toDate: to,
      },
    };

    const roleDesc = await this.simplifyProxyQuery(claim, user_id);

    let partnerUserId = roleDesc.revelator_id;

    if (roleDesc.community_level_user) {
      partnerUserId = env.revelator_parent_account_id;

      const communityRevArtists = await this.artist.getRevArtists(
        roleDesc.community_id,
      );

      analyticsQuery.filters.artistIds = communityRevArtists.map((c) =>
        Number(c.artist_rev_id),
      );
    }

    if (release_id) {
      const release = await this.release.getById(release_id);

      if (!release) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          'Invalid "release_id"',
        );
      }

      analyticsQuery.filters.releaseIds = [
        Number(release.revelator_release_id),
      ];
    }

    if (track_id) {
      const track = await this.track.getById(track_id);

      if (!track) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          'Invalid "track_id"',
        );
      }

      const { result: releases } = await this.track.getTrackReleases(track_id, {
        page_number: 1,
        result_per_page: 1,
      });

      if (releases.length > 0) {
        return new SuccessResponseDto({
          data: {
            total_revenue: 0,
            total_number_of_pages: 1,
            current_page: 1,
            result_per_page: 10,
            total: 0,
            result: [],
          },
        });
      }

      const releaseContent = await this.revelator.getReleaseContent(
        partnerUserId,
        Number(releases[0].revelator_release_id),
      );

      const trackContent = releaseContent.tracks.find(
        (t) => (t.wav.fileId = track.audio_metadata.file_id),
      );

      analyticsQuery.filters.trackIds = [trackContent.trackId];
    }

    const res = await this.revelator.analytics(partnerUserId, analyticsQuery);

    const revelatorArtistIdList: string[] = [];

    for (const resultItem of res.items) {
      revelatorArtistIdList.push(resultItem.metadata.artistId.toString());
    }

    let artists: Artist[];

    if (revelatorArtistIdList.length > 0) {
      artists = await this.artist.getManyByRevelatorId(revelatorArtistIdList);
    }

    const result = res.items.map((i) => {
      const artist = artists.find((a) => a.revelator_id == i.metadata.artistId);

      return {
        image: artist?.image ?? null,
        artist_name: i.metadata.artistName,
        revenue: i.netRevenue,
        percentage_revenue: Number(
          ((i.netRevenue / res.totals.netRevenue) * 100).toFixed(2),
        ),
      };
    });

    return new SuccessResponseDto({
      data: {
        total_revenue: res.totals.netRevenue,
        total_number_of_pages: Math.ceil(res.totalItemsCount / result_per_page),
        current_page: res.pageNumber,
        result_per_page,
        total: res.totalItemsCount,
        result,
      },
    });
  }

  @httpGet(
    '/distribution/revenue/top-countries',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    MIDDLEWARE_TOKENS.SubscriptionCheckMiddleware,
    autoValidate(PaginatedQueryWithDateRangeDto.validationSchema, 'query'),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
    inCategory([
      UserCategory.COMMUNITY_OWNER,
      UserCategory.CREATOR,
      UserCategory.TEAM_MEMBER,
    ]),
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
  )
  public async topProfitableCountries(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { user_id }: OptionalIdFiltersDto,
    @queryParam()
    {
      page_number = 1,
      result_per_page = 10,
      from = new Date('1975-01-01'),
      to = new Date(),
      distributor_id,
      release_id,
      track_id,
    }: TopCountriesDto,
  ) {
    const analyticsQuery: AnalyticsQuery = {
      metrics: 'revenue',
      aggregation: 'byCountry',
      filters: {
        pageNumber: page_number,
        pageSize: result_per_page,
        orderByProperty: 'netRevenue',
        orderByDescending: true,
        fromDate: from,
        toDate: to,
      },
    };

    if (distributor_id) {
      analyticsQuery.filters.distributorIds = [distributor_id];
    }

    if (release_id) {
      const release = await this.release.getById(release_id);

      if (!release) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          'Invalid "release_id"',
        );
      }

      analyticsQuery.filters.releaseIds = [
        Number(release.revelator_release_id),
      ];
    }

    const roleDesc = await this.simplifyProxyQuery(claim, user_id);

    let partnerUserId = roleDesc.revelator_id;

    if (roleDesc.community_level_user) {
      partnerUserId = env.revelator_parent_account_id;

      const communityRevArtists = await this.artist.getRevArtists(
        roleDesc.community_id,
      );

      analyticsQuery.filters.artistIds = communityRevArtists.map((c) =>
        Number(c.artist_rev_id),
      );
    }

    if (track_id) {
      const track = await this.track.getById(track_id);

      if (!track) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          'Invalid "track_id"',
        );
      }

      const { result: releases } = await this.track.getTrackReleases(track_id, {
        page_number: 1,
        result_per_page: 1,
      });

      if (releases.length > 0) {
        return new SuccessResponseDto({
          data: {
            total_revenue: 0,
            total_number_of_pages: 1,
            current_page: 1,
            result_per_page: 10,
            total: 0,
            result: [],
          },
        });
      }

      const releaseContent = await this.revelator.getReleaseContent(
        partnerUserId,
        Number(releases[0].revelator_release_id),
      );

      const trackContent = releaseContent.tracks.find(
        (t) => (t.wav.fileId = track.audio_metadata.file_id),
      );

      analyticsQuery.filters.trackIds = [trackContent.trackId];
    }

    const res = await this.revelator.analytics(partnerUserId, analyticsQuery);

    const result = res.items.map((i) => {
      const country = countryPhoneCode.find(
        (c) => c.code === i.metadata.iso2Code,
      );

      return {
        country: country?.name ?? 'unknown',
        code: i.metadata.iso2Code ?? null,
        flag: country?.flag ?? null,
        revenue: i.netRevenue,
        percentage_revenue: Number(
          ((i.netRevenue / res.totals.netRevenue) * 100).toFixed(2),
        ),
      };
    });

    return new SuccessResponseDto({
      data: {
        total_revenue: res.totals.netRevenue,
        total_number_of_pages: Math.ceil(res.totalItemsCount / result_per_page),
        current_page: res.pageNumber,
        result_per_page,
        total: res.totalItemsCount,
        result,
      },
    });
  }

  @httpGet(
    '/demographic',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    hasPermissions([
      {
        resource: PermissionResource.Analytics,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
  )
  public async demographic(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { user_id }: OptionalIdFiltersDto,
  ) {
    const userDesc = await this.simplifyProxyQuery(claim, user_id);

    let demographic: Record<string, any>;

    if (userDesc.community_level_user) {
      demographic = await this.community.demographicBreakdown(
        userDesc.community_id,
      );
    } else {
      demographic = await this.user.demographicBreakdown(
        userDesc.team_owner?.id ?? userDesc.id,
      );
    }

    return new SuccessResponseDto({ data: demographic });
  }

  @httpGet(
    '/subscription/total',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([UserCategory.COMMUNITY_OWNER, UserCategory.TEAM_MEMBER]),
    hasPermissions([
      {
        resource: PermissionResource.ClientManagement,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
  )
  public async communitySubPlanTotalityAnalytics(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { user_id }: OptionalIdFiltersDto,
  ) {
    const roleDesc = await this.simplifyProxyQuery(claim, user_id);

    if (!roleDesc.community_level_user) {
      throw new ApplicationError(StatusCodes.FORBIDDEN, 'Forbidden');
    }

    const data = await this.analytics.communitySubPlanTotalityAnalytics({
      community_id: roleDesc.community_id,
    });

    return new SuccessResponseDto({ data });
  }

  @httpGet(
    '/subscription/stats',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([UserCategory.COMMUNITY_OWNER, UserCategory.TEAM_MEMBER]),
    hasPermissions([
      {
        resource: PermissionResource.ClientManagement,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
    autoValidate(CommunitySubscriberStatSummaryDto.validationSchema, 'query'),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
  )
  public async communitySubscriberStatSummary(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { from, to }: CommunitySubscriberStatSummaryDto,
    @queryParam() { user_id }: OptionalIdFiltersDto,
  ) {
    const roleDesc = await this.simplifyProxyQuery(claim, user_id);

    if (!roleDesc.community_level_user) {
      throw new ApplicationError(StatusCodes.FORBIDDEN, 'Forbidden');
    }

    const today = new Date();

    const lastMonth = subMonths(today, 1);

    const data = await this.analytics.communitySubscriberStatSummary({
      community_id: roleDesc.community_id,
      from: from ?? today,
      to: to ?? lastMonth,
    });

    return new SuccessResponseDto({ data });
  }

  @httpGet(
    '/subscription/growth-trend',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([UserCategory.COMMUNITY_OWNER, UserCategory.TEAM_MEMBER]),
    hasPermissions([
      {
        resource: PermissionResource.ClientManagement,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
    autoValidate(SubscriberGrowthTrendDto.validationSchema, 'query'),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
  )
  public async subscriberGrowthTrend(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam()
    {
      from,
      to,
      interval,
      plan_id,
      status,
      plan_interval,
    }: SubscriberGrowthTrendDto,
    @queryParam() { user_id }: OptionalIdFiltersDto,
  ) {
    const roleDesc = await this.simplifyProxyQuery(claim, user_id);

    if (!roleDesc.community_level_user) {
      throw new ApplicationError(StatusCodes.FORBIDDEN, 'Forbidden');
    }

    const today = new Date();

    const lastMonth = subMonths(today, 1);

    const data = await this.analytics.subscriberGrowthTrend({
      community_id: roleDesc.community_id,
      start_date: from ?? today,
      end_date: to ?? lastMonth,
      interval,
      plan_interval,
      plan_id,
      status,
    });

    return new SuccessResponseDto({ data });
  }

  @httpGet(
    '/subscription/demographics',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([UserCategory.COMMUNITY_OWNER, UserCategory.TEAM_MEMBER]),
    hasPermissions([
      {
        resource: PermissionResource.ClientManagement,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
    autoValidate(SubscriberDemographicsDto.validationSchema, 'query'),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
  )
  public async subscriberDemographics(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam()
    { plan_id, status }: SubscriberDemographicsDto,
    @queryParam() { user_id }: OptionalIdFiltersDto,
  ) {
    const roleDesc = await this.simplifyProxyQuery(claim, user_id);

    if (!roleDesc.community_level_user) {
      throw new ApplicationError(StatusCodes.FORBIDDEN, 'Forbidden');
    }

    const data = await this.analytics.subscriberDemographics({
      community_id: roleDesc.community_id,
      plan_id,
      status,
    });

    return new SuccessResponseDto({ data });
  }

  @httpGet(
    '/subscription/countries',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([UserCategory.COMMUNITY_OWNER, UserCategory.TEAM_MEMBER]),
    hasPermissions([
      {
        resource: PermissionResource.ClientManagement,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
    autoValidate(SubscriberCountryAnalyticsDto.validationSchema, 'query'),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
  )
  public async subscriberCountryAnalytics(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam()
    { plan_id, status }: SubscriberCountryAnalyticsDto,
    @queryParam() { user_id }: OptionalIdFiltersDto,
  ) {
    const roleDesc = await this.simplifyProxyQuery(claim, user_id);

    if (!roleDesc.community_level_user) {
      throw new ApplicationError(StatusCodes.FORBIDDEN, 'Forbidden');
    }

    const data = await this.analytics.subscriberCountryAnalytics({
      community_id: roleDesc.community_id,
      plan_id,
      status,
    });

    return new SuccessResponseDto({ data });
  }

  @httpGet(
    '/subscription/revenue/total',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([UserCategory.COMMUNITY_OWNER, UserCategory.TEAM_MEMBER]),
    hasPermissions([
      {
        resource: PermissionResource.ClientManagement,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
  )
  public async communitySubcriptionRevenueTotalityAnalytics(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { user_id }: OptionalIdFiltersDto,
  ) {
    const roleDesc = await this.simplifyProxyQuery(claim, user_id);

    if (!roleDesc.community_level_user) {
      throw new ApplicationError(StatusCodes.FORBIDDEN, 'Forbidden');
    }

    const data = await this.analytics.communitySubRevenueTotalityAnalytics({
      community_id: roleDesc.community_id,
    });

    return new SuccessResponseDto({ data });
  }

  @httpGet(
    '/subscription/revenue/growth-trend',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([UserCategory.COMMUNITY_OWNER, UserCategory.TEAM_MEMBER]),
    hasPermissions([
      {
        resource: PermissionResource.ClientManagement,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
    autoValidate(SubscriberGrowthTrendDto.validationSchema, 'query'),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
  )
  public async subscriptionRevenueGrowthTrend(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam()
    {
      from,
      to,
      interval,
      plan_id,
      status,
      plan_interval,
    }: SubscriberGrowthTrendDto,
    @queryParam() { user_id }: OptionalIdFiltersDto,
  ) {
    const roleDesc = await this.simplifyProxyQuery(claim, user_id);

    if (!roleDesc.community_level_user) {
      throw new ApplicationError(StatusCodes.FORBIDDEN, 'Forbidden');
    }

    const today = new Date();

    const lastMonth = subMonths(today, 1);

    const data = await this.analytics.subscriptionRevenueGrowthTrend({
      community_id: roleDesc.community_id,
      start_date: from ?? today,
      end_date: to ?? lastMonth,
      interval,
      plan_interval,
      plan_id,
      status,
    });

    return new SuccessResponseDto({ data });
  }

  @httpGet(
    '/subscription/revenue/countries',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([UserCategory.COMMUNITY_OWNER, UserCategory.TEAM_MEMBER]),
    hasPermissions([
      {
        resource: PermissionResource.ClientManagement,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
    autoValidate(SubscriberCountryAnalyticsDto.validationSchema, 'query'),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
  )
  public async subscriptionRevenueCountryAnalytics(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam()
    { plan_id, status }: SubscriberCountryAnalyticsDto,
    @queryParam() { user_id }: OptionalIdFiltersDto,
  ) {
    const roleDesc = await this.simplifyProxyQuery(claim, user_id);

    if (!roleDesc.community_level_user) {
      throw new ApplicationError(StatusCodes.FORBIDDEN, 'Forbidden');
    }

    const data = await this.analytics.subscriptionRevenueCountryAnalytics({
      community_id: roleDesc.community_id,
      plan_id,
      status,
    });

    return new SuccessResponseDto({ data });
  }

  private async simplifyProxyQuery(claim: Claim, user_id?: string) {
    const roleDesc = await this.user.describe(claim.team_id ?? claim.id);

    const isProxyRequest =
      user_id && (claim.admin_auth || roleDesc?.community_level_user);

    let userDesc: UserDescription;

    if (isProxyRequest) {
      userDesc = await this.user.describe(user_id);

      if (
        !userDesc ||
        (!claim.admin_auth && userDesc.community_id !== claim.community_id)
      ) {
        throw new ApplicationError(StatusCodes.NOT_FOUND, 'User not found');
      }
    } else if (!isProxyRequest && claim.admin_auth) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        "'user_id' is required",
      );
    } else {
      userDesc = roleDesc;
    }

    return userDesc;
  }
}
