import { ExtendedJoi } from '@app/utils/joi-utils';

export enum PeriodFilter {
  last_week = 'last_week',
  last_month = 'last_month',
  last_3_months = 'last_3_months',
  last_6_months = 'last_6_months',
  last_year = 'last_year',
  all_time = 'all_time',
}

export class PeriodDto {
  period: PeriodFilter;
  from: Date;
  to: Date;

  static validationSchema = ExtendedJoi.object<PeriodDto, true>({
    period: ExtendedJoi.string()
      .valid(...Object.values(PeriodFilter))
      .default(PeriodFilter.all_time),
    from: ExtendedJoi.date().format('YYYY-MM-DD').optional(),
    to: ExtendedJoi.date().when('from', {
      is: ExtendedJoi.exist(),
      then: ExtendedJoi.date()
        .format('YYYY-MM-DD')
        .min(ExtendedJoi.ref('from'))
        .required(),
    }),
  });
}
