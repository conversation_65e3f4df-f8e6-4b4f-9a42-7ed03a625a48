import { autoValidate } from '@app/http/middlewares/validation.middleware';
import {
  MIDD<PERSON>WARE_TOKENS,
  MODULE_TOKENS,
  SERVICE_TOKENS,
} from '@app/internal/ioc/tokens';
import { TrackService } from '@app/services/track/track.service';
import { inject } from 'inversify';
import {
  controller,
  httpGet,
  httpPost,
  queryParam,
  request as httpReq,
  requestBody,
  requestParam,
} from 'inversify-express-utils';
import { inCategory } from '@app/http/middlewares/user-category.middleware';
import { UserCategory } from '@app/services/user/entities/user.entity';
import {
  applyFileFilter,
  FileMiddleware,
  fileTypeFilter,
} from '@app/http/middlewares/file.middleware';
import {
  RequestWithAudioMetaData,
  RequestWithClaims,
} from '@app/internal/types';
import { SuccessResponseDto } from '@app/http/dtos/http-response.dto';
import { S3 } from '@app/modules/s3';
import { Track } from '@app/services/track/entities/track.entity';
import { TrackDraftService } from '@app/services/track/track-draft.service';
import { IdDto, OptionalIdDto } from '@app/http/dtos/id.dto';
import { ApplicationError } from '@app/internal/errors';
import { StatusCodes } from 'http-status-codes';
import { DURATION, FileSize } from '@app/internal/enums';
import { AddTrackDetailsDto } from './dto/add-track-details.dto';
import { ArtistService } from '@app/services/artist/artist.service';
import { SubmitTrackDto } from './dto/submit-track.dto';
import { Revelator } from '@app/modules/revelator';
import * as path from 'path';
import { ulid } from 'ulid';
import { captureAudioMetadata } from '@app/http/middlewares/capture-file-metadata.middleware';
import { extractFilename } from '@app/utils/file.utils';
import { FetchTracksDto } from './dto/fetch-tracks.dto';
import { ListTracksDto } from './dto/list-tracks.dto';
import { UserService } from '@app/services/user/user.service';
import { hasPermissions } from '@app/http/middlewares/permissions.middleware';
import { PermissionResource } from '@app/utils/permissions.utils';
import { OffsetPaginationResult } from '@app/internal/postgres/pagination';

@controller('/track')
export class TrackController {
  constructor(
    @inject(SERVICE_TOKENS.ArtistService)
    private readonly artist: ArtistService,
    @inject(SERVICE_TOKENS.TrackDraftService)
    private readonly trackDraft: TrackDraftService,
    @inject(SERVICE_TOKENS.TrackService)
    private readonly track: TrackService,
    @inject(MODULE_TOKENS.S3) private readonly s3: S3,
    @inject(MODULE_TOKENS.Revelator)
    private readonly revelator: Revelator,
    @inject(SERVICE_TOKENS.UserService) private readonly user: UserService,
  ) {}

  @httpGet(
    '/',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([
      UserCategory.COMMUNITY_OWNER,
      UserCategory.TEAM_MEMBER,
      UserCategory.CREATOR,
    ]),
    hasPermissions([
      {
        resource: PermissionResource.Distribution,
        accessLevel: ['viewer', 'editor'],
      },
      {
        resource: PermissionResource.CatalogManagement,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
    autoValidate(ListTracksDto.validationSchema, 'query'),
  )
  public async listTracks(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam()
    { search_term, release_id, page_number, result_per_page }: ListTracksDto,
  ) {
    const roleDesc = await this.user.describe(claim.id);

    let tracks: OffsetPaginationResult<Track>;

    if (roleDesc.community_level_user) {
      tracks = await this.track.listTracks({
        release_id,
        page_number,
        result_per_page,
        search_term,
        community_id: claim.community_id,
      });
    } else {
      tracks = await this.track.listTracks({
        release_id,
        page_number,
        result_per_page,
        search_term,
        user_id: claim?.team_id ?? claim.id,
      });
    }

    return new SuccessResponseDto({ data: tracks });
  }

  @httpGet(
    '/community/creator/:id',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([UserCategory.COMMUNITY_OWNER, UserCategory.TEAM_MEMBER]),
    hasPermissions([
      {
        resource: PermissionResource.Distribution,
        accessLevel: ['editor', 'viewer'],
      },
      {
        resource: PermissionResource.CatalogManagement,
        accessLevel: ['editor', 'viewer'],
      },
    ]),
    autoValidate(IdDto.validationSchema, 'params'),
    autoValidate(FetchTracksDto.validationSchema, 'query'),
  )
  public async getCommunityCreatorTracks(
    @requestParam() { id }: IdDto,
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { page_number, result_per_page, release_id }: FetchTracksDto,
  ) {
    const creator = await this.user.getById(id);

    if (
      !creator ||
      creator?.community_id !== claim.community_id ||
      creator?.category !== UserCategory.CREATOR
    ) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Creator not found');
    }

    const tracks = await this.track.getCreatorTracks(id, {
      page_number,
      result_per_page,
      release_id,
    });

    return new SuccessResponseDto({ data: tracks });
  }

  @httpGet(
    '/drafts',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([UserCategory.CREATOR, UserCategory.TEAM_MEMBER]),
    hasPermissions([
      {
        resource: PermissionResource.Distribution,
        accessLevel: ['editor'],
      },
      {
        resource: PermissionResource.CatalogManagement,
        accessLevel: ['editor'],
      },
    ]),
  )
  public async getTrackDrafts(@httpReq() { claim }: RequestWithClaims) {
    const drafts = await this.trackDraft.getDrafts(claim?.team_id ?? claim.id);

    return new SuccessResponseDto({ data: drafts });
  }

  @httpPost(
    '/new/upload-audio',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    MIDDLEWARE_TOKENS.SubscriptionCheckMiddleware,
    autoValidate(OptionalIdDto.validationSchema, 'query'),
    inCategory([UserCategory.CREATOR, UserCategory.TEAM_MEMBER]),
    hasPermissions([
      {
        resource: PermissionResource.Distribution,
        accessLevel: ['editor'],
      },
      {
        resource: PermissionResource.CatalogManagement,
        accessLevel: ['editor'],
      },
    ]),
    FileMiddleware(
      'single',
      { field_name: 'track_audio' },
      {
        limits: { fileSize: 100 * FileSize.MB },
        fileFilter: applyFileFilter([fileTypeFilter(['.wav'])]),
      },
    ),
    captureAudioMetadata,
  )
  public async uploadNewTracksAudio(
    @httpReq() req: RequestWithClaims & RequestWithAudioMetaData,
    @queryParam() payload: OptionalIdDto,
  ) {
    const { claim, file, audioMetadata } = req;

    if (!file) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        "'track_audio' file is required",
      );
    }

    const user = await this.user.getById(claim?.team_id ?? claim.id);

    const draft = new Track({
      id: payload.id || ulid(),
      user_id: claim?.team_id ?? claim.id,
    });

    const fileExtension = path.extname(file.originalname);

    const timestampedFilename = `${extractFilename(
      file.originalname,
    )}_${new Date().toISOString()}${fileExtension}`;

    const url = await this.s3.upload({
      Body: file.buffer,
      Key: `${user.id}/track/audio/${timestampedFilename}`,
      ContentType: file.mimetype,
    });

    const revelatorTrackUpload = await this.revelator.uploadTrackAudio(
      user.revelator_id,
      user.email,
      user.enterprise_name,
      {
        filename: timestampedFilename,
        externalUrl: url,
      },
    );

    draft.bit_rate = audioMetadata.format.bitrate * DURATION.SECONDS;
    draft.bit_depth = audioMetadata.format.bitsPerSample;
    draft.sample_rate = audioMetadata.format.sampleRate * DURATION.SECONDS;
    draft.duration = audioMetadata.format.duration * DURATION.SECONDS;
    draft.channels = audioMetadata.format.numberOfChannels;

    draft.audio = url;

    draft.audio_metadata = {
      file_id: revelatorTrackUpload.fileId,
      filename: timestampedFilename,
      file_size: file.size,
    };

    await this.trackDraft.save(draft);

    return new SuccessResponseDto({ data: draft });
  }

  @httpPost(
    '/new/details/:id',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    MIDDLEWARE_TOKENS.SubscriptionCheckMiddleware,
    autoValidate(IdDto.validationSchema, 'params'),
    autoValidate(AddTrackDetailsDto.validationSchema),
    inCategory([UserCategory.CREATOR, UserCategory.TEAM_MEMBER]),
    hasPermissions([
      {
        resource: PermissionResource.Distribution,
        accessLevel: ['editor'],
      },
      {
        resource: PermissionResource.CatalogManagement,
        accessLevel: ['editor'],
      },
    ]),
  )
  public async addTrackDetails(
    @httpReq() { claim }: RequestWithClaims,
    @requestParam() { id }: IdDto,
    @requestBody() payload: AddTrackDetailsDto,
  ) {
    const draft = await this.trackDraft.getDraft(
      id,
      claim?.team_id ?? claim.id,
    );

    if (!draft) {
      throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Invalid draft id');
    }

    const {
      artists,
      preview,
      origin,
      track_properties,
      localized_titles,
      version,
      catalog_id,
      lyrics,
      isrc,

      ...rest
    } = payload;

    if (payload.secondary_genre && !payload.primary_genre) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'Primary Genre is required when Secondary Genre is present',
      );
    }

    let idx = 0;

    for (const artist of artists) {
      const artistInDb = await this.artist.get(
        artist.id,
        claim?.team_id ?? claim.id,
      );

      if (!artistInDb) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          `Invalid artist id at artists[${idx}]`,
        );
      }

      idx++;
    }

    localized_titles?.forEach((local) => {
      local.version = local.version || undefined;

      const versionPresent = !['', null, undefined].includes(version);
      const localVersionPresent = !['', null, undefined].includes(
        local.version,
      );

      if (versionPresent && !localVersionPresent) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          'Version on your local must be present since you are creating a track version',
        );
      } else if (!versionPresent && localVersionPresent) {
        local.version = undefined;
      }
    });

    Object.assign(draft, rest, {
      start_time: preview,
      type: origin,
      properties: track_properties?.includes(1) ? [] : track_properties,
      artists,
      version: version || undefined,
      localized_titles,
      catalog_id: catalog_id || undefined,
      isrc: isrc || undefined,
      lyrics: lyrics || undefined,
    });

    await this.trackDraft.save(draft);

    return new SuccessResponseDto();
  }

  @httpPost(
    '/new/submit/:id',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    MIDDLEWARE_TOKENS.SubscriptionCheckMiddleware,
    autoValidate(IdDto.validationSchema, 'params'),
    autoValidate(SubmitTrackDto.validationSchema),
    hasPermissions([
      {
        resource: PermissionResource.Distribution,
        accessLevel: ['editor'],
      },
      {
        resource: PermissionResource.CatalogManagement,
        accessLevel: ['editor'],
      },
    ]),
  )
  public async submitTrack(
    @httpReq() { claim }: RequestWithClaims,
    @requestParam() { id }: IdDto,
    @requestBody() payload: SubmitTrackDto,
  ) {
    const draft = await this.trackDraft.getDraft(
      id,
      claim?.team_id ?? claim.id,
    );

    if (!draft) {
      throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Invalid draft id');
    }

    const { iswc } = payload;

    payload.iswc = iswc || undefined;

    Object.assign(draft, payload);

    await this.trackDraft.save(draft);

    const track = await this.track.create(draft);

    await this.trackDraft.delete(draft.id, claim?.team_id ?? claim.id);

    return new SuccessResponseDto({ data: track });
  }

  @httpGet(
    '/:id',
    autoValidate(IdDto.validationSchema, 'params'),
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([
      UserCategory.COMMUNITY_OWNER,
      UserCategory.TEAM_MEMBER,
      UserCategory.CREATOR,
    ]),
    hasPermissions([
      {
        resource: PermissionResource.Distribution,
        accessLevel: ['editor', 'viewer'],
      },
      {
        resource: PermissionResource.CatalogManagement,
        accessLevel: ['editor', 'viewer'],
      },
    ]),
  )
  public async getTrackInfo(
    @requestParam() { id }: IdDto,
    @httpReq() { claim }: RequestWithClaims,
  ) {
    let track: Track;

    const roleDesc = await this.user.describe(claim.id);

    if (roleDesc.community_level_user) {
      track = await this.track.get(id);

      const trackCreator = await this.user.getById(track.user_id);

      if (trackCreator.community_id !== claim.community_id) {
        throw new ApplicationError(
          StatusCodes.FORBIDDEN,
          'You do not have the required permissions to access this track',
        );
      }
    } else {
      track = await this.track.get(id, claim?.team_id ?? claim.id);
    }

    if (!track) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Track not found');
    }

    return new SuccessResponseDto({ data: track });
  }
}
