import Joi from 'joi';

export class TrackSplitDto {
  splits: {
    track_id: string;
    use_self: boolean;
    payee_name: string;
    payee_email: string;
    share_percentage: number;
  }[];

  static validationSchema = Joi.object<TrackSplitDto, true>({
    splits: Joi.array()
      .items(
        Joi.object({
          track_id: Joi.string().required(),
          use_self: Joi.boolean(),
          payee_name: Joi.string().when('use_self', {
            is: true,
            then: Joi.optional(),
            otherwise: Joi.required(),
          }),
          payee_email: Joi.string().email().lowercase().when('use_self', {
            is: true,
            then: Joi.optional(),
            otherwise: Joi.required(),
          }),
          share_percentage: Joi.number().min(0.01).max(100).required(),
        }),
      )
      .min(1)
      .required()
      .custom((value, helpers) => {
        // Group splits by track_id
        const trackGroups = {};

        for (const split of value) {
          if (!trackGroups[split.track_id]) {
            trackGroups[split.track_id] = [];
          }
          trackGroups[split.track_id].push(split);
        }

        // Check if sum of share_percentage equals 100 for each track
        for (const trackId in trackGroups) {
          const totalSharePercentage = trackGroups[trackId].reduce(
            (sum, split) => sum + split.share_percentage,
            0,
          );

          // Allow a small rounding error tolerance (0.01)
          if (Math.abs(totalSharePercentage - 100) > 0.01) {
            return helpers.error('splits.invalidTotalShare', {
              trackId,
              totalSharePercentage,
            });
          }
        }

        return value;
      }, 'validate total share equals 100%'),
  }).messages({
    'splits.invalidTotalShare':
      'The sum of share percentages must equal 100% (current total: {{#totalSharePercentage}}%)',
  });
}
