import Joi from 'joi';

export class SubmitTrackDto {
  iswc: string;
  publisher: {
    name: string;
    publishing_type: 1 | 2 | 3;
    role: number;
    share: number;
  }[];

  static validationSchema = Joi.object<SubmitTrackDto, true>({
    iswc: Joi.string()
      .pattern(/^T[- ]?\d{3}\.?\d{3}\.?\d{3}[- ]?[0-9C]$/i)
      .optional()
      .allow(''),
    publisher: Joi.array()
      .items(
        Joi.object({
          name: Joi.string().required(),
          publishing_type: Joi.number().valid([1, 2, 3]).required(),
          role: Joi.number().required(),
          share: Joi.number().max(100).required(),
        }),
      )
      .optional(),
  });
}
