import Joi from 'joi';

export class SubmitTrackDto {
  iswc: string;
  publisher: {
    name: string;
    publishing_type: 1 | 2 | 3;
    role: number;
    share: number;
    publication_name: string;
    country: string;
  }[];

  static validationSchema = Joi.object<SubmitTrackDto, true>({
    iswc: Joi.string()
      .pattern(/^T[- ]?\d{3}\.?\d{3}\.?\d{3}[- ]?[0-9C]$/i)
      .optional()
      .allow(''),
    publisher: Joi.array()
      .items(
        Joi.object({
          name: Joi.string().required(),
          publishing_type: Joi.number().valid(1, 2, 3).required(),
          role: Joi.number().required(),
          share: Joi.number().max(100).required(),
          publication_name: Joi.string().when('publishing_type', {
            is: 3,
            then: Joi.required().messages({
              'any.required':
                'publication_name is required when publishing_type is 3',
              'string.empty':
                'publication_name is required when publishing_type is 3',
            }),
            otherwise: Joi.optional().allow(''),
          }),
          country: Joi.string().when('publishing_type', {
            is: 3,
            then: Joi.required().messages({
              'any.required': 'country is required when publishing_type is 3',
              'string.empty': 'country is required when publishing_type is 3',
            }),
            otherwise: Joi.optional().allow(''),
          }),
        }),
      )
      .optional(),
  });
}
