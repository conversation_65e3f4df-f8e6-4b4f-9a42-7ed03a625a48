import { LocalizedTitle } from '@app/services/track/entities/track.entity';
import Joi from 'joi';

export class AddTrackDetailsDto {
  title: string;
  version: string;
  localized_titles: LocalizedTitle[];
  origin: number;
  artists: {
    id: string;
    role: number;
    is_primary: boolean;
    is_main: boolean;
  }[];
  primary_genre: number;
  secondary_genre: number;
  preview: number;
  isrc: string;
  catalog_id: string;
  track_properties: number[];
  language: number;
  explicit: boolean;
  lyrics: string;
  copyright: { year: number; entity: string };

  static validationSchema = Joi.object<AddTrackDetailsDto, true>({
    title: Joi.string().required(),
    version: Joi.string().optional().allow(''),
    localized_titles: Joi.array()
      .items(
        Joi.object({
          language: Joi.number().required(),
          title: Joi.string().required(),
          version: Joi.string().optional().allow(''),
        }),
      )
      .required(),
    origin: Joi.number().required().valid(1, 2, 3),
    artists: Joi.array()
      .items(
        Joi.object({
          id: Joi.string().required(),
          role: Joi.number().required(),
          is_primary: Joi.boolean().default(false),
          is_main: Joi.boolean().default(false),
        }),
      )
      .min(1)
      .required()
      .custom((artists, helpers) => {
        const occurrenceMap: Record<string, boolean> = {};

        let mainArtist: {
          id: string;
          role: number;
          is_primary: boolean;
          is_main: boolean;
        };

        let mainArtistCount = 0;

        for (let idx = 0; idx < artists.length; idx++) {
          const artist = artists[idx];

          if (occurrenceMap[artist.id]) {
            return helpers.message({
              custom: 'Cannot add the same artist more than once',
            });
          }

          occurrenceMap[artist.id] = true;

          if (artist.is_main) {
            mainArtist = artist;
            mainArtistCount++;
          }
        }

        if (mainArtist) {
          if (mainArtistCount > 1) {
            return helpers.message({
              custom: 'There can only be one main artist',
            });
          }

          if (!mainArtist.is_primary) {
            return helpers.message({
              custom: 'Main artist must be primary',
            });
          }

          if (mainArtist.role != 49) {
            return helpers.message({
              custom: 'Main artist must be assigned "main primary artist role"',
            });
          }
        }

        return artists;
      }, 'Primary artist validation'),
    primary_genre: Joi.number().optional(),
    secondary_genre: Joi.number().optional(),
    preview: Joi.number().optional(),
    isrc: Joi.string()
      .pattern(/^[A-Z]{2}[A-Z0-9]{3}\d{2}\d{5}$/)
      .message('Invalid ISRC')
      .optional()
      .allow(''),
    catalog_id: Joi.string().optional().allow(''),
    track_properties: Joi.array().items(
      Joi.number().valid(1, 2, 3, 4, 5, 6, 7, 8),
    ),
    language: Joi.number().required(),
    explicit: Joi.boolean().default(false),
    lyrics: Joi.string().optional().allow(''),
    copyright: Joi.object({
      year: Joi.number().integer().min(1951).max(2100).required(),
      entity: Joi.string().required(),
    }).optional(),
  });
}
