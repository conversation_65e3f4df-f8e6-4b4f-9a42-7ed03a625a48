import { adminOnlyAuth } from '@app/http/middlewares/auth.middleware';
import {
  MIDDLEWARE_TOKENS,
  MODULE_TOKENS,
  SERVICE_TOKENS,
} from '@app/internal/ioc/tokens';
import { UserService } from '@app/services/user/user.service';
import { inject } from 'inversify';
import {
  controller,
  httpGet,
  httpPatch,
  requestBody,
  request as httpReq,
  queryParam,
  requestParam,
  httpPost,
} from 'inversify-express-utils';
import { UploadRoyaltyDto } from './dto/create-royalty.dto';
import {
  applyFileFilter,
  FileMiddleware,
  fileTypeFilter,
} from '@app/http/middlewares/file.middleware';
import { FileSize } from '@app/internal/enums';
import { RequestWithClaims } from '@app/internal/types';
import { RoyaltyRunService } from '@app/services/royalty/royalty.service';
import { AdminService } from '@app/services/admin/admin.service';
import path from 'path';
import { extractFilename } from '@app/utils/file.utils';
import { S3 } from '@app/modules/s3';
import { SuccessResponseDto } from '@app/http/dtos/http-response.dto';
import { autoValidate } from '@app/http/middlewares/validation.middleware';
import { FetchRoyaltiesDto } from './dto/fetch-royalties.dto';
import { IdDto } from '@app/http/dtos/id.dto';
import { ApplicationError } from '@app/internal/errors';
import { StatusCodes } from 'http-status-codes';
import { WalletService } from '@app/services/wallet/wallet.service';
import { parse } from 'csv-parse/sync';
import * as XLSX from 'xlsx';
import {
  emailQueue,
  SendEmailJob,
  sendNotificationJob,
  SendNotificationJob,
} from '@app/mq/jobs';
import { JobQueueManager } from '@app/internal/bull';
import { HttpClient } from '@app/internal/http/client';
import {
  Currency,
  PaymentMethod,
  PaymentType,
} from '@app/services/transaction/entities/transaction.entity';
import { WalletType } from '@app/services/wallet/entities/wallet.entity';
import { generateTransactionRef } from '@app/utils/transaction.util';
import { loadTemplate, render } from '@app/utils/template.utils';
import { env } from '@app/config/env';
import { CommunityService } from '@app/services/community/community.service';

export interface RoyaltyRow {
  PayeeId: number;
  'Payee Name': string;
  'Statement Subtotal in USD': number;
  'Tax Withholding Amount in USD': number;
  'Statement Total in USD': number;
  'Opening Balance in USD': number;
  'Closing Balance in USD': number;
}

@controller('/royalties')
export class RoyaltyController extends HttpClient {
  private readonly emailQueue =
    this.jobQueueManager.getQueue<SendEmailJob>(emailQueue);

  private notificationQueue =
    this.jobQueueManager.getQueue<SendNotificationJob>(sendNotificationJob);

  constructor(
    @inject(MODULE_TOKENS.JobQueueManager)
    private readonly jobQueueManager: JobQueueManager,
    @inject(SERVICE_TOKENS.UserService)
    private readonly user: UserService,
    @inject(MODULE_TOKENS.S3) private readonly s3: S3,
    @inject(SERVICE_TOKENS.RoyaltyRunService)
    private readonly royalty: RoyaltyRunService,
    @inject(SERVICE_TOKENS.AdminService) private readonly admin: AdminService,
    @inject(SERVICE_TOKENS.WalletService)
    private readonly wallet: WalletService,
    @inject(SERVICE_TOKENS.CommunityService)
    private readonly community: CommunityService,
  ) {
    super();
  }

  @httpPost(
    '/',
    adminOnlyAuth,
    MIDDLEWARE_TOKENS.AuthMiddleware,
    FileMiddleware(
      'single',
      {
        field_name: 'royalty_file',
      },
      {
        limits: { fileSize: 5 * FileSize.MB },
        fileFilter: applyFileFilter([
          fileTypeFilter(['.csv', '.xls', '.xlsx']),
        ]),
      },
    ),
    autoValidate(UploadRoyaltyDto.validationSchema),
  )
  public async uploadRun(
    @httpReq() { claim, file }: RequestWithClaims,
    @requestBody() payload: UploadRoyaltyDto,
  ) {
    let statement: string | undefined;
    let total_revenue = 0;

    if (file) {
      const fileExtension = path.extname(file.originalname).toLowerCase();
      const fileBuffer = file.buffer;

      if (fileExtension === '.csv') {
        const csvString = fileBuffer.toString().replace(/^\uFEFF/, '');
        const records = parse(csvString, {
          columns: true,
          skip_empty_lines: true,
        });

        const headers = Object.keys(records[0] || {});
        if (
          !headers.includes('PayeeId') ||
          !headers.includes('Closing Balance in USD')
        ) {
          throw new ApplicationError(
            StatusCodes.BAD_REQUEST,
            'Invalid CSV: Required columns "PayeeId" and "Closing Balance in USD" are missing',
          );
        }

        total_revenue = records.reduce((sum, record) => {
          const amount = parseFloat(
            record['Closing Balance in USD'].replace(/[^0-9.-]+/g, ''),
          );
          return !isNaN(amount) ? sum + amount : sum;
        }, 0);
      } else if (fileExtension === '.xlsx' || fileExtension === '.xls') {
        const workbook = XLSX.read(fileBuffer);
        const sheetName = workbook.SheetNames[0];
        const data: RoyaltyRow[] = XLSX.utils.sheet_to_json(
          workbook.Sheets[sheetName],
        );

        const headers = Object.keys(data[0] || {});
        if (
          !headers.includes('PayeeId') ||
          !headers.includes('Closing Balance in USD')
        ) {
          throw new ApplicationError(
            StatusCodes.BAD_REQUEST,
            'Invalid Excel: Required columns "PayeeId" and "Closing Balance in USD" are missing',
          );
        }

        total_revenue = data.reduce((sum, record) => {
          const amount = parseFloat(
            String(record['Closing Balance in USD']).replace(/[^0-9.-]+/g, ''),
          );
          return !isNaN(amount) ? +sum + amount : sum;
        }, 0);
      } else {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          'Unsupported file type. Only CSV and Excel files are allowed.',
        );
      }

      // Upload file if validation passed
      const timestampedFilename = `${extractFilename(
        file.originalname,
      )}_${new Date().toISOString()}${fileExtension}`;

      statement = await this.s3.upload({
        Key: timestampedFilename,
        Body: fileBuffer,
        ContentType: file.mimetype,
      });
    }

    await this.royalty.create({
      ...payload,
      statement,
      uploaded_by: claim.id,
      total_revenue,
    });

    return new SuccessResponseDto();
  }

  @httpGet(
    '/',
    adminOnlyAuth,
    MIDDLEWARE_TOKENS.AuthMiddleware,
    autoValidate(FetchRoyaltiesDto.validationSchema, 'query'),
  )
  public async fetch(
    @queryParam()
    { search_term, result_per_page, page_number, status }: FetchRoyaltiesDto,
  ) {
    const royalties = await this.royalty.fetch(
      page_number,
      result_per_page,
      status,
      search_term,
    );

    return new SuccessResponseDto({ data: royalties });
  }

  @httpPatch(
    '/:id',
    adminOnlyAuth,
    MIDDLEWARE_TOKENS.AuthMiddleware,
    autoValidate(IdDto.validationSchema, 'params'),
  )
  public async processRoyalties(
    @requestParam() { id }: IdDto,
    @httpReq() { claim }: RequestWithClaims,
  ) {
    const royalty = await this.royalty.get(id);

    if (!royalty)
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Royalty not found');

    if (royalty.status === 'completed')
      throw new ApplicationError(
        StatusCodes.CONFLICT,
        'Royalty run cannot be done more than once',
      );

    if (!royalty.statement) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'No royalty statement file found',
      );
    }

    const response = await this.get(royalty.statement, {
      responseType: 'arraybuffer',
    });

    if (response.status !== 200) {
      throw new ApplicationError(
        StatusCodes.INTERNAL_SERVER_ERROR,
        'Failed to fetch the royalty statement file',
      );
    }

    const fileBuffer = Buffer.from(response.data);
    const ext = royalty.statement.split('.').pop()?.toLowerCase();

    let rows: Array<{ payeeId: string; amount: number }> = [];

    if (ext === 'csv') {
      const csvString = fileBuffer.toString().replace(/^\uFEFF/, '');
      const records = parse(csvString, {
        columns: true,
        skip_empty_lines: true,
      });

      const headers = Object.keys(records[0] ?? {});
      if (
        !headers.includes('PayeeId') ||
        !headers.includes('Closing Balance in USD')
      ) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          'Invalid file for a Royalty run',
        );
      }

      rows = records.map((row: any) => ({
        payeeId: row.PayeeId?.trim(),
        amount: parseFloat(row['Closing Balance in USD']),
      }));
    } else if (ext === 'xlsx' || ext === 'xls') {
      const workbook = XLSX.read(fileBuffer);
      const sheetName = workbook.SheetNames[0];
      const data = XLSX.utils.sheet_to_json(workbook.Sheets[sheetName]);

      const headers = Object.keys(data[0] ?? {});
      if (
        !headers.includes('PayeeId') ||
        !headers.includes('Closing Balance in USD')
      ) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          'Invalid file for a Royalty run',
        );
      }

      rows = (data as any[]).map((row) => ({
        payeeId: String(row['PayeeId'])?.trim(),
        amount: parseFloat(row['Closing Balance in USD']),
      }));
    } else {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'Unsupported file type',
      );
    }

    const unmatched: Record<string, string[]> = {}; // { CommunityName: [email,…] }
    const admin = await this.admin.get(claim.id);

    for (const { payeeId, amount } of rows) {
      if (!payeeId || isNaN(amount) || amount <= 0) continue;

      const user = await this.user.find({ payee_id: payeeId });

      if (!user) {
        (unmatched['Unknown'] ??= []).push(payeeId);
        continue;
      }

      const community = user.community_id
        ? await this.community.get(user.community_id)
        : null;
      const communityName = community?.name ?? 'Unknown';

      if (!community) {
        (unmatched['No Community'] ??= []).push(user.email ?? payeeId);
        continue;
      }

      if (!community.id) {
        (unmatched[communityName] ??= []).push(user.email ?? payeeId);
        continue;
      }

      const wallet = await this.wallet.getOrCreate(
        user.id,
        Currency.USD,
        WalletType.FIAT,
      );

      await this.wallet.credit({
        id: wallet.id,
        amount: amount,
        payment_method: PaymentMethod.INTERNAL,
        payment_type: PaymentType.ROYALTIES,
        currency: Currency.USD,
        description: 'Royalty payout',
        metadata: { royalty_id: id },
        reference: generateTransactionRef('roy'),
        community_id: user.community_id,
      });

      await this.notificationQueue.add('notification', {
        user_id: user.id,
        message: `You have just received a royalty payout of ${amount.toFixed(
          5,
        )} USD`,
        metadata: { amount, royalty_id: id },
      });
    }

    await this.royalty.update(id, {
      status: 'completed',
      processed_by: claim.id,
    });

    const successHtml = render(loadTemplate('makerverse/successful-royalty'), {
      from: 'Makerverse',
      name: admin.first_name,
      url: `${env.admin_client_base_url}/royalties`,
      year: new Date().getFullYear().toString(),
    });

    await this.emailQueue.add('email', {
      to: admin.email,
      subject: 'Successful Royalty Run',
      body: successHtml,
    });

    if (Object.keys(unmatched).length > 0) {
      const unmatchedPayeesListHtml = Object.entries(unmatched)
        .map(([community, emails]) => {
          const list = emails.map((e) => `[${e}]`).join('<br/>');
          return `<strong>${community}</strong><br/>${list}`;
        })
        .join('<br/><br/>');

      const missingHtml = render(loadTemplate('makerverse/payee-not-found'), {
        name: admin.first_name,
        unmatchedPayeesListHtml,
        year: new Date().getFullYear().toString(),
      });

      await this.emailQueue.add('email', {
        from: 'Makerverse',
        to: admin.email,
        subject: 'Action Required - Missing Payee Information',
        body: missingHtml,
      });
    }

    return new SuccessResponseDto();
  }
}
