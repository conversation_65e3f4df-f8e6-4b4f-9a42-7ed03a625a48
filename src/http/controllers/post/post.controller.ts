import { IdDto } from '@app/http/dtos/id.dto';
import { autoValidate } from '@app/http/middlewares/validation.middleware';
import {
  controller,
  httpDelete,
  httpGet,
  httpPatch,
  httpPost,
  queryParam,
  request as httpReq,
  requestBody,
  requestParam,
} from 'inversify-express-utils';
import { CreatePostDto } from './dto/create-post.dto';
import { PostContent } from '@app/services/post/entities/post.entity';
import { ApplicationError } from '@app/internal/errors';
import { StatusCodes } from 'http-status-codes';
import { inject } from 'inversify';
import {
  MIDDLEWARE_TOKENS,
  MODULE_TOKENS,
  SERVICE_TOKENS,
} from '@app/internal/ioc/tokens';
import { PostService } from '@app/services/post/post.service';
import { SuccessResponseDto } from '@app/http/dtos/http-response.dto';
import { RequestWithClaims } from '@app/internal/types';
import { PaginatedQueryDto } from '@app/http/dtos/paginated-query.dto';
import { MediaService } from '@app/services/media/media.service';
import { MediaType } from '@app/services/media/entites/media.entity';
import { UserService } from '@app/services/user/user.service';
import { QueryPostsDto } from './dto/query-posts.dto';
import { IP2C } from '@app/modules/ip2c';
import { PostPlayDto } from '@app/http/controllers/post/dto/post-play.dto';
import { PostIdsDto } from './dto/view-post.dto';
import { ulid } from 'ulid';
import { hasPermissions } from '@app/http/middlewares/permissions.middleware';
import { PermissionResource } from '@app/utils/permissions.utils';
import { parseFromUserAgent } from '@app/utils/device-detector-utils';
import { notificationMilestones } from '@app/services/notification/entity/notification.entity';
import { JobQueueManager } from '@app/internal/bull';
import { sendNotificationJob, SendNotificationJob } from '@app/mq/jobs';
import { pick } from 'lodash';
import { UserCategory } from '@app/services/user/entities/user.entity';

@controller('/post')
export class PostController {
  private notificationQueue =
    this.jobQueueManager.getQueue<SendNotificationJob>(sendNotificationJob);

  constructor(
    @inject(MODULE_TOKENS.IP2C) private readonly ip2c: IP2C,
    @inject(SERVICE_TOKENS.PostService)
    private readonly post: PostService,
    @inject(SERVICE_TOKENS.MediaService) private readonly media: MediaService,
    @inject(SERVICE_TOKENS.UserService) private readonly user: UserService,
    @inject(MODULE_TOKENS.JobQueueManager)
    private readonly jobQueueManager: JobQueueManager,
  ) {}

  @httpGet(
    '/all',
    MIDDLEWARE_TOKENS.OptionalAuthMiddleware,
    autoValidate(QueryPostsDto.validationSchema, 'query'),
  )
  public async getPosts(
    @queryParam() { user_id, cursor, result_per_page }: QueryPostsDto,
    @httpReq() { claim }: RequestWithClaims,
  ) {
    const user = await this.user.getById(user_id);

    if (!user) {
      throw new ApplicationError(
        StatusCodes.NOT_FOUND,
        "This user doesn't exist",
      );
    }

    const id = claim?.team_id ?? user.id;

    const paginatedPostResult = await (user.category === UserCategory.FAN
      ? this.post.fetchUsersReplies(id, {
          cursor,
          result_per_page,
          user_viewing_post_id: claim?.id,
        })
      : this.post.fetchUsersPosts(id, {
          cursor,
          result_per_page,
          user_viewing_post_id: claim?.id,
        }));

    return new SuccessResponseDto({ data: paginatedPostResult });
  }

  @httpGet(
    '/',
    autoValidate(PaginatedQueryDto.validationSchema, 'query'),
    MIDDLEWARE_TOKENS.AuthMiddleware,
  )
  public async getUserPosts(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { cursor, result_per_page }: PaginatedQueryDto,
  ) {
    const paginatedPostResult = await this.post.fetchUsersPosts(
      claim?.team_id ?? claim.id,
      {
        cursor,
        result_per_page,
        user_viewing_post_id: claim.id,
      },
    );

    return new SuccessResponseDto({ data: paginatedPostResult });
  }

  @httpGet(
    '/replies',
    MIDDLEWARE_TOKENS.OptionalAuthMiddleware,
    autoValidate(QueryPostsDto.validationSchema, 'query'),
  )
  public async getUsersReplies(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { user_id, cursor, result_per_page }: QueryPostsDto,
  ) {
    if (user_id) {
      const user = await this.user.getById(user_id);

      if (!user) {
        throw new ApplicationError(
          StatusCodes.NOT_FOUND,
          "This user doesn't exist",
        );
      }
    }

    const replies = await this.post.fetchUsersReplies(
      claim?.team_id ?? claim.id,
      {
        cursor,
        result_per_page,
        user_viewing_post_id: claim.id,
      },
    );

    return new SuccessResponseDto({ data: replies });
  }

  @httpGet('/following', MIDDLEWARE_TOKENS.AuthMiddleware)
  public async getFollowingPosts(
    @queryParam() { cursor, result_per_page }: QueryPostsDto,
    @httpReq() { claim }: RequestWithClaims,
  ) {
    const posts = await this.post.followingPosts(claim?.team_id ?? claim.id, {
      cursor,
      result_per_page,
      user_viewing_post_id: claim.id,
    });

    return new SuccessResponseDto({ data: posts });
  }

  @httpGet(
    '/likes',
    MIDDLEWARE_TOKENS.OptionalAuthMiddleware,
    autoValidate(QueryPostsDto.validationSchema, 'query'),
  )
  public async getUsersLikes(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { user_id, cursor, result_per_page }: QueryPostsDto,
  ) {
    if (user_id) {
      const user = await this.user.getById(user_id);

      if (!user) {
        throw new ApplicationError(
          StatusCodes.NOT_FOUND,
          "This user doesn't exist",
        );
      }
    }

    const likes = await this.post.fetchUsersLikes(claim?.team_id ?? claim.id, {
      cursor,
      result_per_page,
      user_viewing_post_id: claim.id,
    });

    return new SuccessResponseDto({ data: likes });
  }

  @httpGet(
    '/saves',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    autoValidate(QueryPostsDto.validationSchema, 'query'),
  )
  public async getUsersSaves(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { user_id, cursor, result_per_page }: QueryPostsDto,
  ) {
    if (user_id) {
      const user = await this.user.getById(user_id);

      if (!user) {
        throw new ApplicationError(
          StatusCodes.NOT_FOUND,
          "This user doesn't exist",
        );
      }
    }

    const saves = await this.post.fetchUsersSaved(claim?.team_id ?? claim.id, {
      cursor,
      result_per_page,
      user_viewing_post_id: claim.id,
    });

    return new SuccessResponseDto({ data: saves });
  }

  @httpGet(
    '/parent/:id',
    MIDDLEWARE_TOKENS.OptionalAuthMiddleware,
    autoValidate(IdDto.validationSchema, 'params'),
  )
  public async getPostParent(
    @requestParam() { id }: IdDto,
    @httpReq() { claim }: RequestWithClaims,
  ) {
    const post = await this.post.get(id);

    if (!post) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Post not found');
    }

    const parentPost = await this.post.fetchPostParent(
      id,
      claim?.team_id ?? claim?.id,
    );

    return new SuccessResponseDto({ data: parentPost });
  }

  @httpGet(
    '/replies/:id',
    MIDDLEWARE_TOKENS.OptionalAuthMiddleware,
    autoValidate(IdDto.validationSchema, 'params'),
    autoValidate(PaginatedQueryDto.validationSchema, 'query'),
  )
  public async getPostReplies(
    @requestParam() { id }: IdDto,
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { cursor, result_per_page }: PaginatedQueryDto,
  ) {
    const post = await this.post.get(id);

    if (!post) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Post not found');
    }

    if (claim) {
      if (post.author.community_id !== claim?.community_id) {
        throw new ApplicationError(
          StatusCodes.NOT_FOUND,
          'This post does not belong in your community',
        );
      }
    }

    const paginatedPostResult = await this.post.fetchPostChildren(id, {
      user_viewing_post_id: claim?.id,
      cursor,
      result_per_page,
    });

    return new SuccessResponseDto({ data: paginatedPostResult });
  }

  @httpPost(
    '/',
    autoValidate(CreatePostDto.validationSchema),
    MIDDLEWARE_TOKENS.AuthMiddleware,
    hasPermissions([
      {
        resource: PermissionResource.FanPage,
        accessLevel: ['editor'],
      },
    ]),
  )
  public async createPost(
    @httpReq() { claim }: RequestWithClaims,
    @requestBody() payload: CreatePostDto,
  ) {
    if (Object.keys(payload).length === 0) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'You cannot create an empty post',
      );
    }

    const postContent = new PostContent();

    if (payload.parent_id) {
      const postParent = await this.post.get(payload.parent_id);
      if (!postParent) {
        throw new ApplicationError(
          StatusCodes.NOT_FOUND,
          'Post being replied to not found',
        );
      }

      if (postParent.author.community_id !== claim?.community_id) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          "You can only reply to your community's posts",
        );
      }
    }

    if (payload.text_content) {
      postContent.text = payload.text_content;
    }

    if (Object.keys(payload.media ?? {}).length > 0) {
      for (const [key, mediaItems] of Object.entries(payload.media)) {
        if (mediaItems?.length > 0) {
          const processedMediaItems = await Promise.all(
            mediaItems.map(async (item) => {
              const media = await this.media.getById(item.id);
              if (!media) {
                throw new ApplicationError(
                  StatusCodes.BAD_REQUEST,
                  'Media not found',
                );
              }
              const mediaType =
                media.type === MediaType.AUDIO ? media.type : `${media.type}s`;
              if (mediaType !== key) {
                throw new ApplicationError(
                  StatusCodes.BAD_REQUEST,
                  `Cannot use ${key} as media for ${mediaType}`,
                );
              }

              return {
                id: item.id,
                url: media.url,
                metadata: item.metadata,
              };
            }),
          );

          postContent[key] = processedMediaItems;
        }
      }
    }
    if (payload.tags?.length > 0) {
      postContent.tags = payload.tags;
    }

    if (payload.hyperlinks?.length > 0) {
      postContent.hyperlinks = payload.hyperlinks.map((link) => ({
        ...link,
        id: ulid(),
      }));
    }

    const post = await this.post.create({
      content: postContent,
      metadata: payload.metadata,
      user_id: claim?.team_id ?? claim.id,
      parent_id: payload?.parent_id || null,
    });

    if (postContent.hyperlinks?.length > 0) {
      await Promise.all(
        postContent.hyperlinks.map((link) =>
          this.post.createPostLink(
            post.id,
            link.url,
            claim?.team_id ?? claim.id,
            link.id,
          ),
        ),
      );
    }

    if (payload.parent_id) {
      const parentPost = await this.post.fetchPostParent(post.id);
      if (parentPost.user_id !== (claim?.team_id ?? claim.id)) {
        const user = await this.user.getById(claim?.team_id ?? claim.id);
        await this.notificationQueue.add('send_notification_job', {
          user_id: parentPost.user_id,
          message: `💬 @${user.username} commented on your post, 'Great work!'`,
          metadata: {
            user: { ...pick(user, 'username', 'profile_picture') },
            content: { id: post.id, ...post.content },
          },
        });
      }

      if (notificationMilestones.includes(parentPost.replies)) {
        await this.notificationQueue.add('send_notification_job', {
          user_id: parentPost.user_id,
          message: `👥 Your post has received ${parentPost.replies} comments! Fans are loving it.`,
          metadata: {
            content: { id: parentPost.id, ...parentPost.content },
          },
        });
      }
    }

    if (postContent.tags && postContent.tags.length > 0) {
      for (const tag of postContent.tags) {
        const taggedUser = await this.user.getByUsername(tag.handle);
        if (taggedUser && taggedUser.id !== post.user_id) {
          await this.notificationQueue.add('send_notification_job', {
            user_id: taggedUser.id,
            message: `🏷️ You just got tagged to a post`,
            metadata: {
              user: { ...pick(taggedUser, 'username', 'profile_picture') },
              content: { id: post.id, ...post.content },
            },
          });
        }
      }
    }

    return new SuccessResponseDto({ data: post });
  }

  @httpPatch(
    '/:id/link/click',
    autoValidate(IdDto.validationSchema, 'params'),
    MIDDLEWARE_TOKENS.AuthMiddleware,
  )
  public async registerLinkClick(@requestParam() { id }: IdDto) {
    let link = await this.post.getLink(id);

    if (!link) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Link not found');
    }

    await this.post.updateLinkClicks(id);

    link = await this.post.getLink(id);

    if (notificationMilestones.includes(link.clicks)) {
      const post = await this.post.get(link.post_id);
      await this.notificationQueue.add('send_notification_job', {
        user_id: link.user_id,
        message: `🔗 Your link received ${link.clicks} clicks! Your fans are curious.`,
        metadata: { content: post, link: { ...link, post } },
      });
    }

    return new SuccessResponseDto();
  }

  @httpGet('/feed', MIDDLEWARE_TOKENS.AuthMiddleware)
  public async getFeed(
    @queryParam() { cursor, result_per_page }: QueryPostsDto,
    @httpReq() { claim }: RequestWithClaims,
  ) {
    const id = claim?.team_id ?? claim.id;
    const feed = await this.post.recommendPosts(id, {
      cursor,
      result_per_page,
      user_viewing_post_id: claim.id,
    });
    return new SuccessResponseDto({ data: feed });
  }

  @httpGet(
    '/:id',
    MIDDLEWARE_TOKENS.OptionalAuthMiddleware,
    autoValidate(IdDto.validationSchema, 'params'),
  )
  public async getPost(
    @requestParam() { id }: IdDto,
    @httpReq() { claim }: RequestWithClaims,
  ) {
    const post = await this.post.get(id, claim?.team_id ?? claim?.id);

    if (!post) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Post not found');
    }

    if (claim) {
      if (post.author.community_id !== claim?.community_id) {
        throw new ApplicationError(
          StatusCodes.NOT_FOUND,
          'This post does not belong in your community',
        );
      }
    }

    return new SuccessResponseDto({ data: post });
  }

  @httpPost(
    '/:id/like',
    autoValidate(IdDto.validationSchema, 'params'),
    MIDDLEWARE_TOKENS.AuthMiddleware,
  )
  public async likePost(
    @requestParam() { id }: IdDto,
    @httpReq() { claim }: RequestWithClaims,
  ) {
    const post = await this.post.get(id);

    if (!post) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Post not found');
    }

    const like = await this.post.toggleLike(id, claim?.team_id ?? claim.id);

    if (!(post.user_id === claim.id) && like) {
      const liked_user = await this.user.getById(claim?.team_id ?? claim.id);

      await this.notificationQueue.add('send_notification_job', {
        user_id: post.user_id,
        message: `❤️ @${liked_user.username} liked your post`,
        metadata: {
          user: { ...pick(liked_user, 'username', 'profile_picture') },
          content: { id: post.id, ...post.content },
        },
      });
    }

    if (notificationMilestones.includes(post.likes)) {
      await this.notificationQueue.add('send_notification_job', {
        user_id: post.user_id,
        message: `🔥 Your post just reached ${post.likes} likes. Amazing engagement!`,
        metadata: {
          content: { id: post.id, content: post.content },
        },
      });
    }

    return new SuccessResponseDto();
  }

  @httpPost(
    '/:id/save',
    autoValidate(IdDto.validationSchema, 'params'),
    MIDDLEWARE_TOKENS.AuthMiddleware,
  )
  public async savePost(
    @requestParam() { id }: IdDto,
    @httpReq() { claim }: RequestWithClaims,
  ) {
    const post = await this.post.get(id);

    if (!post)
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Post not found');

    await this.post.toggleSave(id, claim?.team_id ?? claim.id);

    if (notificationMilestones.includes(post.saves)) {
      await this.notificationQueue.add('send_notification_job', {
        user_id: post.user_id,
        message: `💾 Fans saved your post ${post.saves} times! Looks like they want to keep it close.`,
        metadata: {
          content: { id: post.id, content: post.content },
        },
      });
    }

    return new SuccessResponseDto();
  }

  @httpPost(
    '/view',
    autoValidate(PostIdsDto.validationSchema, 'body'),
    MIDDLEWARE_TOKENS.OptionalAuthMiddleware,
  )
  public async viewPost(
    @requestBody() payload: PostIdsDto,
    @httpReq() req: RequestWithClaims,
  ) {
    const { claim } = req;
    for (const id of payload.post_ids) {
      const post = await this.post.get(id);

      if (!post) {
        throw new ApplicationError(
          StatusCodes.NOT_FOUND,
          `Post with id ${id} not found`,
        );
      }

      const ip = <string>req.headers['x-real-ip'] ?? req.ip;

      const ipMetadata = await this.ip2c.queryIP(ip);

      const userAgentData = parseFromUserAgent(req.headers?.['user-agent']);

      await this.post.viewPost(id, {
        user_id: claim?.id,
        metadata: { ip, country: ipMetadata.isoCountryCode, ...userAgentData },
      });
    }

    return new SuccessResponseDto();
  }

  @httpPost(
    '/:id/play',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    autoValidate(IdDto.validationSchema, 'params'),
    autoValidate(PostPlayDto.validationSchema),
  )
  public async postPlay(
    @requestParam() { id }: IdDto,
    @httpReq() req: RequestWithClaims,
    @requestBody() payload: PostPlayDto,
  ) {
    const { claim } = req;

    const post = await this.post.get(id);

    if (!post) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Post not found');
    }

    const media = await this.media.getById(payload.media_id);

    if (!media) {
      throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Media not found');
    }

    const ip = <string>req.headers['x-real-ip'] ?? req.ip;

    const ipMetadata = await this.ip2c.queryIP(ip);

    await this.post.registerPlay(id, {
      user_id: claim.id,
      media_id: media.id,
      metadata: { ip, country: ipMetadata.isoCountryCode },
    });

    const plays = await this.post.getMediaPlays(media.id);

    if (notificationMilestones.includes(plays)) {
      const message =
        media.type === MediaType.AUDIO
          ? `🎶 Your track is trending with ${plays} listens!`
          : `📽️ Your video just hit ${plays} views. Fans are hooked!`;

      await this.notificationQueue.add('send_notification_job', {
        user_id: post.user_id,
        message,
        metadata: {
          content: { id: post.id, content: post.content, media },
        },
      });
    }

    return new SuccessResponseDto();
  }

  @httpPatch(
    '/:id/share',
    autoValidate(IdDto.validationSchema, 'params'),
    MIDDLEWARE_TOKENS.AuthMiddleware,
  )
  public async registerShare(
    @requestParam() { id }: IdDto,
    @httpReq() { claim }: RequestWithClaims,
  ) {
    let post = await this.post.get(id);

    if (!post)
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Post not found');

    await this.post.registerShare(id);

    if (claim?.team_id ?? claim.id !== post.user_id) {
      const shared_user = await this.user.getById(claim?.team_id ?? claim.id);
      await this.notificationQueue.add('send_notification_job', {
        user_id: post.user_id,
        message: `🔄 @${shared_user.username} just shared your post`,
        metadata: {
          user: { ...pick(shared_user, 'username', 'profile_picture') },
          content: { id: post.id, content: post.content },
        },
      });
    }

    post = await this.post.get(id);
    if (notificationMilestones.includes(post.shares)) {
      await this.notificationQueue.add('send_notification_job', {
        user_id: post.user_id,
        message: `🔄 ${post.shares} fans have shared your post! Keep the momentum going.`,
        metadata: {
          content: { id: post.id, content: post.content },
        },
      });
    }

    return new SuccessResponseDto();
  }

  @httpDelete(
    '/:id',
    autoValidate(IdDto.validationSchema, 'params'),
    MIDDLEWARE_TOKENS.AuthMiddleware,
    hasPermissions([
      {
        resource: PermissionResource.FanPage,
        accessLevel: ['editor'],
      },
    ]),
  )
  public async deletePost(
    @requestParam() { id }: IdDto,
    @httpReq() { claim }: RequestWithClaims,
  ) {
    const post = await this.post.getUserPost(id, claim?.team_id ?? claim.id);

    if (!post) {
      throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Invalid action');
    }

    await this.post.softDelete(post.id);

    return new SuccessResponseDto();
  }
}
