import { IdDto } from '@app/http/dtos/id.dto';
import { ApplicationError } from '@app/internal/errors';
import {
  MIDDLEWARE_TOKENS,
  MODULE_TOKENS,
  SERVICE_TOKENS,
} from '@app/internal/ioc/tokens';
import { RequestWithClaims } from '@app/internal/types';
import { Stripe } from '@app/modules/stripe';
import { BeneficiaryService } from '@app/services/beneficiary/beneficiary.service';
import { inject } from 'inversify';
import {
  controller,
  httpPost,
  requestBody,
  request as httpReq,
  requestParam,
  httpGet,
} from 'inversify-express-utils';
import { WithdrawDto } from './dto/withdraw.dto';
import { UserCategory } from '@app/services/user/entities/user.entity';
import { inCategory } from '@app/http/middlewares/user-category.middleware';
import { autoValidate } from '@app/http/middlewares/validation.middleware';
import { WalletService } from '@app/services/wallet/wallet.service';
import { StatusCodes } from 'http-status-codes';
import {
  Currency,
  PaymentMethod,
  PaymentType,
} from '@app/services/transaction/entities/transaction.entity';
import { WalletType } from '@app/services/wallet/entities/wallet.entity';
import { JobQueueManager } from '@app/internal/bull';
import {
  emailQueue,
  initiateTransferJob,
  InitiateTransferJob,
  SendEmailJob,
  stripePayoutCurrencies,
} from '@app/mq/jobs';
import {
  HttpResponseDto,
  SuccessResponseDto,
} from '@app/http/dtos/http-response.dto';
import { DURATION } from '@app/internal/enums';
import { loadTemplate, render } from '@app/utils/template.utils';
import { UserService } from '@app/services/user/user.service';
import { CommunityService } from '@app/services/community/community.service';
import { generateOtp, generateOtpToken } from '@app/utils/otp-utils';
import { prefixKey } from '@app/utils/prefix-key';
import { RedisStore } from '@app/internal/redis/store';
import { Logger } from '@app/internal/logger';
import { Paystack } from '@app/modules/paystack';
import { formatDateTime } from '@app/utils/date-utils';

@controller('/payments')
export class PaymentsController {
  private readonly transferQueue =
    this.jobQueueManager.getQueue<InitiateTransferJob>(initiateTransferJob);

  private readonly emailQueue =
    this.jobQueueManager.getQueue<SendEmailJob>(emailQueue);

  constructor(
    @inject(MODULE_TOKENS.Logger) protected readonly logger: Logger,
    @inject(MODULE_TOKENS.RedisStore)
    private readonly redisStore: RedisStore,
    @inject(MODULE_TOKENS.JobQueueManager)
    private readonly jobQueueManager: JobQueueManager,
    @inject(MODULE_TOKENS.Stripe)
    private readonly stripe: Stripe,
    @inject(SERVICE_TOKENS.BeneficiaryService)
    private readonly beneficiary: BeneficiaryService,
    @inject(SERVICE_TOKENS.WalletService)
    private readonly wallet: WalletService,
    @inject(SERVICE_TOKENS.UserService)
    private readonly user: UserService,
    @inject(SERVICE_TOKENS.CommunityService)
    private readonly community: CommunityService,
    @inject(MODULE_TOKENS.Paystack) private readonly paystack: Paystack,
  ) {}

  @httpGet(
    '/withdraw/initiate',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([UserCategory.CREATOR, UserCategory.COMMUNITY_OWNER]),
  )
  public async initiateWithdrawal(@httpReq() { claim }: RequestWithClaims) {
    const user = await this.user.describe(claim.id);
    const community = await this.community.get(claim.community_id);
    const otp = generateOtp(6);
    const otpToken = generateOtpToken(user.id, otp);

    const templateData = {
      communityName: user.community_level_user ? community.name : 'Makerverse',
      templateName: user.community_level_user
        ? 'makerverse/otp'
        : 'community/otp',
      redisData: {
        email: user.email,
        first_name: user.first_name,
        community_name: user.community_level_user
          ? community.name
          : 'Makerverse',
        community_id: null,
        category: user.category,
      },
    };

    if (user.community_level_user) {
      templateData.redisData.community_id = community.id;
    }

    const htmlData = {
      fname: user.first_name,
      otp,
      communityName: templateData.communityName,
      logoUrl: community.brand_themed_logo,
      color: community.brand_color,
      year: new Date().getFullYear().toString(),
    };

    const template = loadTemplate(templateData.templateName);
    const html = render(template, htmlData);
    await this.emailQueue.add('email', {
      from: templateData.communityName,
      to: user.email,
      subject: 'Initiate Withdrawal Request',
      body: html,
    });

    const otpVerificationKey = prefixKey(otpToken, otp);

    await this.redisStore.set(
      otpVerificationKey,
      templateData.redisData,
      5 * DURATION.MINUTES,
    );

    // save email to enable resend
    await this.redisStore.set(
      otpToken,
      templateData.redisData,
      15 * DURATION.MINUTES,
    );

    return new SuccessResponseDto({ data: { otp_token: otpToken } });
  }

  @httpPost(
    '/withdraw/:id',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([UserCategory.CREATOR, UserCategory.COMMUNITY_OWNER]),
    autoValidate(WithdrawDto.validationSchema),
    autoValidate(IdDto.validationSchema, 'params'),
  )
  public async withdraw(
    @requestParam() { id }: IdDto,
    @httpReq() { claim }: RequestWithClaims,
    @requestBody() payload: WithdrawDto,
  ) {
    const otpVerificationKey = prefixKey(payload.otp_token, payload.token);

    const metadata = await this.redisStore.get<{
      email: string;
      category: UserCategory;
    }>(otpVerificationKey);

    if (!metadata) {
      throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Invalid Otp');
    }

    await this.redisStore.delete(otpVerificationKey);

    if (
      metadata.category === UserCategory.COMMUNITY_OWNER &&
      payload.amount < 100
    ) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'The least amount you can withdraw is $100',
      );
    }

    const beneficiary = await this.beneficiary.get(id, claim.id);

    if (!beneficiary) {
      throw new ApplicationError(
        StatusCodes.NOT_FOUND,
        'Beneficiary not found',
      );
    }

    const wallet = await this.wallet.getOrCreate(
      claim.id,
      Currency.USD,
      WalletType.FIAT,
    );

    const quote = await this.stripe.createFxQuote({
      from_currencies: [wallet.currency],
      to_currency: beneficiary.currency,
      lock_duration: 'none',
    });

    const baseRate =
      quote.rates[wallet.currency.toLowerCase()].rate_details.base_rate;

    const usdAmountMinor = Math.round(payload.amount * 100);
    const feeMinor = Math.round(usdAmountMinor * 0.03);
    const debitMinor = usdAmountMinor + feeMinor;

    const amountMinor = Math.round((debitMinor / 100) * baseRate * 100);

    const canDebit = await this.wallet.canWithdraw(
      wallet.id,
      payload.amount,
      feeMinor / 100,
    );

    if (!canDebit) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'Insufficient funds for this withdrawal',
      );
    }

    const transferRef = crypto.randomUUID();

    await this.wallet.debit({
      id: wallet.id,
      amount: debitMinor / 100,
      community_id: claim.community_id,
      beneficiary_id: beneficiary.id,
      currency: Currency.USD,
      payment_method: PaymentMethod.BANK_TRANSFER,
      payment_type: PaymentType.WITHDRAWAL,
      reference: transferRef,
      description: payload.description,
      fee: feeMinor / 100,
      conversion_rate: baseRate,
    });

    if (!stripePayoutCurrencies.includes(beneficiary.currency)) {
      const balances = await this.paystack.getBalances();
      const ledger = balances.data.find(
        (b) => b.currency === beneficiary.currency.toUpperCase(),
      );
      const available = ledger?.balance ?? 0;

      if (available < amountMinor) {
        const today = new Date();
        const template = loadTemplate('admin/low-float');
        const htmlData = {
          providerName: 'Paystack',
          currentBalance: `${available / 100} ${beneficiary.currency}`,
          amount: `${amountMinor / 100} ${beneficiary.currency}`,
          timeStamp: formatDateTime(today),
          year: today.getFullYear().toString(),
        };

        const html = render(template, htmlData);

        await this.emailQueue.add(
          'email',
          {
            from: 'Makerverse',
            to: '<EMAIL>',
            subject: 'Insufficient funds in paystack',
            body: html,
          },
          { removeOnComplete: true, removeOnFail: true },
        );
      }
    }

    const jobPayload: InitiateTransferJob = {
      user_id: claim.id,
      wallet_id: wallet.id,
      debit_amount: debitMinor,
      amount: amountMinor,
      fee: feeMinor,
      conversion_rate: baseRate,
      beneficiary_id: beneficiary.id,
      community_id: claim.community_id,
      currency: beneficiary.currency,
      description: payload.description,
      payment_method: PaymentMethod.BANK_TRANSFER,
      payment_type: PaymentType.WITHDRAWAL,
      reference: transferRef,
      external_beneficiary_id: beneficiary.metadata.external_id,
    };

    await this.transferQueue.add('initiate_transfer_job', jobPayload, {
      delay: 12 * DURATION.HOURS,
      removeOnComplete: true,
    });

    this.logger.log(
      `Withdrawal initiated for user ${claim.id} to beneficiary ${beneficiary.id}`,
    );

    return new HttpResponseDto({
      message: 'Withdrawal initiated...',
    });
  }
}
