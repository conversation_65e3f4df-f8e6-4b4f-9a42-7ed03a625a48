import {
  applyFileFilter,
  FileMiddleware,
  fileTypeFilter,
} from '@app/http/middlewares/file.middleware';
import { inCategory } from '@app/http/middlewares/user-category.middleware';
import { autoValidate } from '@app/http/middlewares/validation.middleware';
import { DURATION, FileSize } from '@app/internal/enums';
import {
  MIDDLEWARE_TOKENS,
  MODULE_TOKENS,
  SERVICE_TOKENS,
} from '@app/internal/ioc/tokens';
import {
  ReleaseService,
  UpdateReleaseData,
} from '@app/services/release/release.service';
import { User, UserCategory } from '@app/services/user/entities/user.entity';
import { inject } from 'inversify';
import {
  controller,
  httpGet,
  httpPatch,
  httpPost,
  queryParam,
  request as httpReq,
  requestBody,
  requestParam,
} from 'inversify-express-utils';
import { NewReleaseDto } from './dto/new-release.dto';
import { Claim, RequestWithClaims } from '@app/internal/types';
import { StatusCodes } from 'http-status-codes';
import { ApplicationError } from '@app/internal/errors';
import * as path from 'path';
import { S3 } from '@app/modules/s3';
import {
  CountryListResponse,
  DistributorStoreId,
  DspStatus,
  GetDistributionStatusResponse,
  Revelator,
  SetDistributionOptionsPayload,
  TrackObject,
} from '@app/modules/revelator';
import { TrackService } from '@app/services/track/track.service';
import { GetCreatorReleasesDto } from './dto/get-creator-releases.dto';
import { ListReleaseDto } from './dto/list-release.dto';
import { SuccessResponseDto } from '@app/http/dtos/http-response.dto';
import { extractFilename } from '@app/utils/file.utils';
import { ArtistService } from '@app/services/artist/artist.service';
import { Artist } from '@app/services/artist/entity/artist.entity';
import { formatDateToMMDDYYYY } from '@app/utils/date-utils';
import { IdDto, OptionalIdFiltersDto } from '@app/http/dtos/id.dto';
import { DistributeReleaseDto } from './dto/distribute-release.dto';
import {
  DistributionStatus,
  Release,
} from '@app/services/release/entities/release.entity';
import { LabelService } from '@app/services/label/label.service';
import { Label } from '@app/services/label/entitiy/label.entity';
import { UserDescription, UserService } from '@app/services/user/user.service';
import { UpdateReleaseDto } from '@app/http/controllers/release/dto/update-release.dto';
import { Track } from '@app/services/track/entities/track.entity';
import sharp from 'sharp';
import { GetReleaseDspStatusDto } from '@app/http/controllers/release/dto/get-release-dsp-status.dto';
import { hasPermissions } from '@app/http/middlewares/permissions.middleware';
import { PermissionResource } from '@app/utils/permissions.utils';
import { TrackSplitDto } from '@app/http/controllers/track/dto/track-split.dto';
import { OffsetPaginationResult } from '@app/internal/postgres/pagination';
import Deasyncify from 'deasyncify';
import { AxiosError } from 'axios';

@controller('/release')
export class ReleaseController {
  constructor(
    @inject(SERVICE_TOKENS.ArtistService)
    private readonly artist: ArtistService,
    @inject(SERVICE_TOKENS.LabelService) private readonly label: LabelService,
    @inject(MODULE_TOKENS.S3) private readonly s3: S3,
    @inject(SERVICE_TOKENS.ReleaseService)
    private readonly release: ReleaseService,
    @inject(MODULE_TOKENS.Revelator)
    private readonly revelator: Revelator,
    @inject(SERVICE_TOKENS.TrackService) private readonly track: TrackService,
    @inject(SERVICE_TOKENS.UserService) private readonly user: UserService,
  ) {}

  @httpGet(
    '/',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([
      UserCategory.COMMUNITY_OWNER,
      UserCategory.TEAM_MEMBER,
      UserCategory.CREATOR,
    ]),
    hasPermissions([
      {
        resource: PermissionResource.Distribution,
        accessLevel: ['viewer', 'editor'],
      },
      {
        resource: PermissionResource.CatalogManagement,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
    autoValidate(ListReleaseDto.validationSchema, 'query'),
    autoValidate(OptionalIdFiltersDto.validationSchema, 'query'),
  )
  public async listReleases(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { user_id, community_id }: OptionalIdFiltersDto,
    @queryParam()
    { search_term, status, page_number, result_per_page }: ListReleaseDto,
  ) {
    const [roleDesc, err] = await Deasyncify.watch(
      this.simplifyProxyQuery(claim, user_id),
    );

    if (err != null) {
      if (err.code !== StatusCodes.BAD_REQUEST) throw err;
    }

    let releases: OffsetPaginationResult<Release>;

    if (roleDesc?.community_level_user) {
      releases = await this.release.listReleases({
        status,
        page_number,
        result_per_page,
        search_term,
        community_id: roleDesc.community_id,
      });
    } else if (claim.admin_auth) {
      releases = await this.release.listReleases({
        status,
        page_number,
        result_per_page,
        search_term,
        user_id: roleDesc?.team_owner?.id ?? roleDesc?.id,
        community_id,
      });
    } else {
      releases = await this.release.listReleases({
        status,
        page_number,
        result_per_page,
        search_term,
        user_id: roleDesc.team_owner?.id ?? roleDesc.id,
      });
    }

    return new SuccessResponseDto({ data: releases });
  }

  @httpGet(
    '/community/creator/:id',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([UserCategory.COMMUNITY_OWNER, UserCategory.TEAM_MEMBER]),
    hasPermissions([
      {
        resource: PermissionResource.Distribution,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
    autoValidate(IdDto.validationSchema, 'params'),
    autoValidate(GetCreatorReleasesDto.validationSchema, 'query'),
  )
  public async getCommunityCreatorReleases(
    @requestParam() { id }: IdDto,
    @httpReq() { claim }: RequestWithClaims,
    @queryParam()
    { page_number, result_per_page, status }: GetCreatorReleasesDto,
  ) {
    const creator = await this.user.getById(id);

    if (
      !creator ||
      creator?.community_id !== claim.community_id ||
      creator?.category !== UserCategory.CREATOR
    ) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Creator not found');
    }

    const paginatedReleases = await this.release.getCreatorReleases(id, {
      page_number,
      result_per_page,
      status,
    });

    return new SuccessResponseDto({ data: paginatedReleases });
  }

  @httpPost(
    '/new',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    MIDDLEWARE_TOKENS.SubscriptionCheckMiddleware,
    inCategory([UserCategory.CREATOR, UserCategory.TEAM_MEMBER]),
    hasPermissions([
      {
        resource: PermissionResource.Distribution,
        accessLevel: ['editor'],
      },
    ]),
    FileMiddleware(
      'single',
      { field_name: 'cover_art' },
      {
        limits: { fileSize: 10 * FileSize.MB },
        fileFilter: applyFileFilter([
          fileTypeFilter(['.jpg', '.png', '.jpeg']),
        ]),
      },
    ),
    autoValidate(NewReleaseDto.validationSchema),
  )
  public async newRelease(
    @httpReq() req: RequestWithClaims,
    @requestBody() payload: NewReleaseDto,
  ) {
    const { claim, file } = req;

    const {
      label_id,
      tracks: selectedTracks,
      artists: selectedArtists,
      title_version,
      localized_releases,
    } = payload.details;

    payload.details.label_id = label_id || undefined;

    let label: Label;
    const user = await this.user.getById(claim?.team_id ?? claim.id);

    if (payload.details.label_id) {
      label = await this.label.get(label_id, user.id);

      if (!label) {
        throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Invalid label id');
      }
    }

    payload.details.title_version = title_version || undefined;

    if (localized_releases?.length > 0) {
      localized_releases.forEach((local) => {
        local.version = local.version || undefined;

        const versionPresent = !['', null, undefined].includes(title_version);
        const localVersionPresent = !['', null, undefined].includes(
          local.version,
        );

        if (versionPresent && !localVersionPresent) {
          throw new ApplicationError(
            StatusCodes.BAD_REQUEST,
            'Version on your release must be present since you are creating a release version',
          );
        } else if (!versionPresent && localVersionPresent) {
          local.version = undefined;
        }
      });
    }

    const [tracksInDb, { mainArtist, otherArtist }] = await Promise.all([
      this.validateSelectedTracks(selectedTracks),
      this.validateSelectedArtists(selectedArtists),
    ]);

    if (!file) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        "File 'cover_art' is required",
      );
    }

    const fileExtension = path.extname(req.file.originalname);

    const coverArtImg = await this.dynamicallyResizeCoverArt(file.buffer);

    const timestampedFilename = `${extractFilename(
      file.originalname,
    )}_${new Date().toISOString()}${fileExtension}`;

    const [url, revelatorUploadId] = await Promise.all([
      this.s3.upload({
        Key: `${user.id}/release/cover-art/${timestampedFilename}`,
        Body: coverArtImg,
        ContentType: file.mimetype,
      }),
      this.revelator.uploadImage(user.revelator_id, {
        coverImage: true,
        file: {
          name: timestampedFilename,
          buffer: coverArtImg,
          contentType: file.mimetype,
        },
      }),
    ]);

    const releasesLocals = [];

    if (payload.details.localized_releases?.length > 0) {
      for (const local of payload.details.localized_releases) {
        releasesLocals.push({
          name: local.title,
          version: local.version,
          languageId: local.language,
        });
      }
    }

    const tracks = tracksInDb.map((t) => {
      let copyrightP: string;
      if (t.copyright) {
        copyrightP = `${t.copyright.year} ${t.copyright.entity}`;
      }

      const mainTrackArtist = t.artists.find((a) => a.is_main);
      const otherTrackArtists = t.artists.filter((a) => !a.is_main);

      const contributors = [];

      for (const artist of otherTrackArtists) {
        contributors.push({
          roleId: artist.role,
          artist: {
            artistId: Number(artist.revelator_id),
            artistLocals:
              artist.localized_names?.map((local) => ({
                name: local.name,
                languageId: local.language,
              })) ?? [],
            artistExternalIds: [
              {
                distributorStoreId: DistributorStoreId.APPLE_MUSIC,
                profileId: artist.apple_music_id,
              },
              {
                distributorStoreId: DistributorStoreId.SPOTIFY,
                profileId: artist.spotify_id,
              },
            ],
          },
        });
      }

      let primaryMusicStyleId: number;
      let secondaryMusicStyleId: number;

      if (t.primary_genre) {
        primaryMusicStyleId = Number(t.primary_genre);
      }

      if (t.secondary_genre) {
        secondaryMusicStyleId = Number(t.secondary_genre);
      }

      return new TrackObject({
        name: t.title,
        languageId: Number(t.language),
        version: t.version,
        trackType: t.type,
        trackProperties: t.properties?.length > 0 ? t.properties : [1],
        tracksLocals:
          t.localized_titles?.map((local) => ({
            name: local.title,
            version: local.version,
            languageId: local.language,
          })) ?? [],
        artistId: mainTrackArtist ? Number(mainTrackArtist.revelator_id) : null,
        artistAppleId: mainTrackArtist?.apple_music_id,
        artistSpotifyId: mainTrackArtist?.spotify_id,
        explicit: t.explicit,
        isrc: t.isrc,
        lyrics: t.lyrics,
        previewStartSeconds: t.start_time / DURATION.SECONDS,
        copyrightP,
        primaryMusicStyleId,
        secondaryMusicStyleId,
        wav: {
          fileId: t.audio_metadata.file_id,
          filename: t.audio_metadata.filename,
        },
        composerContentsDTO:
          t.publisher?.map((writer) => {
            const composerContent: any = {
              id: writer.id,
              composerName: writer.name,
              roleId: writer.role,
              rightsId: writer.publishing_type,
              share: writer.share,
            };

            if (writer.publishing_type === 3) {
              composerContent.publisherName = writer.publication_name;
              composerContent.publisherId = writer.publication_id;
            }

            return composerContent;
          }) ?? [],
        contributors,
      });
    });

    let copyrightP: string;
    let copyrightC: string;

    if (payload.details.copyright?.p) {
      copyrightP = `${payload.details.copyright.p.year} ${payload.details.copyright.p.entity}`;
    }

    if (payload.details.copyright?.c) {
      copyrightC = `${payload.details.copyright.c.year} ${payload.details.copyright.c.entity}`;
    }

    const contributors = [];

    for (const artist of otherArtist) {
      contributors.push({
        roleId: artist.role,
        artist: {
          artistId: Number(artist.revelator_id),
          artistLocals: artist?.localized_names?.map((local) => ({
            name: local.name,
            languageId: local.language,
          })),
          artistExternalIds: [
            {
              distributorStoreId: DistributorStoreId.APPLE_MUSIC,
              profileId: artist.apple_music_id,
            },
            {
              distributorStoreId: DistributorStoreId.SPOTIFY,
              profileId: artist.spotify_id,
            },
          ],
        },
      });
    }

    let releaseDate: string;
    let upc: number;

    if (payload.details.previously_released) {
      releaseDate = formatDateToMMDDYYYY(payload.details.previous_release_date);
      upc = Number(payload.details.release_code);
    }

    let secondaryMusicStyleId: number;
    let secondary_genre: string;

    if (payload.details.secondary_genre) {
      secondary_genre = String(payload.details.secondary_genre);

      secondaryMusicStyleId = payload.details.secondary_genre;
    }

    const image = {
      fileId: revelatorUploadId,
      filename: `${extractFilename(
        file.originalname,
      )}_${new Date().toISOString()}.jpg`,
    };

    const res = await this.revelator.saveRelease(user.revelator_id, {
      name: payload.details.title,
      version: payload.details.title_version,
      languageId: payload.details.language,
      artistId: mainArtist?.revelator_id
        ? Number(mainArtist.revelator_id)
        : null,
      contributors,
      previouslyReleased: payload.details.previously_released,
      releaseDate,
      upc,
      releasesLocals,
      tracks,
      primaryMusicStyleId: payload.details.primary_genre,
      secondaryMusicStyleId,
      artistAppleId: mainArtist?.apple_music_id,
      artistSpotifyId: mainArtist?.spotify_id,
      copyrightP,
      copyrightC,
      labelId: label ? Number(label.revelator_id) : undefined,
      hasRecordLabel: !!label,
      image,
    });

    const newRelease = await this.release.create({
      title: payload.details.title,
      version: payload.details.title_version,
      user_id: user.id,
      cover_art: url,
      cover_art_metadata: {
        file_id: image.fileId,
        filename: image.filename,
        file_size: file.size,
      },
      copyright: payload.details.copyright,
      label_id: payload.details.label_id,
      was_previously_released: payload.details.previously_released,
      release_code: payload.details.previously_released
        ? payload.details.release_code
        : null,
      language: String(payload.details.language),
      primary_genre: String(payload.details.primary_genre),
      secondary_genre,
      revelator_release_id: res.releaseId,
      release_locals: payload.details.localized_releases,
      catalog_id: payload.details.catalog_id,
      tracks: selectedTracks,
      artists: selectedArtists,
    });

    return new SuccessResponseDto({ data: newRelease });
  }

  @httpPost(
    '/distribute/:id',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    MIDDLEWARE_TOKENS.SubscriptionCheckMiddleware,
    autoValidate(IdDto.validationSchema, 'params'),
    autoValidate(DistributeReleaseDto.validationSchema, 'body'),
    inCategory([UserCategory.CREATOR, UserCategory.TEAM_MEMBER]),
    hasPermissions([
      {
        resource: PermissionResource.Distribution,
        accessLevel: ['editor'],
      },
    ]),
  )
  public async distributeRelease(
    @requestParam() { id }: IdDto,
    @httpReq() { claim }: RequestWithClaims,
    @requestBody() payload: DistributeReleaseDto,
  ) {
    const user = await this.user.getById(claim?.team_id ?? claim.id);
    const release = await this.release.get(id, user.id);

    if (!release) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Release not found');
    }

    if (
      release.in_distribution_queue &&
      release.status === DistributionStatus.PROCESSING
    ) {
      throw new ApplicationError(
        StatusCodes.CONFLICT,
        'Release already in distribution queue',
      );
    }
    // validate release with revelator

    const validationErrors = await this.revelator.validateRelease(
      user.revelator_id,
      Number(release.revelator_release_id),
    );

    // TODO: push this to distribution logs and check for severity
    if (validationErrors?.length > 0) {
      throw new ApplicationError(
        StatusCodes.CONFLICT,
        'There seems to issues with some of the data on your release',
        validationErrors.map((err) => ({
          field: err.field,
          message: err.errorMessage,
          value: err.value,
        })),
      );
    }

    // save distribution options
    const distributionOptions: SetDistributionOptionsPayload = {
      releaseId: Number(release.revelator_release_id),
      monetizationPolicyIds: payload.monetization_policy.map(
        (p) => p.policy_id,
      ),
      priceTierIds: payload.track_retail_price_tiers.map(
        (p) => p.price_tier_id,
      ),
    };

    if (!payload.distribute_immediately) {
      const distributeAt = payload.distribute_at.toISOString().split('T');

      distributionOptions.saleStartDate = distributeAt.at(0);

      if (payload.specify_timezone) {
        distributionOptions.saleStartTimezoneId = payload.specific_timezone;

        distributionOptions.saleStartTime = distributeAt
          .at(1)
          .split(':')
          .splice(0, 2)
          .join(':');
      }

      if (payload.pre_order_date) {
        distributionOptions.salePreOrderDate = formatDateToMMDDYYYY(
          payload.pre_order_date,
        );
      }
    }

    const needsCountryList =
      payload.include_countries?.length > 0 ||
      payload.exclude_countries?.length > 0;

    let countryList: CountryListResponse;

    if (needsCountryList) {
      countryList = await this.revelator.listCountries();
    }

    if (payload.include_countries?.length > 0) {
      distributionOptions.countriesIncluded = payload.include_countries.map(
        (country, idx) => {
          const countryFromList = countryList.find(
            (c) => c.isO2Code === country.country_code,
          );

          if (!countryFromList) {
            throw new ApplicationError(
              StatusCodes.BAD_REQUEST,
              `Incorrect country code at include_countries[${idx}]`,
            );
          }

          return { countryId: countryFromList.countryId };
        },
      );
    }

    if (payload.exclude_countries?.length > 0) {
      distributionOptions.countriesExcluded = payload.exclude_countries.map(
        (country, idx) => {
          const countryFromList = countryList.find(
            (c) => c.isO2Code === country.country_code,
          );

          if (!countryFromList) {
            throw new ApplicationError(
              StatusCodes.BAD_REQUEST,
              `Incorrect country code at exclude_countries[${idx}]`,
            );
          }

          return { countryId: countryFromList.countryId };
        },
      );
    }

    await this.revelator.setDistributionOptions(
      user.revelator_id,
      distributionOptions,
    );

    const [, distributionError] = await Deasyncify.watch(
      this.revelator.addDistributionToQueue(user.revelator_id, {
        releaseId: Number(release.revelator_release_id),
        stores: payload.stores.map((store) => store.distributor_store_id),
      }),
    );

    if (distributionError != null) {
      if (
        distributionError instanceof AxiosError &&
        distributionError?.response?.status === 400
      ) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          distributionError?.response?.data?.error,
        );
      }

      throw distributionError;
    }

    await this.release.update(release.id, {
      status: DistributionStatus.PROCESSING,
      in_distribution_queue: true,
    });

    return new SuccessResponseDto();
  }

  @httpGet(
    '/:id',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([
      UserCategory.COMMUNITY_OWNER,
      UserCategory.TEAM_MEMBER,
      UserCategory.CREATOR,
    ]),
    hasPermissions([
      {
        resource: PermissionResource.Distribution,
        accessLevel: ['editor', 'viewer'],
      },
      {
        resource: PermissionResource.CatalogManagement,
        accessLevel: ['editor', 'viewer'],
      },
    ]),
    autoValidate(IdDto.validationSchema, 'params'),
  )
  public async releaseInfo(
    @requestParam() { id }: IdDto,
    @httpReq() { claim }: RequestWithClaims,
  ) {
    let release: Release;

    const roleDesc = await this.user.describe(claim.id);

    let partnerUserId = roleDesc?.revelator_id;

    if (roleDesc?.community_level_user) {
      release = await this.release.getById(id);

      const releaseCreator = await this.user.getById(release.user_id);

      partnerUserId = releaseCreator.revelator_id;

      if (releaseCreator.community_id !== claim.community_id) {
        throw new ApplicationError(
          StatusCodes.FORBIDDEN,
          'You do not have the required permissions to access this release',
        );
      }
    } else if (claim.admin_auth) {
      release = await this.release.get(id);
    } else {
      release = await this.release.get(id, claim?.team_id ?? claim.id);
    }

    if (!release) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Release not found');
    }

    let label = await this.label.getById(release.label_id);

    const [res] = await Deasyncify.watch(
      this.revelator.getDistributionStatus(partnerUserId, {
        releaseId: Number(release.revelator_release_id),
      }),
    );

    const statuses = this.parseDSPStatus(res);

    const [releaseContent, err] = await Deasyncify.watch(
      this.revelator.getReleaseContent(
        partnerUserId,
        Number(release.revelator_release_id),
      ),
    );

    if (!release.release_code) {
      await this.release.update(release.id, {
        release_code: releaseContent?.upc ? String(releaseContent.upc) : null,
      });

      release.release_code = releaseContent?.upc
        ? String(releaseContent.upc)
        : null;
    }

    if (err == null && label == null && releaseContent?.labelId) {
      label = await this.label.getByRevelatorId(String(releaseContent.labelId));

      if (label) {
        await this.release.update(release.id, {
          label_id: label.id,
        });
      } else {
        const labelContent = await this.revelator.getLabel(
          partnerUserId,
          releaseContent.labelId,
        );

        let image: string;

        if (labelContent.image != null) {
          image = `https://cdn.revelator.com/images/${labelContent.image.fileId}/file.jpg`;
        }

        label = await this.label.create({
          name: labelContent.name,
          description: labelContent.description,
          image,
          user_id: claim.id,
          revelator_id: String(releaseContent.labelId),
        });

        await this.release.update(release.id, {
          label_id: label.id,
        });
      }
    }

    return new SuccessResponseDto({ data: { ...release, label, statuses } });
  }

  // helpers

  private async validateSelectedTracks(
    selectedTracks: { id: string }[],
  ): Promise<
    (Track & {
      artists: (Artist & {
        role: number;
        is_primary: boolean;
        is_main: boolean;
      })[];
    })[]
  > {
    let idx = 0;

    const orderedTracks: (Track & {
      artists: (Artist & {
        role: number;
        is_primary: boolean;
        is_main: boolean;
      })[];
    })[] = new Array(selectedTracks.length);

    const tracksInDb = await this.track.getManyById(
      selectedTracks.map((t) => t.id),
    );

    for (const track of selectedTracks) {
      const trackInDb = tracksInDb.find((t) => t.id === track.id);

      if (!trackInDb) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          `Invalid track id at tracks[${idx}]`,
        );
      }

      orderedTracks[idx] = trackInDb;

      idx++;
    }

    return orderedTracks;
  }

  private async validateSelectedArtists(
    selectedArtists: {
      id: string;
      role: number;
      is_primary: boolean;
      is_main: boolean;
    }[],
  ) {
    let idx = 0;

    let mainArtist: Artist;

    const otherArtist: (Artist & {
      role: number;
      is_main: boolean;
      is_primary: boolean;
    })[] = [];

    if (selectedArtists.length === 0) {
      return { mainArtist, otherArtist };
    }

    const artistsInDb = await this.artist.getManyById(
      selectedArtists.map((a) => a.id),
    );

    const contributorRole = await this.revelator
      .listContributorRoles()
      .then((d) => d.filter((c) => c.contributorRoleGroupId === 3));

    let hasInProductAndEngineeringGroup = false;

    for (const artist of selectedArtists) {
      const artistInDB = artistsInDb.find((a) => a.id === artist.id);

      if (!artistInDB) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          `Invalid artist id at artists[${idx}]`,
        );
      }

      if (artist.is_main) {
        mainArtist = artistInDB;
      } else {
        otherArtist.push({ ...artistInDB, ...artist });
      }

      if (!hasInProductAndEngineeringGroup) {
        if (contributorRole.findIndex((c) => c.roleId === artist.role) > -1) {
          hasInProductAndEngineeringGroup = true;
        }
      }

      idx++;
    }

    if (!hasInProductAndEngineeringGroup) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'At least one contributor must be in the Product & Engineering group',
      );
    }

    return { mainArtist, otherArtist };
  }

  @httpPatch(
    '/:id',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    MIDDLEWARE_TOKENS.SubscriptionCheckMiddleware,
    inCategory([UserCategory.CREATOR, UserCategory.TEAM_MEMBER]),
    hasPermissions([
      {
        resource: PermissionResource.Distribution,
        accessLevel: ['editor'],
      },
      {
        resource: PermissionResource.CatalogManagement,
        accessLevel: ['editor'],
      },
    ]),
    FileMiddleware(
      'single',
      { field_name: 'cover_art' },
      {
        limits: { fileSize: 10 * FileSize.MB },
        fileFilter: applyFileFilter([
          fileTypeFilter(['.jpg', '.png', '.jpeg']),
        ]),
      },
    ),
    autoValidate(IdDto.validationSchema, 'params'),
    autoValidate(UpdateReleaseDto.validationSchema),
  )
  public async updateRelease(
    @requestParam() { id }: IdDto,
    @httpReq() { file, claim }: RequestWithClaims,
    @requestBody() payload: UpdateReleaseDto,
  ) {
    const user = await this.user.getById(claim?.team_id ?? claim.id);
    const release = await this.release.get(id, user.id);

    if (!release) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Release not found');
    }

    if (release.in_distribution_queue) {
      throw new ApplicationError(
        StatusCodes.CONFLICT,
        'Cannot edit release that has been distributed',
      );
    }

    if (!payload.details && !file) {
      return;
    }

    if (!payload.details) {
      (payload.details as any) = {};
    }

    const {
      title,
      title_version: version,
      language,
      primary_genre,
      secondary_genre,
      catalog_id,
      copyright,
      tracks: trackSelectionUpdate,
      artists: artistSelectionUpdate,
      label_id,
    } = payload.details;

    const singularFields: Partial<Release> = {
      title,
      version,
      language: String(language),
      primary_genre: String(primary_genre),
      secondary_genre: String(secondary_genre),
      catalog_id,
      copyright,
    };

    const fieldsToUpdate: Partial<typeof singularFields> & UpdateReleaseData =
      {};

    payload.details.label_id = label_id || undefined;

    let label: Label;

    if (payload.details.label_id) {
      label = await this.label.get(label_id, user.id);

      if (!label) {
        throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Invalid label id');
      }

      fieldsToUpdate.label_id = label.id;
    }

    for (const key in singularFields) {
      if (!['', null, undefined].includes(singularFields[key])) {
        fieldsToUpdate[key] = singularFields[key];
      }
    }

    let tracksInDb: (Track & {
      artists: (Artist & {
        role: number;
        is_primary: boolean;
        is_main: boolean;
      })[];
    })[] = [];

    if (trackSelectionUpdate) {
      tracksInDb = await this.validateSelectedTracks(trackSelectionUpdate);
    }

    let mainArtist: Artist;
    let otherArtist: (Artist & {
      role: number;
      is_main: boolean;
      is_primary: boolean;
    })[];

    if (artistSelectionUpdate) {
      const artistObj = await this.validateSelectedArtists(
        artistSelectionUpdate.filter((a) => !a.delete),
      );

      fieldsToUpdate.artists = artistSelectionUpdate;

      mainArtist = artistObj.mainArtist;
      otherArtist = artistObj.otherArtist;
    }

    let image = {
      fileId: release.cover_art_metadata?.file_id,
      filename: release.cover_art_metadata?.filename,
    };

    if (file) {
      const coverArtImg = await this.dynamicallyResizeCoverArt(file.buffer);

      const fileExtension = path.extname(file.originalname);

      const timestampedFilename = `${extractFilename(
        file.originalname,
      )}_${new Date().toISOString()}${fileExtension}`;

      const [url, revelatorUploadId] = await Promise.all([
        this.s3.upload({
          Key: `${user.id}/release/cover-art/${timestampedFilename}`,
          Body: coverArtImg,
          ContentType: file.mimetype,
        }),
        this.revelator.uploadImage(user.revelator_id, {
          coverImage: true,
          file: {
            name: timestampedFilename,
            buffer: coverArtImg,
            contentType: file.mimetype,
          },
        }),
      ]);

      fieldsToUpdate.cover_art = url;
      fieldsToUpdate.cover_art_metadata = {
        file_id: image.fileId,
        filename: image.filename,
        file_size: file.size,
      };

      image = {
        fileId: revelatorUploadId,
        filename: `${extractFilename(
          file.originalname,
        )}_${new Date().toISOString()}.jpg`,
      };
    }

    const releasesLocals = [];

    if (payload.details.localized_releases?.length > 0) {
      for (const local of payload.details.localized_releases) {
        releasesLocals.push({
          name: local.title,
          version: local.version,
          languageId: local.language,
        });
      }

      Object.assign(fieldsToUpdate, {
        release_locals: payload.details.localized_releases,
      });
    }

    const tracks = tracksInDb.map((t) => {
      let copyrightP: string;

      if (t.copyright) {
        copyrightP = `${t.copyright.year} ${t.copyright.entity}`;
      }

      const mainTrackArtist = t.artists.find((a) => a.is_main);
      const otherTrackArtists = t.artists.filter((a) => !a.is_main);

      const contributors = [];

      if (otherTrackArtists.length > 0) {
        for (const artist of otherTrackArtists) {
          contributors.push({
            roleId: artist.role,
            artist: {
              artistId: Number(artist.revelator_id),
              artistLocals:
                artist.localized_names?.map((local) => ({
                  name: local.name,
                  languageId: local.language,
                })) ?? [],
              artistExternalIds: [
                {
                  distributorStoreId: DistributorStoreId.APPLE_MUSIC,
                  profileId: artist.apple_music_id,
                },
                {
                  distributorStoreId: DistributorStoreId.SPOTIFY,
                  profileId: artist.spotify_id,
                },
              ],
            },
          });
        }
      }

      let primaryMusicStyleId: number;
      let secondaryMusicStyleId: number;

      if (t.primary_genre) {
        primaryMusicStyleId = Number(t.primary_genre);
      }

      if (t.secondary_genre) {
        secondaryMusicStyleId = Number(t.secondary_genre);
      }

      return new TrackObject({
        name: t.title,
        languageId: Number(t.language),
        version: t.version,
        trackType: t.type,
        trackProperties: t.properties?.length > 0 ? t.properties : [1],
        tracksLocals:
          t.localized_titles?.map((local) => ({
            name: local.title,
            version: local.version,
            languageId: local.language,
          })) ?? [],
        artistId: Number(mainTrackArtist.revelator_id),
        artistAppleId: mainArtist.apple_music_id,
        artistSpotifyId: mainArtist.spotify_id,
        explicit: t.explicit,
        isrc: t.isrc,
        lyrics: t.lyrics,
        previewStartSeconds: t.start_time / DURATION.SECONDS,
        copyrightP,
        primaryMusicStyleId,
        secondaryMusicStyleId,
        wav: {
          fileId: t.audio_metadata.file_id,
          filename: t.audio_metadata.filename,
        },
        composerContentsDTO:
          t.publisher?.map((composer) => ({
            composerName: composer.name,
            roleId: composer.role,
            rightsId: composer.publishing_type,
            share: composer.share,
          })) ?? [],
        contributors,
      });
    });

    const copyrightP = `${
      payload.details.copyright.p.year ?? release.copyright.p.year
    } ${payload.details.copyright.p.entity ?? release.copyright.p.entity}`;

    const copyrightC = `${
      payload.details.copyright.c.year ?? release.copyright.c.year
    } ${payload.details.copyright.c.entity ?? release.copyright.c.entity}`;

    const contributors = [];

    if (otherArtist.length > 0) {
      for (const artist of otherArtist) {
        contributors.push({
          roleId: artist.role,
          artist: {
            artistId: Number(artist.revelator_id),
            artistLocals: artist?.localized_names?.map((local) => ({
              name: local.name,
              languageId: local.language,
            })),
            artistExternalIds: [
              {
                distributorStoreId: DistributorStoreId.APPLE_MUSIC,
                profileId: artist.apple_music_id,
              },
              {
                distributorStoreId: DistributorStoreId.SPOTIFY,
                profileId: artist.spotify_id,
              },
            ],
          },
        });
      }
    }

    let releaseDate: string;
    let upc: number;

    if (payload.details.previously_released) {
      releaseDate = formatDateToMMDDYYYY(payload.details.previous_release_date);
      upc = Number(payload.details.release_code);
    }

    let secondaryMusicStyleId: number;

    if (payload.details.secondary_genre) {
      secondaryMusicStyleId = payload.details.secondary_genre;
    }

    const releaseContent = await this.revelator.getReleaseContent(
      user.revelator_id,
      Number(release.revelator_release_id),
    );

    await this.revelator.saveRelease(user.revelator_id, {
      releaseId: Number(release.revelator_release_id),
      name: payload.details.title,
      version: payload.details.title_version,
      languageId: payload.details.language,
      artistId: Number(mainArtist.revelator_id),
      contributors,
      previouslyReleased: payload.details.previously_released,
      releaseDate: releaseDate ?? releaseContent.releaseDate,
      upc,
      releasesLocals,
      tracks,
      primaryMusicStyleId: payload.details.primary_genre,
      secondaryMusicStyleId,
      artistAppleId: mainArtist.apple_music_id,
      artistSpotifyId: mainArtist.spotify_id,
      copyrightP,
      copyrightC,
      labelId: label ? Number(label.revelator_id) : undefined,
      hasRecordLabel: !!label,
      image,
    });

    const updatedRelease = await this.release.update(
      release.id,
      fieldsToUpdate,
    );

    return new SuccessResponseDto({ data: updatedRelease });
  }

  @httpGet(
    '/distribution/status/:id',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([
      UserCategory.COMMUNITY_OWNER,
      UserCategory.CREATOR,
      UserCategory.TEAM_MEMBER,
    ]),
    hasPermissions([
      {
        resource: PermissionResource.Distribution,
        accessLevel: ['editor', 'viewer'],
      },
      {
        resource: PermissionResource.CatalogManagement,
        accessLevel: ['editor', 'viewer'],
      },
    ]),
    autoValidate(IdDto.validationSchema, 'params'),
  )
  public async fetchDSPStatus(
    @requestParam() { id }: IdDto,
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { status }: GetReleaseDspStatusDto,
  ) {
    const user = await this.user.getById(claim?.team_id ?? claim.id);

    let partnerUserId = user.revelator_id;

    let release: Release;

    const roleDesc = await this.user.describe(claim.id);

    if (roleDesc.community_level_user) {
      release = await this.release.getById(id);

      const releaseCreator = await this.user.getById(release.user_id);

      if (releaseCreator.community_id !== claim.community_id) {
        throw new ApplicationError(
          StatusCodes.FORBIDDEN,
          'You do not have the required permissions to access this release',
        );
      }

      partnerUserId = releaseCreator.revelator_id;
    } else {
      release = await this.release.get(id, claim?.team_id ?? claim.id);
    }

    if (!release) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Release not found');
    }

    const res = await this.revelator.getDistributionStatus(partnerUserId, {
      releaseId: Number(release.revelator_release_id),
    });

    let statuses = this.parseDSPStatus(res);

    if (status) {
      statuses = statuses.filter((s) => s.status === status);
    }

    return new SuccessResponseDto({ data: statuses });
  }

  @httpGet(
    '/track-split/:id',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    autoValidate(IdDto.validationSchema, 'params'),
  )
  public async fetchSplits(
    @httpReq() { claim }: RequestWithClaims,
    @requestParam() { id }: IdDto,
  ) {
    let user: User;
    let release: Release & {
      tracks: Track[];
      artists: {
        id: string;
        role: number;
        is_primary: boolean;
        is_main: boolean;
      }[];
    };

    if (claim.admin_auth) {
      release = await this.release.get(id);

      user = await this.user.getById(release.user_id);
    } else {
      user = await this.user.getById(claim?.team_id ?? claim.id);

      release = await this.release.get(id, user.id);
    }

    if (!release) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Release not found');
    }

    const releaseContent = await this.revelator.getReleaseContent(
      user.revelator_id,
      Number(release.revelator_release_id),
    );

    const splits = await this.revelator.getTrackSplits(
      user.revelator_id,
      Number(release.revelator_release_id),
    );

    const splitList: any[] = [];

    for (const split of splits) {
      const trackContent = releaseContent.tracks.find(
        (t) => t.trackId === split.trackId,
      );

      const track = release.tracks.find(
        (t) => t.audio_metadata.file_id === trackContent.wav.fileId,
      );

      splitList.push({
        track_id: track.id,
        cover_art: release.cover_art,
        title: track.title,
        artist_name: split.artist.name,
        duration: track.duration,
        splits: split.splits.map((s) => ({
          track_id: track.id,
          payee_id: s.payeeId,
          payee_email: s.payeeEmail,
          payee_name: s.payeeName,
          share_percentage: s.percentShare,
          is_self: s.isChildEnterprisePayee,
        })),
      });
    }

    return new SuccessResponseDto({ data: splitList });
  }

  @httpPost(
    '/track-split/:id',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    MIDDLEWARE_TOKENS.SubscriptionCheckMiddleware,
    autoValidate(IdDto.validationSchema, 'params'),
    autoValidate(TrackSplitDto.validationSchema),
  )
  public async createTrackSplits(
    @httpReq() { claim }: RequestWithClaims,
    @requestParam() { id }: IdDto,
    @requestBody() payload: TrackSplitDto,
  ) {
    const user = await this.user.getById(claim?.team_id ?? claim.id);
    const release = await this.release.get(id, user.id);

    if (!release) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Release not found');
    }

    let idx = 0;

    const splitMetadata: typeof payload.splits & Record<string, any>[] =
      payload.splits;

    for (const split of splitMetadata) {
      const track = release.tracks.find((t) => t.id === split.track_id);

      if (!track) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          `Invalid track id at split[${idx}]`,
        );
      }

      split.track_file_id = track.audio_metadata.file_id;

      idx++;
    }

    const res = await this.revelator.getDistributionStatus(user.revelator_id, {
      releaseId: Number(release.revelator_release_id),
    });

    const statuses = this.parseDSPStatus(res);

    const storeDistributedTo = statuses.find(
      (s) => s.status === DistributionStatus.DISTRIBUTED,
    );

    if (!storeDistributedTo) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'Unable to create Track Split: Your release must be distributed to at least one DSP first',
      );
    }

    const releaseContent = await this.revelator.getReleaseContent(
      user.revelator_id,
      Number(release.revelator_release_id),
    );

    for (const split of splitMetadata) {
      const revelatorTrackMetadata = releaseContent.tracks.find(
        (t) => t.wav.fileId === split.track_file_id,
      );

      split.track_id = String(revelatorTrackMetadata.trackId);
    }

    await this.revelator.saveTrackSplit(
      user.revelator_id,
      splitMetadata.map((split) => {
        if (split.use_self) {
          return {
            isChildEnterprisePayee: true,
            percentShare: split.share_percentage,
            trackId: Number(split.track_id),
          };
        }

        return {
          isChildEnterprisePayee: false,
          payeeName: split.payee_name,
          payeeEmail: split.payee_email,
          percentShare: split.share_percentage,
          trackId: Number(split.track_id),
        };
      }),
    );
  }

  private parseDSPStatus(res: GetDistributionStatusResponse): {
    store_id: number;
    status: DistributionStatus;
    info: string;
    enqueued_on: Date;
    delivered_on: Date;
  }[] {
    let statuses: any[] = [];

    if (res?.items?.length > 0) {
      statuses = res.items.map((s) => {
        let status: DistributionStatus;

        switch (s.releaseStatus.status) {
          case DspStatus.DELIVERED:
          case DspStatus.ON_STORE:
            status = DistributionStatus.DISTRIBUTED;
            break;
          case DspStatus.TAKEDOWN_PROCESSED:
            status = DistributionStatus.TAKEN_DOWN;
            break;
          case DspStatus.REJECTED_BY_INSPECTOR:
          case DspStatus.DOWNLOADING_EXTERNAL_ASSETS_FAILED:
          case DspStatus.TRANSFORMATION_FAILED:
          case DspStatus.VALIDATION_FAILED:
          case DspStatus.CREATING_PACKAGE_FAILED:
          case DspStatus.UPLOADING_FAILED:
          case DspStatus.REMOVING_FAILED:
            status = DistributionStatus.ISSUES;
            break;
          default:
            status = DistributionStatus.PROCESSING;
        }

        return {
          store_id: s.distributorStoreId,
          status,
          info: s.releaseStatus.statusText,
          url: s.releaseStatus.urlInStore,
          enqueued_on: new Date(s.releaseStatus.addedDate),
          delivered_on: s.releaseStatus.deliveryDate
            ? new Date(s.releaseStatus.deliveryDate)
            : null,
        };
      });
    }

    return statuses;
  }

  private async dynamicallyResizeCoverArt(img: Buffer): Promise<Buffer> {
    const { width, height } = await sharp(img).metadata();

    const MIN_DIMENSION = 1400;

    const notSquare = width !== height;

    const shouldResize =
      notSquare || width < MIN_DIMENSION || height < MIN_DIMENSION;

    if (shouldResize) {
      const maxOfDimension = Math.max(width, height);

      if (notSquare && maxOfDimension >= MIN_DIMENSION) {
        return sharp(img)
          .resize(maxOfDimension, maxOfDimension, { fit: 'cover' })
          .toBuffer();
      }

      if (notSquare || maxOfDimension < MIN_DIMENSION) {
        return sharp(img)
          .resize(MIN_DIMENSION, MIN_DIMENSION, { fit: 'cover' })
          .toBuffer();
      }
    }

    return img;
  }

  private async simplifyProxyQuery(claim: Claim, user_id?: string) {
    const roleDesc = await this.user.describe(claim.team_id ?? claim.id);

    const isProxyRequest =
      user_id && (claim.admin_auth || roleDesc?.community_level_user);

    let userDesc: UserDescription;

    if (isProxyRequest) {
      userDesc = await this.user.describe(user_id);

      if (
        !userDesc ||
        (!claim.admin_auth && userDesc.community_id !== claim.community_id)
      ) {
        throw new ApplicationError(StatusCodes.NOT_FOUND, 'User not found');
      }
    } else if (!isProxyRequest && claim.admin_auth) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        "'user_id' is required",
      );
    } else {
      userDesc = roleDesc;
    }

    return userDesc;
  }
}
