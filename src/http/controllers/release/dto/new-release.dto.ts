import Joi from 'joi';
import { ExtendedJoi } from '@app/utils/joi-utils';

// Combined regex for UPC (12 digits) and EAN/JAN (13 digits)
const UPC_EAN_JAN_REGEX = /^(?:\d{12}|\d{13})$/;

export class NewReleaseDetailsDto {
  tracks: {
    id: string;
  }[];
  label_id: string;
  artists: {
    id: string;
    role: number;
    is_primary: boolean;
    is_main: boolean;
  }[];
  language: number;
  title: string;
  title_version: string;
  localized_releases: {
    title: string;
    language: number;
    version: string;
  }[];
  primary_genre: number;
  secondary_genre: number;
  previously_released: boolean;
  previous_release_date: Date;
  release_code: string;
  catalog_id: string;
  copyright: Record<
    'p' | 'c',
    {
      year: number;
      entity: string;
    }
  >;

  static validationSchema = Joi.object<NewReleaseDetailsDto, true>({
    label_id: Joi.string().optional().allow('', null),
    tracks: Joi.array()
      .items(
        Joi.object({
          id: Joi.string().required(),
        }),
      )
      .min(1)
      .required(),
    artists: Joi.array()
      .items(
        Joi.object({
          id: Joi.string().required(),
          role: Joi.number().required(),
          is_primary: Joi.boolean().default(false),
          is_main: Joi.boolean().default(false),
        }),
      )
      .min(1)
      .default([])
      .custom((artists, helpers) => {
        const occurrenceMap: Record<string, boolean> = {};

        let mainArtist: {
          id: string;
          role: number;
          is_primary: boolean;
          is_main: boolean;
        };

        let mainArtistCount = 0;

        for (let idx = 0; idx < artists.length; idx++) {
          const artist = artists[idx];

          if (occurrenceMap[artist.id]) {
            return helpers.message({
              custom: 'Cannot add the same artist more than once',
            });
          }

          occurrenceMap[artist.id] = true;

          if (artist.is_main) {
            mainArtist = artist;
            mainArtistCount++;
          }
        }

        if (mainArtist) {
          if (mainArtistCount > 1) {
            return helpers.message({
              custom: 'There can only be one main artist',
            });
          }

          if (!mainArtist.is_primary) {
            return helpers.message({
              custom: 'Main artist must be primary',
            });
          }

          if (mainArtist.role != 49) {
            return helpers.message({
              custom: 'Main artist must be assigned "main primary artist role"',
            });
          }
        }

        return artists;
      }, 'Primary artist validation'),
    language: Joi.number().integer().required(),
    title: Joi.string().required(),
    title_version: Joi.string().optional().allow('', null),
    localized_releases: Joi.array()
      .items(
        Joi.object({
          language: Joi.number().integer().required(),
          title: Joi.string().required().allow('', null),
          version: Joi.string().optional(),
        }),
      )
      .optional(),
    primary_genre: Joi.number().integer().required(),
    secondary_genre: Joi.number().integer().optional(),
    previously_released: Joi.boolean().required(),
    previous_release_date: ExtendedJoi.date().format('YYYY-MM-DD').optional(),
    release_code: Joi.string().when('previously_released', {
      not: Joi.valid(true),
      then: Joi.optional().allow(null, ''),
      otherwise: Joi.string()
        .pattern(UPC_EAN_JAN_REGEX, 'UPC/EAN/JAN')
        .required(),
    }),
    catalog_id: Joi.string().optional().allow(null, ''),
    copyright: Joi.object({
      p: Joi.object({
        year: Joi.number().integer().min(1951).max(2100).required(),
        entity: Joi.string().required(),
      }).required(),
      c: Joi.object({
        year: Joi.number().integer().min(1951).max(2100).required(),
        entity: Joi.string().required(),
      }).required(),
    }).required(),
  });
}

export class NewReleaseDto {
  details: NewReleaseDetailsDto;

  static validationSchema = Joi.object<NewReleaseDto, true>({
    details: NewReleaseDetailsDto.validationSchema.required(),
  });
}
