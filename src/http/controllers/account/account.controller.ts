import { autoValidate } from '@app/http/middlewares/validation.middleware';
import {
  controller,
  httpGet,
  requestBody,
  request as httpReq,
  httpPost,
} from 'inversify-express-utils';
import { ConnectAccountDto } from './dto/connect-account.dto';
import { VibrateClient } from '@app/modules/vibrate';
import { SuccessResponseDto } from '@app/http/dtos/http-response.dto';
import { inject } from 'inversify';
import {
  MIDDLEWARE_TOKENS,
  MODULE_TOKENS,
  SERVICE_TOKENS,
} from '@app/internal/ioc/tokens';
import { StatusCodes } from 'http-status-codes';
import { ApplicationError } from '@app/internal/errors';
import { UserService } from '@app/services/user/user.service';
import { RequestWithClaims } from '@app/internal/types';
import { TokenAuth } from '@app/internal/token/auth';
import { connectedArtistAccount } from '@app/http/middlewares/account.middleware';
import { inCategory } from '@app/http/middlewares/user-category.middleware';
import { UserCategory } from '@app/services/user/entities/user.entity';

@controller('/account')
export class AccountController {
  constructor(
    @inject(MODULE_TOKENS.Vibrate) private readonly vibrate: VibrateClient,
    @inject(SERVICE_TOKENS.UserService)
    private readonly user: UserService,
    @inject(MODULE_TOKENS.TokenAuth) private readonly tokenAuth: TokenAuth,
  ) {}

  @httpGet('/channel/list', MIDDLEWARE_TOKENS.AuthMiddleware)
  public async listChannels() {
    const list = await this.vibrate.channels();
    return new SuccessResponseDto({ data: { channels: list.data } });
  }

  @httpPost(
    '/channel/connect',
    autoValidate(ConnectAccountDto.validationSchema),
    MIDDLEWARE_TOKENS.AuthMiddleware,
    MIDDLEWARE_TOKENS.SubscriptionCheckMiddleware,
    inCategory([UserCategory.CREATOR]),
  )
  public async connectAccount(
    @requestBody() payload: ConnectAccountDto,
    @httpReq() req: RequestWithClaims,
  ) {
    const claim = req.claim;
    let user = await this.user.getById(claim.id);

    if (user.vibrate_id) {
      throw new ApplicationError(
        StatusCodes.CONFLICT,
        'Account already connected',
      );
    }

    const provider_id =
      user?.tiktok_id ?? user?.facebook_id ?? user?.instagram_id;

    const data = await this.vibrate.getArtistByChannel({
      channel: payload.channel,
      linkId: provider_id,
    });

    const vibrate_id = data.data.uuid;

    if (!vibrate_id) {
      throw new ApplicationError(
        StatusCodes.NOT_FOUND,
        'Artist data not found',
      );
    }

    const existingAccount = await this.user.getByVibrateId(vibrate_id);

    if (existingAccount) {
      throw new ApplicationError(
        StatusCodes.CONFLICT,
        'Profile has been claimed, contact support for change requests or disputes.',
      );
    }

    const artistAccounts = await this.vibrate.getArtistLinks(vibrate_id);

    const mappedAccounts = artistAccounts?.data?.data?.map((account) => ({
      platform: account?.channel,
      url: account?.link,
      isVisible: true,
    }));

    const updateData: any = { vibrate_id };

    if (!user.accounts) {
      updateData.accounts = JSON.stringify(mappedAccounts);
    }

    user = await this.user.update(claim.id, updateData);
    await this.tokenAuth.reset(req.claimId, {
      ...claim,
      vibrate_id: user.vibrate_id,
    });

    return new SuccessResponseDto();
  }

  @httpGet(
    '/artist-links',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    connectedArtistAccount,
  )
  public async artistLinks(@httpReq() req: RequestWithClaims) {
    const claim = req.claim;

    const links = await this.vibrate.getArtistLinks(claim.vibrate_id);

    return new SuccessResponseDto({ data: { links: links.data.data } });
  }
}
