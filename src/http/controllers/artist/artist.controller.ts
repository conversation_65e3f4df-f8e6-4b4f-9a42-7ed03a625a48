import {
  controller,
  httpGet,
  httpPatch,
  httpPost,
  queryParam,
  request as httpReq,
  requestBody,
  requestParam,
} from 'inversify-express-utils';
import { inCategory } from '@app/http/middlewares/user-category.middleware';
import {
  MIDDLEWARE_TOKENS,
  MODULE_TOKENS,
  SERVICE_TOKENS,
} from '@app/internal/ioc/tokens';
import { UserCategory } from '@app/services/user/entities/user.entity';
import { inject } from 'inversify';
import { RequestWithClaims } from '@app/internal/types';
import { ArtistService } from '@app/services/artist/artist.service';
import { SuccessResponseDto } from '@app/http/dtos/http-response.dto';
import { autoValidate } from '@app/http/middlewares/validation.middleware';
import { CreateArtistDto, EditArtistDto } from './dto/artist.dto';
import { ListArtistsDto } from './dto/list-artists.dto';
import { S3 } from '@app/modules/s3';
import {
  applyFileFilter,
  FileMiddleware,
  fileTypeFilter,
} from '@app/http/middlewares/file.middleware';
import { FileSize } from '@app/internal/enums';
import { ApplicationError } from '@app/internal/errors';
import { StatusCodes } from 'http-status-codes';
import { IdDto } from '@app/http/dtos/id.dto';
import * as path from 'path';
import { DistributorStoreId, Revelator } from '@app/modules/revelator';
import { extractFilename } from '@app/utils/file.utils';
import { omit, pick } from 'lodash';
import { UserService } from '@app/services/user/user.service';
import { Artist } from '@app/services/artist/entity/artist.entity';
import { PermissionResource } from '@app/utils/permissions.utils';
import { hasPermissions } from '@app/http/middlewares/permissions.middleware';
import { PaginatedQueryDto } from '@app/http/dtos/paginated-query.dto';
import { OffsetPaginationResult } from '@app/internal/postgres/pagination';

@controller('/artist')
export class ArtistController {
  constructor(
    @inject(SERVICE_TOKENS.ArtistService)
    private readonly artist: ArtistService,
    @inject(MODULE_TOKENS.S3) private readonly s3: S3,
    @inject(MODULE_TOKENS.Revelator)
    private readonly revelator: Revelator,
    @inject(SERVICE_TOKENS.UserService) private readonly user: UserService,
  ) {}

  @httpPost(
    '/',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    MIDDLEWARE_TOKENS.SubscriptionCheckMiddleware,
    inCategory([UserCategory.CREATOR, UserCategory.TEAM_MEMBER]),
    hasPermissions([
      {
        resource: PermissionResource.Distribution,
        accessLevel: ['viewer'],
      },
      {
        resource: PermissionResource.CatalogManagement,
        accessLevel: ['viewer'],
      },
    ]),
    FileMiddleware(
      'single',
      { field_name: 'profile_picture' },
      {
        limits: { fileSize: 7 * FileSize.MB },
        fileFilter: applyFileFilter([
          fileTypeFilter(['.jpg', '.png', '.jpeg']),
        ]),
      },
    ),
    autoValidate(CreateArtistDto.validationSchema),
  )
  public async createArtist(
    @requestBody()
    payload: CreateArtistDto,
    @httpReq() req: RequestWithClaims,
  ) {
    const { file, claim } = req;

    const user = await this.user.getById(claim?.team_id ?? claim.id);

    const artistLocals = [];

    if (payload.localized_names?.length > 0) {
      for (const nameLocal of payload.localized_names) {
        artistLocals.push({
          name: nameLocal.name,
          languageId: nameLocal.language,
        });
      }
    }

    const newRevelatorArtistPayload = {
      name: payload.name,
      artistLocals,
      artistExternalIds: [
        {
          distributorStoreId: DistributorStoreId.APPLE_MUSIC,
          profileId: String(payload.apple_music_id),
        },
        {
          distributorStoreId: DistributorStoreId.SPOTIFY,
          profileId: payload.spotify_id,
        },
      ],
    };

    const artistData = {
      localized_names: payload.localized_names,
      apple_music_id: String(payload.apple_music_id),
      spotify_id: payload.spotify_id,
      name: payload.name,
      user_id: claim?.team_id ?? claim.id,
    };

    if (file) {
      const fileExtension = path.extname(file.originalname);

      const timestampedFilename = `${extractFilename(
        file.originalname,
      )}_${new Date().toISOString()}${fileExtension}`;

      const url = await this.s3.upload({
        Key: timestampedFilename,
        Body: file.buffer,
        ContentType: file.mimetype,
      });

      const revelatorUploadId = await this.revelator.uploadImage(
        user.revelator_id,
        user.email,
        user.enterprise_name,
        {
          coverImage: false,
          file: {
            name: timestampedFilename,
            buffer: file.buffer,
            contentType: file.mimetype,
          },
        },
      );

      Object.assign(artistData, { image: url });

      Object.assign(newRevelatorArtistPayload, {
        image: {
          fileId: revelatorUploadId,
          filename: `${extractFilename(
            file.originalname,
          )}_${new Date().toISOString()}.jpg`,
        },
      });
    }

    const newRevelatorArtist = await this.revelator.saveArtist(
      user.revelator_id,
      user.email,
      user.enterprise_name,
      newRevelatorArtistPayload,
    );

    Object.assign(artistData, {
      revelator_id: String(newRevelatorArtist.artistId),
    });

    const newArtist = await this.artist.create(artistData);

    return new SuccessResponseDto({ data: omit(newArtist, ['revelator_id']) });
  }

  @httpGet(
    '/',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([
      UserCategory.COMMUNITY_OWNER,
      UserCategory.TEAM_MEMBER,
      UserCategory.CREATOR,
    ]),
    hasPermissions([
      {
        resource: PermissionResource.Distribution,
        accessLevel: ['viewer', 'editor'],
      },
      {
        resource: PermissionResource.CatalogManagement,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
    autoValidate(ListArtistsDto.validationSchema, 'query'),
  )
  public async listArtists(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam()
    { search_term, page_number, result_per_page }: ListArtistsDto,
  ) {
    const roleDesc = await this.user.describe(claim.id);

    let artists: OffsetPaginationResult<Artist>;

    if (roleDesc.community_level_user) {
      artists = await this.artist.listArtists({
        search_term,
        page_number,
        result_per_page,
        community_id: claim.community_id,
      });
    } else {
      artists = await this.artist.listArtists({
        search_term,
        page_number,
        result_per_page,
        user_id: claim?.team_id ?? claim.id,
      });
    }

    return new SuccessResponseDto({ data: artists });
  }

  @httpGet(
    '/community/creator/:id',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([UserCategory.COMMUNITY_OWNER, UserCategory.TEAM_MEMBER]),
    hasPermissions([
      {
        resource: PermissionResource.Distribution,
        accessLevel: ['viewer', 'editor'],
      },
      {
        resource: PermissionResource.CatalogManagement,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
    autoValidate(PaginatedQueryDto.validationSchema, 'query'),
  )
  public async fetchCommunityUsersArtists(
    @requestParam() { id }: IdDto,
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { page_number, result_per_page }: PaginatedQueryDto,
  ) {
    const creator = await this.user.getById(id);

    if (
      !creator ||
      creator?.community_id !== claim.community_id ||
      creator?.category !== UserCategory.CREATOR
    ) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Creator not found');
    }

    const artistList = await this.artist.getUserArtists(id, {
      page_number,
      result_per_page,
    });

    return new SuccessResponseDto({ data: artistList });
  }

  @httpGet(
    '/:id',
    autoValidate(IdDto.validationSchema, 'params'),
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([
      UserCategory.COMMUNITY_OWNER,
      UserCategory.TEAM_MEMBER,
      UserCategory.CREATOR,
    ]),
    hasPermissions([
      {
        resource: PermissionResource.Distribution,
        accessLevel: ['viewer', 'editor'],
      },
      {
        resource: PermissionResource.CatalogManagement,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
  )
  public async getArtistByID(
    @httpReq() { claim }: RequestWithClaims,
    @requestParam() { id }: IdDto,
  ) {
    let artist: Artist;

    const roleDesc = await this.user.describe(claim.id);

    if (roleDesc.community_level_user) {
      artist = await this.artist.get(id);

      if (!artist) {
        throw new ApplicationError(StatusCodes.NOT_FOUND, 'Artist not found');
      }

      const artistCreator = await this.user.getById(artist.user_id);

      if (artistCreator.community_id !== claim.community_id) {
        throw new ApplicationError(
          StatusCodes.FORBIDDEN,
          'You do not have the required permissions to view this artist',
        );
      }
    } else {
      artist = await this.artist.get(id, claim?.team_id ?? claim.id);
    }

    if (!artist) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Artist not found');
    }

    return new SuccessResponseDto({ data: artist });
  }

  @httpPatch(
    '/:id',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    MIDDLEWARE_TOKENS.SubscriptionCheckMiddleware,
    inCategory([UserCategory.CREATOR, UserCategory.TEAM_MEMBER]),
    hasPermissions([
      {
        resource: PermissionResource.Distribution,
        accessLevel: ['editor'],
      },
      {
        resource: PermissionResource.CatalogManagement,
        accessLevel: ['editor'],
      },
    ]),
    FileMiddleware(
      'single',
      { field_name: 'profile_picture' },
      {
        limits: { fileSize: 7 * FileSize.MB },
        fileFilter: applyFileFilter([
          fileTypeFilter(['.jpg', '.png', '.jpeg']),
        ]),
      },
    ),
    autoValidate(IdDto.validationSchema, 'params'),
    autoValidate(EditArtistDto.validationSchema),
  )
  public async editArtist(
    @requestParam() { id }: IdDto,
    @httpReq() { file, claim }: RequestWithClaims,
    @requestBody() payload: EditArtistDto,
  ) {
    const user = await this.user.getById(claim?.team_id ?? claim.id);

    const artist = await this.artist.get(id, user.id);

    if (!artist) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Artist not found');
    }

    const fieldsToUpdate: Partial<Artist> = Object.assign(
      {},
      pick(
        artist,
        'name',
        'spotify_id',
        'apple_music_id',
        'localized_names',
        'bio',
      ),
    );

    for (const field in payload) {
      if (payload[field]) {
        fieldsToUpdate[field] = payload[field];
      }
    }

    const artistLocals = [];

    if (fieldsToUpdate.localized_names?.length > 0) {
      for (const nameLocal of fieldsToUpdate.localized_names) {
        artistLocals.push({
          name: nameLocal.name,
          languageId: nameLocal.language,
        });
      }
    }

    const editRevelatorArtistPayload: Partial<
      Record<
        | 'artistId'
        | 'name'
        | 'artistLocals'
        | 'artistExternalIds'
        | 'biography'
        | 'image',
        any
      >
    > = {
      name: fieldsToUpdate.name,
      artistLocals,
      artistExternalIds: [
        {
          distributorStoreId: DistributorStoreId.APPLE_MUSIC,
          profileId: String(fieldsToUpdate.apple_music_id),
        },
        {
          distributorStoreId: DistributorStoreId.SPOTIFY,
          profileId: fieldsToUpdate.spotify_id,
        },
      ],
    };

    if (file) {
      if (file) {
        const fileExtension = path.extname(file.originalname);

        const timestampedFilename = `${extractFilename(
          file.originalname,
        )}_${new Date().toISOString()}${fileExtension}`;

        const [url, revelatorUploadId] = await Promise.all([
          this.s3.upload({
            Key: timestampedFilename,
            Body: file.buffer,
            ContentType: file.mimetype,
          }),

          this.revelator.uploadImage(
            user.revelator_id,
            user.email,
            user.enterprise_name,
            {
              coverImage: true,
              file: {
                name: timestampedFilename,
                buffer: file.buffer,
                contentType: file.mimetype,
              },
            },
          ),
        ]);

        fieldsToUpdate.image = url;

        editRevelatorArtistPayload.image = {
          fileId: revelatorUploadId,
          filename: `${extractFilename(
            file.originalname,
          )}_${new Date().toISOString()}.jpg`,
        };
      }
    }

    await this.revelator.saveArtist(
      user.revelator_id,
      user.email,
      user.enterprise_name,
      {
        ...editRevelatorArtistPayload,
        artistId: Number(artist.revelator_id),
      },
    );

    await this.artist.update(artist.id, fieldsToUpdate);

    return new SuccessResponseDto();
  }
}
