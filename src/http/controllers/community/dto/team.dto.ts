import { PaginatedQueryDto } from '@app/http/dtos/paginated-query.dto';
import { InviteStatus } from '@app/services/invite/entities/invite.entity';
import Jo<PERSON> from 'joi';

export class TeamQueryDto extends PaginatedQueryDto {
  search_term?: string;
  status?: InviteStatus;

  static validationSchema = PaginatedQueryDto.validationSchema.concat(
    Joi.object({
      search_term: Joi.string().lowercase().optional(),
      status: Joi.string()
        .valid(...Object.values(InviteStatus))
        .optional(),
    }),
  );
}
