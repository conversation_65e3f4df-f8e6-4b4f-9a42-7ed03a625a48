import {
  controller,
  httpGet,
  httpPatch,
  httpPost,
  queryParam,
  request as httpReq,
  requestBody,
  requestParam,
  response as httpRes,
} from 'inversify-express-utils';
import { isRootDomain, resolveCname } from '@app/utils/dns-utils';
import { ApplicationError } from '@app/internal/errors';
import { StatusCodes } from 'http-status-codes';
import {
  HttpResponseDto,
  SuccessResponseDto,
} from '@app/http/dtos/http-response.dto';
import { autoValidate } from '@app/http/middlewares/validation.middleware';
import { DomainDto } from '@app/http/controllers/community/dto/domain.dto';
import { inject } from 'inversify';
import {
  MIDDLEWARE_TOKENS,
  MODULE_TOKENS,
  SERVICE_TOKENS,
} from '@app/internal/ioc/tokens';
import { ACM } from '@app/modules/acm';
import { RedisStore } from '@app/internal/redis/store';
import { SignupDto } from '@app/http/controllers/community/dto/signup.dto';
import { UserService } from '@app/services/user/user.service';
import {
  Role,
  User,
  UserCategory,
  UserStatus,
} from '@app/services/user/entities/user.entity';
import { prefixKey } from '@app/utils/prefix-key';
import { ulid } from 'ulid';
import { OnboardingUser } from '@app/http/controllers';
import { DURATION, FileSize } from '@app/internal/enums';
import { hashPassword, verifyPassword } from '@app/utils/bcrypt-utils';
import { generateOtp, generateOtpToken } from '@app/utils/otp-utils';
import { JobQueueManager } from '@app/internal/bull';
import {
  emailQueue,
  SendEmailJob,
  sendNotificationJob,
  SendNotificationJob,
  VerifyCustomDomainConfigJob,
  verifyCustomDomainConfigQueue,
} from '@app/mq/jobs';
import { Claim, RequestWithClaims } from '@app/internal/types';
import { TokenAuth } from '@app/internal/token/auth';
import Deasyncify from 'deasyncify';
import {
  applyFileFilter,
  FileMiddleware,
  fileTypeFilter,
} from '@app/http/middlewares/file.middleware';
import { S3 } from '@app/modules/s3';
import { Request, Response } from 'express';
import * as path from 'path';
import { BrandSetupDto } from '@app/http/controllers/community/dto/brand-setup.dto';
import {
  adminOnlyAuth,
  passTempToken,
} from '@app/http/middlewares/auth.middleware';
import { CommunityService } from '@app/services/community/community.service';
import { UpdateBrandIdentityDto } from '@app/http/controllers/community/dto/update-brand-identity.dto';
import { SetupSettingsDto } from '@app/http/controllers/community/dto/setup-settings.dto';
import { SignInDto } from '@app/http/controllers/community/dto/signin.dto';
import { omit, pick } from 'lodash';
import { inCategory } from '@app/http/middlewares/user-category.middleware';
import { ResourceRecord } from '@aws-sdk/client-acm';
import { env } from '@app/config/env';
import { syncCatch } from '@app/utils/sync-catch';
import { loadTemplate, render } from '@app/utils/template.utils';
import { CloudFront } from '@app/modules/cloudfront';
import {
  ActivateCustomDomainJob,
  activateCustomDomainQueue,
} from '@app/mq/jobs/activate-custom-domain';
import { sleep } from '@app/utils/sleep';
import { CommunityUsersQueryDto } from './dto/community-users.dto';
import { IdDto, OptionalIdDto } from '@app/http/dtos/id.dto';
import { nanoid } from 'nanoid';
import { InviteDto } from './dto/invite.dto';
import { InviteService } from '@app/services/invite/invite.service';
import moment from 'moment';
import { AcceptInviteDto } from './dto/accept-invite.dto';
import {
  Invitation,
  InviteStatus,
} from '@app/services/invite/entities/invite.entity';
import {
  assignPermissions,
  PermissionResource,
} from '@app/utils/permissions.utils';
import { TeamQueryDto } from './dto/team.dto';
import { GoogleOauthClient } from '@app/modules/google-oauth';
import { ForgotPasswordDto } from '../auth/dto/forgot-password.dto';
import { ResetPasswordDto } from '../auth/dto/reset-password.dto';
import { hasPermissions } from '@app/http/middlewares/permissions.middleware';
import { PaginatedQueryDto } from '@app/http/dtos/paginated-query.dto';
import { PostService } from '@app/services/post/post.service';
import { SsoDto } from './dto/sso.dto';
import { UpdateCommunityDto } from '@app/http/controllers/community/dto/update-community.dto';
import { Community } from '@app/services/community/entities/community.entity';
import { WalletService } from '@app/services/wallet/wallet.service';
import { WalletType } from '@app/services/wallet/entities/wallet.entity';
import { TransactionService } from '@app/services/transaction/transaction.service';
import { SubscriptionService } from '@app/services/subscription/subscription.service';
import { CommunityTransactionsQueryDto } from './dto/get-transactions.dto';
import { MonetizationService } from '@app/services/monetization/monetization.service';
import { ProductObject, Stripe } from '@app/modules/stripe';
import { safeJsonParse } from '@app/utils/object';
import { ListCommunitiesDto } from './dto/list-communities.dto';
import { Currency } from '@app/services/transaction/entities/transaction.entity';
import { ReportsService } from '@app/services/reports/reports.service';
import { CommunityReportDto } from './dto/community-reports.dto';
import { SuspendReviewCommunityDto } from './dto/suspend-review-community.dto';
import { Revelator } from '@app/modules/revelator';
import AdmZip from 'adm-zip';

@controller('/community')
export class CommunityController {
  private readonly emailQueue =
    this.jobQueueManager.getQueue<SendEmailJob>(emailQueue);

  private readonly verifyCustomDomainConfigJob =
    this.jobQueueManager.getQueue<VerifyCustomDomainConfigJob>(
      verifyCustomDomainConfigQueue,
    );

  private readonly activeCustomDomainJob =
    this.jobQueueManager.getQueue<ActivateCustomDomainJob>(
      activateCustomDomainQueue,
    );

  private NotificationQueue =
    this.jobQueueManager.getQueue<SendNotificationJob>(sendNotificationJob);

  constructor(
    @inject(MODULE_TOKENS.ACM) private readonly acm: ACM,
    @inject(MODULE_TOKENS.CloudFront) private readonly cloudFront: CloudFront,
    @inject(SERVICE_TOKENS.CommunityService)
    private readonly community: CommunityService,
    @inject(MODULE_TOKENS.JobQueueManager)
    private readonly jobQueueManager: JobQueueManager,
    @inject(MODULE_TOKENS.RedisStore) private readonly redisStore: RedisStore,
    @inject(MODULE_TOKENS.S3) private readonly s3: S3,
    @inject(MODULE_TOKENS.Stripe) private readonly stripe: Stripe,
    @inject(MODULE_TOKENS.TokenAuth) private readonly tokenAuth: TokenAuth,
    @inject(SERVICE_TOKENS.UserService)
    private readonly user: UserService,
    @inject(SERVICE_TOKENS.inviteService)
    private readonly invite: InviteService,
    @inject(MODULE_TOKENS.GoogleOauthClient)
    private readonly google: GoogleOauthClient,
    @inject(SERVICE_TOKENS.PostService)
    private readonly post: PostService,
    @inject(SERVICE_TOKENS.WalletService)
    private readonly wallet: WalletService,
    @inject(SERVICE_TOKENS.TransactionService)
    private readonly transaction: TransactionService,
    @inject(SERVICE_TOKENS.SubscriptionService)
    private readonly subscription: SubscriptionService,
    @inject(SERVICE_TOKENS.MonetizationService)
    private readonly monetization: MonetizationService,
    @inject(SERVICE_TOKENS.ReportsService)
    private readonly reports: ReportsService,
    @inject(MODULE_TOKENS.Revelator) private readonly revelator: Revelator,
  ) {}

  @httpPatch(
    '/',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    MIDDLEWARE_TOKENS.SubscriptionCheckMiddleware,
    autoValidate(UpdateCommunityDto.validationSchema),
  )
  public async updateCommunity(
    @requestBody() payload: UpdateCommunityDto,
    @httpReq() { claim }: RequestWithClaims,
  ) {
    const fieldsToUpdate = {};

    if (payload.name != null) {
      const communityUsingName = await this.community.getWithCustomDomain(
        payload.name,
      );

      if (communityUsingName) {
        throw new ApplicationError(
          StatusCodes.CONFLICT,
          `You cannot use the name "${payload.name}" for your community because it is already taken`,
        );
      }

      Object.assign(fieldsToUpdate, { name: payload.name });
    }

    await this.community.update(claim.community_id, fieldsToUpdate);

    return new SuccessResponseDto();
  }

  @httpGet(
    '/list',
    adminOnlyAuth,
    MIDDLEWARE_TOKENS.AuthMiddleware,
    autoValidate(ListCommunitiesDto.validationSchema, 'query'),
  )
  public async listCommunities(
    @queryParam()
    { search_term, result_per_page, page_number, status }: ListCommunitiesDto,
  ) {
    const communities = await this.community.fetch(
      page_number,
      result_per_page,
      status,
      search_term,
    );

    return new SuccessResponseDto({ data: communities });
  }

  @httpGet(
    '/overview',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([UserCategory.COMMUNITY_OWNER, UserCategory.TEAM_MEMBER]),
  )
  public async communitiesOverview(@httpReq() { claim }: RequestWithClaims) {
    const overview = await this.community.overviewByCommunity(
      claim.community_id,
    );

    return new SuccessResponseDto({ data: overview });
  }

  @httpGet('/metadata')
  public async queryCommunityFromDomain(
    @httpReq() req: Request,
    @queryParam() { id }: OptionalIdDto,
  ) {
    const origin = req.headers['origin'];

    const [clientUrl, urlParseErr] = syncCatch(() => new URL(origin));

    if (urlParseErr != null) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'Invalid Request Header',
      );
    }

    let community: Community;

    if (id) {
      community = await this.community.get(id);
    } else {
      community = await this.community
        .find({
          custom_domain: clientUrl.hostname,
          custom_domain_active: true,
        })
        .then((d) => d[0]);
    }

    if (!community) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Community not found');
    }

    return new SuccessResponseDto({ data: community });
  }

  @httpGet('/discover')
  public async discoverPublicCommunities() {
    const communities = await this.community.find({ is_private: false });
    return new SuccessResponseDto({ data: communities });
  }

  @httpPost('/signin', autoValidate(SignInDto.validationSchema))
  public async signIn(@requestBody() payload: SignInDto) {
    const user = await this.user.find({
      email: payload.email,
      category: [UserCategory.COMMUNITY_OWNER, UserCategory.TEAM_MEMBER],
    });

    if (!user) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'Invalid email/password',
      );
    }

    const isPasswordCorrect = await verifyPassword(
      payload.password,
      user.password,
    );

    if (!isPasswordCorrect) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'Incorrect email/password',
      );
    }

    await this.user.update(user.id, { last_login: new Date() });

    if (user.category === UserCategory.TEAM_MEMBER) {
      const teamOwner = await this.user.getById(user.team_id);

      if (teamOwner.category !== UserCategory.COMMUNITY_OWNER) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          'Invalid email/password',
        );
      }
    }

    const token = await this.tokenAuth.generate(
      {
        id: user.id,
        email: user.email,
        category: user.category,
        team_role: user.role,
        vibrate_id: user.vibrate_id,
        community_id: user.community_id,
        permissions: user.permissions,
        team_id: user?.team_id,
      },
      DURATION.DAYS,
    );

    return new SuccessResponseDto({
      data: { token, ...omit(user, ['password']) },
    });
  }

  @httpPost('/signup/init', autoValidate(SignupDto.validationSchema))
  public async initCommunityOwnerSignup(@requestBody() payload: SignupDto) {
    const communityOwner = await this.user.find({
      email: payload.email,
      category: [UserCategory.COMMUNITY_OWNER],
    });

    if (communityOwner) {
      throw new ApplicationError(
        StatusCodes.CONFLICT,
        'This email is already used by a community owner',
      );
    }

    const key = prefixKey('new_community_owner', payload.email);

    const newCommunityOwner = (await this.redisStore.get<OnboardingUser>(
      key,
    )) ?? {
      id: ulid(),
    };

    const { email, password, ...rest } = payload;

    Object.assign(newCommunityOwner, {
      email: email,
      password: await hashPassword(password),
      ...rest,
    });

    await this.redisStore.set(key, newCommunityOwner, DURATION.DAYS);

    const otp = generateOtp(6);

    // create public token to search for otp in cache
    const otpToken = generateOtpToken(payload.email, otp);

    const template = loadTemplate('makerverse/otp');
    const htmlData = {
      fname: payload.first_name,
      otp,
      communityName: 'Makerverse',
    };
    const html = render(template, htmlData);

    await this.emailQueue.add('email', {
      to: payload.email,
      subject: 'Verify your email 🎶',
      body: html,
    });

    // create verification key making otp itself act as a private key
    const otpVerificationKey = prefixKey(otpToken, otp);

    await this.redisStore.set(
      otpVerificationKey,
      {
        first_name: payload.first_name,
        email: payload.email,
        community_name: 'Makerverse',
      },
      5 * DURATION.MINUTES,
    );

    // save email to enable resend
    await this.redisStore.set(
      otpToken,
      {
        first_name: payload.first_name,
        email: payload.email,
        community_name: 'Makerverse',
      },
      15 * DURATION.MINUTES,
    );

    return new SuccessResponseDto({ data: { otp_token: otpToken } });
  }

  @httpPost('/signup/email/verify')
  public async verifyEmail(@requestBody() payload: any) {
    const otpVerificationKey = prefixKey(payload.otp_token, payload.otp);

    const metadata = await this.redisStore.get<{ email: string }>(
      otpVerificationKey,
    );

    if (!metadata) {
      throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Invalid Otp');
    }

    // revoke otp by deleting its verification key after it has been used
    await this.redisStore.delete(otpVerificationKey);
    await this.redisStore.delete(payload.otp_token);

    const key = prefixKey('new_community_owner', metadata.email);

    const newCommunityOwner = await this.redisStore.get<
      Pick<User, 'id' | 'email'>
    >(key);

    // mark email as verified
    Object.assign(newCommunityOwner, { email_verified: true });

    await this.redisStore.set(key, newCommunityOwner);

    // mark token as token as temporary
    const tokenPayload: Claim = {
      id: newCommunityOwner.id,
      email: newCommunityOwner.email,
      temp: true,
    };

    const token = await this.tokenAuth.generate(tokenPayload, DURATION.DAYS);

    return new SuccessResponseDto({ data: { token } });
  }

  @httpPost(
    '/signup/brand/setup',
    passTempToken,
    MIDDLEWARE_TOKENS.AuthMiddleware,
    FileMiddleware(
      'single',
      { field_name: 'account_icon' },
      {
        limits: { fileSize: 10 * FileSize.MB },
        fileFilter: applyFileFilter([
          fileTypeFilter(['.jpg', '.jfif', '.png', '.jpeg']),
        ]),
      },
    ),
    autoValidate(BrandSetupDto.validationSchema),
  )
  public async brandSetup(
    @httpReq() { file, claim, claimId }: RequestWithClaims,
    @requestBody() payload: BrandSetupDto,
  ) {
    const communityOwner = await this.user.getById(claim.id);

    if (communityOwner) {
      throw new ApplicationError(
        StatusCodes.CONFLICT,
        'Community owner has already setup community',
      );
    }

    if (!file) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        "File 'account_icon' is required",
      );
    }

    const key = prefixKey('new_community_owner', claim.email);

    const newCommunityOwner = await this.redisStore.get<Partial<User>>(key);

    Object.assign(newCommunityOwner, {
      category: UserCategory.COMMUNITY_OWNER,
      role: Role.OWNER,
      username: 'admin',
    });

    const fileExtension = path.extname(file.originalname);

    const timestampedFilename = `account_icon_${new Date().toISOString()}${fileExtension}`;

    const url = await this.s3.upload({
      Key: `communities/${newCommunityOwner.id}/${timestampedFilename}`,
      Body: file.buffer,
      ContentType: file.mimetype,
    });

    const communityUsingName = await this.community.queryByName(payload.name);

    if (communityUsingName) {
      throw new ApplicationError(
        StatusCodes.CONFLICT,
        `You cannot use the name "${payload.name}" for your community because it is already taken`,
      );
    }

    const community = await this.community.create({
      name: payload.name,
      account_icon: url,
    });

    const user = await this.user.create({
      ...newCommunityOwner,
      community_id: community.id,
    });

    await this.wallet.getOrCreate(
      user?.team_id ?? user.id,
      Currency.USD,
      WalletType.FIAT,
    );

    await this.NotificationQueue.add('send_notification_job', {
      user_id: user.id,
      message: 'Welcome to Makerverse - Complete your setup',
    });

    await this.redisStore.delete(key);

    await this.tokenAuth.reset(claimId, {
      id: user.id,
      email: user.email,
      category: user.category,
      team_role: user.role,
      community_id: user.community_id,
    });

    return new SuccessResponseDto({
      data: pick(user, [
        'id',
        'first_name',
        'last_name',
        'email',
        'category',
        'community_id',
        'profile_picture',
        'country',
      ]),
    });
  }

  @httpPatch(
    '/brand/identity',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([UserCategory.COMMUNITY_OWNER, UserCategory.TEAM_MEMBER]),
    hasPermissions([
      {
        resource: PermissionResource.AccountSettings,
        accessLevel: ['editor'],
      },
    ]),
    FileMiddleware(
      'fields',
      {
        fields: [
          { name: 'favicon', maxCount: 1 },
          { name: 'brand_themed_logo', maxCount: 1 },
          { name: 'account_icon', maxCount: 1 },
          { name: 'statement_logo', maxCount: 1 },
        ],
      },
      {
        limits: { fileSize: 5 * FileSize.MB },
        fileFilter: applyFileFilter([
          fileTypeFilter(['.jpg', '.jfif', '.png', '.jpeg']),
        ]),
      },
    ),
    autoValidate(UpdateBrandIdentityDto.validationSchema),
  )
  public async brandIdentifySetup(
    @requestBody() payload: UpdateBrandIdentityDto,
    @httpReq() { claim, files }: RequestWithClaims,
  ) {
    const fieldUpdate: Record<string, any> = { ...payload };

    if (Object.keys(files).length > 0) {
      const asyncOps = [];

      for (const key in files) {
        const file = files[key]?.[0];

        const fileExtension = path.extname(file.originalname);

        const timestampedFilename = `${key}_${new Date().toISOString()}${fileExtension}`;

        const asyncOp = this.s3
          .upload({
            Key: `communities/${claim.community_id}/${timestampedFilename}`,
            Body: file.buffer,
            ContentType: file.mimetype,
          })
          .then((val) => {
            fieldUpdate[key] = val;
          });

        asyncOps.push(asyncOp);
      }

      await Promise.all(asyncOps);
    }

    await this.community.update(claim.community_id, fieldUpdate);

    return new SuccessResponseDto();
  }

  @httpPost(
    '/signup/settings/setup',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    autoValidate(SetupSettingsDto.validationSchema),
    inCategory([UserCategory.COMMUNITY_OWNER]),
  )
  public async configureCommunitySettings(
    @httpReq() { claim }: RequestWithClaims,
    @requestBody() payload: SetupSettingsDto,
  ) {
    if (claim.team_role !== 'owner') {
      throw new ApplicationError(
        StatusCodes.FORBIDDEN,
        'You do not have the permissions to carry out this action',
      );
    }

    await this.community.update(claim.community_id, {
      is_private: payload.type === 'private',
      enabled_members: payload.enabled_members,
      enabled_sso: payload.enabled_sso,
    });

    return new SuccessResponseDto();
  }

  @httpPost('/schedule/call', MIDDLEWARE_TOKENS.AuthMiddleware)
  public async bookCall(@httpReq() { claim }: RequestWithClaims) {
    if (claim.team_role !== 'owner') {
      throw new ApplicationError(
        StatusCodes.FORBIDDEN,
        'You do not have the permissions to carry out this action',
      );
    }

    await this.community.update(claim.community_id, {
      has_booked_call: true,
    });

    return new SuccessResponseDto();
  }

  @httpPost(
    '/domain/verify',
    autoValidate(DomainDto.validationSchema),
    MIDDLEWARE_TOKENS.AuthMiddleware,
    MIDDLEWARE_TOKENS.SubscriptionCheckMiddleware,
    inCategory([UserCategory.COMMUNITY_OWNER, UserCategory.TEAM_MEMBER]),
    hasPermissions([
      {
        resource: PermissionResource.AccountSettings,
        accessLevel: ['editor'],
      },
    ]),
  )
  public async verifyDomain(
    @requestBody() payload: DomainDto,
    @httpReq() { claim }: RequestWithClaims,
    @httpRes() res: Response,
  ) {
    const communityUsingDomain = await this.community.getWithCustomDomain(
      payload.domain,
    );

    if (
      communityUsingDomain &&
      communityUsingDomain.id !== claim.community_id
    ) {
      throw new ApplicationError(
        StatusCodes.CONFLICT,
        'You cannot use this domain because it is taken',
      );
    } else if (
      communityUsingDomain &&
      communityUsingDomain.id === claim.community_id
    ) {
      res.status(204);

      return new SuccessResponseDto();
    }

    if (payload.domain.startsWith('www.')) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        "The 'www' subdomain is not allowed.",
      );
    }

    if (isRootDomain(payload.domain)) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'You can only use subdomains',
      );
    }

    const [, err] = await Deasyncify.watch(
      this.validateCnameConfiguration(
        payload.domain,
        env.web_client_host_name.split(',').map((hostname) => hostname.trim()),
      ),
    );

    if (err != null) {
      await this.verifyCustomDomainConfigJob.add(
        'verify_custom_domain_config',
        {
          community_id: claim.community_id,
          domain: payload.domain,
        },
        {
          jobId: nanoid(22),
          repeat: {
            every: DURATION.HOURS,
            limit: 48,
            immediately: true,
            key: nanoid(22),
          },
        },
      );

      return new HttpResponseDto({
        message: 'Your custom domain is being verified',
        data: {
          resolved: false,
        },
      });
    }

    await this.community.update(claim.community_id, {
      custom_domain: payload.domain,
      custom_domain_active: false,
    });

    return new HttpResponseDto({
      message: 'Your custom domain has been Verified',
      data: {
        resolved: true,
      },
    });
  }

  @httpPost(
    '/domain/activate',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    MIDDLEWARE_TOKENS.SubscriptionCheckMiddleware,
    inCategory([UserCategory.COMMUNITY_OWNER, UserCategory.TEAM_MEMBER]),
    hasPermissions([
      {
        resource: PermissionResource.AccountSettings,
        accessLevel: ['editor', 'viewer'],
      },
    ]),
  )
  public async activateDomain(@httpReq() { claim }: RequestWithClaims) {
    const community = await this.community.get(claim.community_id);

    if (!community.custom_domain) {
      throw new ApplicationError(
        StatusCodes.CONFLICT,
        'Please enter a valid domain for verification',
      );
    }

    await this.validateCnameConfiguration(
      community.custom_domain,
      env.web_client_host_name.split(',').map((hostname) => hostname.trim()),
    );

    // fetch cloud front distribution settings
    const distributionSettings = await this.cloudFront.getDistribution(
      env.cloudfront_distribution_id,
    );

    const currentCertArn =
      distributionSettings.Distribution.DistributionConfig.ViewerCertificate
        .ACMCertificateArn;

    const currentCert = await this.acm.describeCertificate({
      CertificateArn: currentCertArn,
    });

    const domains = currentCert.Certificate.SubjectAlternativeNames;

    if (domains.findIndex((v) => v === community.custom_domain) < 0) {
      // add communities custom domain to the SANs list
      domains.push(community.custom_domain);
    }

    const newCert = await this.acm.requestCertificate({
      DomainName: domains[0],
      SubjectAlternativeNames: domains,
      ValidationMethod: 'DNS',
    });

    const newCertArn = newCert.CertificateArn;

    let dnsRecord: ResourceRecord;

    let errFetchingDnsRecord: Error;

    for (let i = 0; i < 5; i++) {
      const certDescription = await this.acm.describeCertificate({
        CertificateArn: newCertArn,
      });

      const validationOptions =
        certDescription.Certificate?.DomainValidationOptions?.find(
          (v) => v.DomainName === community.custom_domain,
        );

      if (
        validationOptions?.ValidationMethod === 'DNS' &&
        validationOptions.ResourceRecord
      ) {
        dnsRecord = validationOptions.ResourceRecord;

        errFetchingDnsRecord = null;

        break;
      } else {
        errFetchingDnsRecord = new ApplicationError(
          StatusCodes.INTERNAL_SERVER_ERROR,
          'Could not activate custom domain, Please try again',
        );
      }

      await sleep(DURATION.SECONDS);
    }

    if (errFetchingDnsRecord) {
      throw errFetchingDnsRecord;
    }

    await this.activeCustomDomainJob.add(
      'activate_custom_domain',
      {
        community_id: community.id,
        certificateArn: newCertArn,
      },
      {
        jobId: nanoid(22),
        repeat: {
          every: DURATION.HOURS,
          limit: 48,
          immediately: true,
          key: nanoid(22),
        },
      },
    );

    const dnsConf = {
      record_name: dnsRecord.Name,
      record_type: dnsRecord.Type,
      record_value: dnsRecord.Value,
    };

    const template = loadTemplate('makerverse/activate-domain');

    const html = render(template, { ...dnsConf });

    await this.emailQueue.add('email', {
      to: claim.email,
      subject: 'Activate your community',
      body: html,
    });

    return new SuccessResponseDto({ data: dnsConf });
  }

  @httpGet(
    '/users',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    autoValidate(CommunityUsersQueryDto.validationSchema, 'query'),
    inCategory([UserCategory.COMMUNITY_OWNER, UserCategory.TEAM_MEMBER]),
    hasPermissions([
      {
        resource: PermissionResource.ClientManagement,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
  )
  public async communityUsers(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam()
    {
      category,
      page_number,
      result_per_page,
      search_term,
    }: CommunityUsersQueryDto,
  ) {
    const users = await this.user.all(claim.community_id, {
      selectFields: [
        'email',
        'email_verified',
        'phone',
        'phone_verified',
        'community_id',
      ],
      search_term,
      category,
      page_number,
      result_per_page,
    });

    return new SuccessResponseDto({ data: users });
  }

  @httpGet(
    '/users/:id/details',
    autoValidate(IdDto.validationSchema, 'params'),
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([UserCategory.COMMUNITY_OWNER, UserCategory.TEAM_MEMBER]),
    hasPermissions([
      {
        resource: PermissionResource.ClientManagement,
        accessLevel: ['editor', 'viewer'],
      },
    ]),
  )
  public async userDetails(
    @httpReq() { claim }: RequestWithClaims,
    @requestParam() { id }: IdDto,
  ) {
    const user = await this.user.getById(id);

    if (!user) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'User not found');
    }

    if (claim.community_id !== user.community_id) {
      throw new ApplicationError(
        StatusCodes.FORBIDDEN,
        'User not found in current community',
      );
    }

    const details = await this.user.userDetails(id);
    return new SuccessResponseDto({ data: details });
  }

  @httpPost(
    '/invites',
    autoValidate(InviteDto.validationSchema),
    MIDDLEWARE_TOKENS.AuthMiddleware,
    MIDDLEWARE_TOKENS.SubscriptionCheckMiddleware,
    inCategory([UserCategory.COMMUNITY_OWNER]),
  )
  public async inviteTeamMember(
    @httpReq() { claim }: RequestWithClaims,
    @requestBody() payload: InviteDto,
  ) {
    if (claim.team_role !== 'owner') {
      throw new ApplicationError(
        StatusCodes.UNAUTHORIZED,
        'You do not have the permissions to perform this action',
      );
    }

    if (claim.email === payload.email) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'You cannot invite yourself',
      );
    }

    const sub = await this.subscription.getCurrentSubscription(claim.id);

    const product = await this.stripe.retrieveProduct(sub.metadata.product_id);

    const maxAdmins = product.metadata?.usage_limits?.max_community_admins ?? 0;

    const adminCount = await this.user.teamMemberCount(claim.community_id);

    if (adminCount + 1 > maxAdmins) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        `You have reached the admin limit (${maxAdmins}) for your current plan.`,
      );
    }

    const existingUser = await this.user.find({
      email: payload.email,
      category: [UserCategory.COMMUNITY_OWNER, UserCategory.TEAM_MEMBER],
    });

    if (existingUser) {
      throw new ApplicationError(
        StatusCodes.CONFLICT,
        'User already belongs to a community',
      );
    }

    const community = await this.community.get(claim.community_id);

    if (!community) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Community not found');
    }

    const permissions = assignPermissions(payload.role);

    const existingInvite = await this.invite.find({
      community_id: community.id,
      email: payload.email,
      status: InviteStatus.pending,
    });

    const newExpiry = moment().add(5, 'days').toDate();

    let invite: Invitation;

    if (existingInvite) {
      if (moment(existingInvite.expiry).isAfter(moment())) {
        throw new ApplicationError(
          StatusCodes.CONFLICT,
          'An active invitation already exists for this email',
        );
      }
      invite = await this.invite.update(existingInvite.id, {
        expiry: newExpiry,
        permissions,
      });
    } else {
      invite = await this.invite.create({
        community_id: claim.community_id,
        email: payload.email,
        expiry: newExpiry,
        team_id: claim.id,
        role: payload.role,
        permissions,
        status: InviteStatus.pending,
      });
    }

    const url = `${env.web_client_base_url}/invite/${invite.id}`;
    const template = loadTemplate('makerverse/account-invite');
    const html = render(template, {
      communityName: community.name,
      role: payload.role,
      url,
    });

    await this.emailQueue.add('email', {
      to: payload.email,
      subject: 'Community Invite 🎶',
      body: html,
    });

    return new SuccessResponseDto();
  }

  @httpPost(
    '/invites/:id/resend',
    autoValidate(IdDto.validationSchema, 'params'),
    MIDDLEWARE_TOKENS.AuthMiddleware,
    MIDDLEWARE_TOKENS.SubscriptionCheckMiddleware,
    inCategory([UserCategory.COMMUNITY_OWNER]),
  )
  public async resendInvite(
    @httpReq() { claim }: RequestWithClaims,
    @requestParam() { id }: IdDto,
  ) {
    const invite = await this.invite.get(id);
    if (!invite || invite.team_id !== (claim.team_id ?? claim.id)) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Invitation not found');
    }

    if (invite.status === 'accepted') {
      throw new ApplicationError(
        StatusCodes.CONFLICT,
        'This invitation has already been accepted',
      );
    }

    if (moment(invite.expiry).isAfter(moment())) {
      throw new ApplicationError(
        StatusCodes.CONFLICT,
        'An active invitation already exists for this email. You can resend only after it expires.',
      );
    }

    invite.expiry = moment().add(5, 'days').toDate();
    await this.invite.update(invite.id, { expiry: invite.expiry });

    const community = await this.community.get(invite.community_id);

    const user = await this.user.getById(invite.team_id);

    const url = `https://${community.custom_domain}/invite/${invite.id}`;
    const template = loadTemplate('makerverse/account-invite');
    const html = render(template, {
      accountName: user.first_name,
      communityName: community.name,
      role: invite.role,
      url,
      logoUrl: community.brand_themed_logo,
      color: community.brand_color,
    });

    await this.emailQueue.add('email', {
      to: invite.email,
      subject: 'Team Invite 🎶 (Resent)',
      body: html,
    });

    return new SuccessResponseDto();
  }

  @httpPost(
    '/invites/:id/accept',
    autoValidate(AcceptInviteDto.validationSchema),
    autoValidate(IdDto.validationSchema, 'params'),
  )
  public async acceptInvite(
    @requestParam() { id }: IdDto,
    @requestBody() payload: AcceptInviteDto,
  ) {
    const validInvite = await this.invite.get(id);
    if (
      moment().isAfter(validInvite.expiry) ||
      validInvite.status === 'accepted'
    ) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'This invite has expired/accepted',
      );
    }

    const password = await hashPassword(payload.password);

    const community = await this.community.get(validInvite.community_id);

    const user = await this.user.create({
      first_name: payload.first_name,
      last_name: payload.last_name,
      email: validInvite.email,
      role: validInvite.role,
      category: UserCategory.TEAM_MEMBER,
      permissions: validInvite.permissions,
      password,
      community_id: validInvite.community_id,
      team_id: validInvite.team_id,
      email_verified: true,
    });

    await this.invite.update(validInvite.id, {
      status: InviteStatus.accepted,
    });

    const template = loadTemplate('makerverse/welcome-invitee');
    const htmlData = {
      fname: user.first_name,
      communityName: community.name,
      url:
        env.node_env === 'development'
          ? 'https://beta.maker-verse.xyz/login'
          : 'https://maker-verse.xyz/login',
    };
    const html = render(template, htmlData);

    await this.emailQueue.add('email', {
      to: user.email,
      subject: 'Welcome Onboard 🎶🚀',
      body: html,
    });

    const tokenPayload: Claim = {
      id: user.id,
      email: user.email,
      category: user.category,
      team_role: user.role,
      community_id: user.community_id,
      permissions: user.permissions,
      team_id: user?.team_id,
    };

    const token = await this.tokenAuth.generate(tokenPayload, DURATION.DAYS);

    return new SuccessResponseDto({
      data: { token, ...omit(user, ['password']) },
    });
  }

  @httpPatch(
    '/invites/:id/toggle-suspend',
    autoValidate(IdDto.validationSchema, 'params'),
    MIDDLEWARE_TOKENS.AuthMiddleware,
    MIDDLEWARE_TOKENS.SubscriptionCheckMiddleware,
    inCategory([UserCategory.COMMUNITY_OWNER]),
  )
  public async toggleSuspendUser(
    @httpReq() { claim }: RequestWithClaims,
    @requestParam() { id }: IdDto,
  ) {
    if (claim.id === id) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'You cannot change your own suspension state',
      );
    }

    const user = await this.user.getById(id);
    if (!user) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'User not found');
    }

    const teamMember = await this.user.getById(user.team_id);
    if (
      (user.category !== UserCategory.TEAM_MEMBER && !teamMember) ||
      teamMember.id !== claim.id
    ) {
      throw new ApplicationError(
        StatusCodes.FORBIDDEN,
        'You can only suspend your team members',
      );
    }

    const nextStatus =
      user.status === UserStatus.suspended
        ? UserStatus.active
        : UserStatus.suspended;

    await this.user.update(id, { status: nextStatus });

    const template = loadTemplate(
      nextStatus === UserStatus.suspended
        ? 'makerverse/account-suspended'
        : 'makerverse/account-reactivated',
    );
    const html = render(template, {
      fname: user.first_name,
    });

    await this.emailQueue.add('email', {
      to: user.email,
      subject:
        nextStatus === UserStatus.suspended
          ? 'Account Suspension Notice'
          : 'Account Reactivated ✅',
      body: html,
    });

    await this.NotificationQueue.add('send_notification_job', {
      user_id: user.id,
      message: `👋 Your account has been ${
        nextStatus === UserStatus.suspended ? 'suspended' : 'reactivated'
      } by your admin`,
    });

    return new SuccessResponseDto();
  }

  @httpGet(
    '/invites',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([UserCategory.COMMUNITY_OWNER, UserCategory.TEAM_MEMBER]),
  )
  public async invitees(@httpReq() { claim }: RequestWithClaims) {
    const invitees = await this.invite.find({
      community_id: claim.community_id,
      team_id: claim?.team_id ?? claim.id,
    });

    return new SuccessResponseDto({ data: invitees });
  }

  @httpGet(
    '/invites/:id',
    autoValidate(IdDto.validationSchema, 'params'),
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([UserCategory.COMMUNITY_OWNER, UserCategory.TEAM_MEMBER]),
  )
  public async inviteeDetails(@requestParam() { id }: IdDto) {
    const invite = await this.invite.get(id);

    return new SuccessResponseDto({ data: invite });
  }

  @httpGet(
    '/team-members',
    autoValidate(TeamQueryDto.validationSchema, 'query'),
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([UserCategory.COMMUNITY_OWNER, UserCategory.TEAM_MEMBER]),
  )
  public async teamMembers(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam()
    { search_term, status, result_per_page, cursor }: TeamQueryDto,
  ) {
    const teamMembers = await this.user.teamMembers(
      claim?.team_id ?? claim.id,
      {
        search_term,
        status,
        cursor,
        result_per_page,
      },
    );

    return new SuccessResponseDto({ data: teamMembers });
  }

  @httpPatch(
    '/invites/:id/change-permissions',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    MIDDLEWARE_TOKENS.SubscriptionCheckMiddleware,
    autoValidate(IdDto.validationSchema, 'params'),
    autoValidate(InviteDto.validationSchema),
    inCategory([UserCategory.COMMUNITY_OWNER]),
  )
  public async updateTeamMemberPermissions(
    @httpReq() { claim }: RequestWithClaims,
    @requestParam() { id }: IdDto,
    @requestBody() payload: InviteDto,
  ) {
    const user = await this.user.getById(id);
    if (!user) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'User not found');
    }

    const teamMember = await this.user.getById(user.team_id);
    if (
      (user.category !== UserCategory.TEAM_MEMBER && !teamMember) ||
      teamMember.id !== claim.id
    ) {
      throw new ApplicationError(
        StatusCodes.FORBIDDEN,
        'You can only update your team members permissions',
      );
    }

    if (assignPermissions(payload.role) === user.permissions) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'The user is already assigned the same permissions',
      );
    }

    await this.user.update(id, {
      permissions: assignPermissions(payload.role),
      role: payload.role,
    });

    const community = await this.community.get(user.community_id);

    const template = loadTemplate('makerverse/updated-permissions');

    const html = render(template, {
      fname: user.first_name,
      role: payload.role,
      communityName: community.name,
    });

    await this.emailQueue.add('email', {
      to: user.email,
      subject: 'Updated Permissions 📝',
      body: html,
    });

    await this.NotificationQueue.add('send_notification_job', {
      user_id: user.id,
      message: `👋 Your permissions have been updated to ${payload.role} by your admin`,
    });

    return new SuccessResponseDto();
  }

  @httpPost('/sso', autoValidate(SsoDto.validationSchema))
  public async sso(@requestBody() payload: SsoDto) {
    const { access_token } = payload;

    const userInfo: Record<string, any> = await this.google.getUserInfo(
      access_token,
    );

    const user = await this.user.find({
      email: userInfo.email,
      category: [UserCategory.COMMUNITY_OWNER, UserCategory.TEAM_MEMBER],
    });

    if (!user) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'User not found');
    }

    const community = await this.community.get(user.community_id);

    if (!community) {
      throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Invalid community');
    }

    const token = await this.tokenAuth.generate(
      {
        id: user.id,
        email: user.email,
        category: user.category,
        team_role: user.role,
        community_id: user.community_id,
        permissions: user.permissions,
        team_id: user?.team_id,
      },
      DURATION.DAYS,
    );

    return new SuccessResponseDto({
      data: { token, ...omit(user, ['password']) },
    });
  }

  @httpPost(
    '/forgot-password',
    autoValidate(ForgotPasswordDto.validationSchema),
  )
  public async forgotPassword(@requestBody() payload: ForgotPasswordDto) {
    const user = await this.user.find({
      email: payload.email.toLocaleLowerCase(),
      category: [UserCategory.COMMUNITY_OWNER, UserCategory.TEAM_MEMBER],
    });

    if (!user)
      throw new ApplicationError(StatusCodes.BAD_REQUEST, 'User not found');

    const otp = generateOtp(6);

    const otpToken = generateOtpToken(payload.email, otp);

    const template = loadTemplate('makerverse/otp');
    const htmlData = {
      fname: user.first_name,
      otp,
      communityName: 'Makerverse',
    };
    const html = render(template, htmlData);

    await this.emailQueue.add('email', {
      to: user.email,
      subject: 'Reset your password 🎶',
      body: html,
    });

    const otpVerificationKey = prefixKey(otpToken, otp);

    await this.redisStore.set(
      otpVerificationKey,
      {
        email: payload.email,
        community_name: 'Makerverse',
        first_name: user.first_name,
      },
      5 * DURATION.MINUTES,
    );

    // save email to enable resend
    await this.redisStore.set(
      otpToken,
      {
        email: payload.email,
        community_name: 'Makerverse',
        first_name: user.first_name,
      },
      15 * DURATION.MINUTES,
    );

    return new SuccessResponseDto({ data: { otp_token: otpToken } });
  }

  @httpPost('/reset-password', autoValidate(ResetPasswordDto.validationSchema))
  public async resetPassword(@requestBody() payload: ResetPasswordDto) {
    const otpVerificationKey = prefixKey(payload.otp_token, payload.token);

    const metadata = await this.redisStore.get<{ email: string }>(
      otpVerificationKey,
    );

    if (!metadata) {
      throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Invalid Otp');
    }

    await this.redisStore.delete(otpVerificationKey);

    const user = await this.user.find({
      email: metadata.email,
      category: [UserCategory.COMMUNITY_OWNER, UserCategory.TEAM_MEMBER],
    });

    if (!user) {
      throw new ApplicationError(StatusCodes.BAD_REQUEST, 'User not found');
    }

    const password = await hashPassword(payload.password);

    await this.user.update(user.id, { password });

    const template = loadTemplate('makerverse/reset-password');
    const htmlData = {
      fname: user.first_name,
      contactEmail: '<EMAIL>',
      communityName: 'Makerverse',
      url:
        env.node_env === 'development'
          ? 'https://beta.maker-verse.xyz/login'
          : 'https://maker-verse.xyz/login',
    };
    const html = render(template, htmlData);

    await this.emailQueue.add('email', {
      to: user.email,
      subject: 'Password reset successful 🎶🚀',
      body: html,
    });

    return new SuccessResponseDto({ message: 'Password reset successful' });
  }

  @httpGet(
    '/posts/all',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    autoValidate(PaginatedQueryDto.validationSchema, 'query'),
    inCategory([UserCategory.COMMUNITY_OWNER, UserCategory.TEAM_MEMBER]),
  )
  public async communityFeed(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { cursor, result_per_page }: PaginatedQueryDto,
  ) {
    const feed = await this.community.fetchPosts(claim.community_id, {
      cursor,
      result_per_page,
    });

    return new SuccessResponseDto({ data: feed });
  }

  @httpGet(
    '/posts/parent/:id',
    autoValidate(IdDto.validationSchema, 'params'),
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([UserCategory.COMMUNITY_OWNER, UserCategory.TEAM_MEMBER]),
  )
  public async getPostParent(
    @requestParam() { id }: IdDto,
    @httpReq() { claim }: RequestWithClaims,
  ) {
    const post = await this.post.get(id);

    if (!post) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Post not found');
    }

    const parentPost = await this.community.fetchPostParent(
      claim.community_id,
      id,
    );

    return new SuccessResponseDto({ data: parentPost });
  }

  @httpGet(
    '/posts/replies/:id',
    autoValidate(IdDto.validationSchema, 'params'),
    autoValidate(PaginatedQueryDto.validationSchema, 'query'),
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([UserCategory.COMMUNITY_OWNER, UserCategory.TEAM_MEMBER]),
  )
  public async getPostReplies(
    @requestParam() { id }: IdDto,
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { cursor, result_per_page }: PaginatedQueryDto,
  ) {
    const post = await this.post.get(id);

    if (!post) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Post not found');
    }

    const replies = await this.community.fetchPostChildren(
      claim.community_id,
      id,
      {
        cursor,
        result_per_page,
      },
    );

    return new SuccessResponseDto({ data: replies });
  }

  @httpGet(
    '/transactions',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    autoValidate(CommunityTransactionsQueryDto.validationSchema, 'query'),
    inCategory([UserCategory.COMMUNITY_OWNER, UserCategory.TEAM_MEMBER]),
  )
  public async communityTransactions(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam()
    {
      status,
      payment_type,
      type,
      page_number,
      result_per_page,
    }: CommunityTransactionsQueryDto,
  ) {
    const transactions = await this.transaction.getTransactions(
      claim.community_id,
      'community',
      {
        status,
        payment_type,
        type,
        page_number,
        result_per_page,
      },
    );

    return new SuccessResponseDto({ data: transactions });
  }

  @httpGet(
    '/subscriptions',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([UserCategory.COMMUNITY_OWNER, UserCategory.TEAM_MEMBER]),
  )
  public async communitySubscriptions(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { page_number, result_per_page }: PaginatedQueryDto,
  ) {
    const subscriptions = await this.subscription.getCommunitySubscriptions(
      claim.community_id,
      {
        page_number,
        result_per_page,
      },
    );

    return new SuccessResponseDto({ data: subscriptions });
  }

  @httpGet(
    '/wallet',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([UserCategory.COMMUNITY_OWNER, UserCategory.TEAM_MEMBER]),
    hasPermissions([
      {
        resource: PermissionResource.RevenueManagement,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
  )
  public async walletDetails(@httpReq() { claim }: RequestWithClaims) {
    const wallet = await this.user.getWalletDetails(claim?.team_id ?? claim.id);

    return new SuccessResponseDto({ data: wallet });
  }

  @httpGet(
    '/statements',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([UserCategory.COMMUNITY_OWNER, UserCategory.TEAM_MEMBER]),
    hasPermissions([
      {
        resource: PermissionResource.RevenueManagement,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
    autoValidate(PaginatedQueryDto.validationSchema, 'query'),
  )
  public async getStatements(
    @httpReq() { claim }: RequestWithClaims,
    @queryParam() { result_per_page = 10, page_number = 1 }: PaginatedQueryDto,
  ) {
    const user = await this.user.getById(claim?.team_id ?? claim.id);

    const statements = await this.revelator.retrieveStatements(
      user.revelator_id,
      user.email,
      user.enterprise_name,
      {
        enterpriseId: user.enterprise_id,
        pageNumber: page_number,
        pageSize: result_per_page,
      },
    );

    return new SuccessResponseDto({ data: statements });
  }

  @httpGet(
    '/statements/:id',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([UserCategory.COMMUNITY_OWNER, UserCategory.TEAM_MEMBER]),
    hasPermissions([
      {
        resource: PermissionResource.RevenueManagement,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
    autoValidate(IdDto.validationSchema, 'params'),
  )
  public async getStatement(
    @httpReq() { claim }: RequestWithClaims,
    @requestParam() { id }: IdDto,
  ) {
    const user = await this.user.getById(claim?.team_id ?? claim.id);

    const statement = await this.revelator.retrieveStatement(
      user.revelator_id,
      id,
    );

    return new SuccessResponseDto({ data: statement });
  }

  @httpGet(
    '/statements/:id/download',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([UserCategory.COMMUNITY_OWNER, UserCategory.TEAM_MEMBER]),
    hasPermissions([
      {
        resource: PermissionResource.RevenueManagement,
        accessLevel: ['viewer', 'editor'],
      },
    ]),
    autoValidate(IdDto.validationSchema, 'params'),
  )
  public async downloadStatement(
    @httpReq() { claim }: RequestWithClaims,
    @requestParam() { id }: IdDto,
  ) {
    const user = await this.user.getById(claim?.team_id ?? claim.id);

    const statement = await this.revelator.downloadStatement(
      user.revelator_id,
      user.email,
      user.enterprise_name,
      id,
    );

    const zip = new AdmZip(statement);
    const csvEntry = zip.getEntries().find((e) => e.entryName.endsWith('.csv'));

    if (!csvEntry) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'CSV file not found in zip',
      );
    }

    const csvBuffer = csvEntry.getData();
    const filename = csvEntry.entryName || `statement-${id}.csv`;

    await this.emailQueue.add('email', {
      to: user.email,
      subject: 'Your statement is ready for download',
      body: `<p>Hi ${user.first_name},</p>
                <p>Your Royalty statement is attached below.</p>`,
      attachments: [
        {
          filename,
          content: csvBuffer,
          contentType: 'text/csv',
        },
      ],
    });

    return new SuccessResponseDto();
  }

  private async validateCnameConfiguration(
    domain: string,
    addressesToBeResolvedTo: string[],
  ): Promise<string[]> {
    const [addresses, err] = await Deasyncify.watch(resolveCname(domain));

    if (err != null) {
      if (['ENOTFOUND', 'ETIMEOUT', 'ENODATA'].includes(err.code)) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          'Domain could not be reached, please check if your domain was configured correctly',
        );
      }

      throw err;
    }

    if (!addresses.some((addr) => addressesToBeResolvedTo.includes(addr))) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        `Please configure your cname to point to ${addressesToBeResolvedTo}`,
      );
    }

    return addresses;
  }

  @httpGet(
    '/:id',
    adminOnlyAuth,
    MIDDLEWARE_TOKENS.AuthMiddleware,
    autoValidate(IdDto.validationSchema, 'params'),
    autoValidate(CommunityReportDto.validationSchema, 'query'),
  )
  public async fetchCommunityById(
    @requestParam() { id }: IdDto,
    @queryParam() payload: CommunityReportDto,
  ) {
    const community = await this.community.get(id);

    if (!community) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Community not found');
    }

    const report = await this.reports.communityReport(id, payload);

    const owner = await this.user.find({
      community_id: community.id,
      category: [UserCategory.COMMUNITY_OWNER],
    });

    return new SuccessResponseDto({
      data: {
        ...community,
        report,
        owner: pick(
          owner,
          'id',
          'first_name',
          'last_name',
          'email',
          'email_verified',
          'phone',
          'phone_verified',
          'revelator_id',
          'profile_picture',
          'cover_picture',
          'created_at',
          'updated_at',
        ),
      },
    });
  }

  @httpGet(
    '/:id/users',
    adminOnlyAuth,
    MIDDLEWARE_TOKENS.AuthMiddleware,
    autoValidate(IdDto.validationSchema, 'params'),
    autoValidate(CommunityUsersQueryDto.validationSchema, 'query'),
  )
  public async fetchCommunityUsers(
    @requestParam() { id }: IdDto,
    @queryParam()
    {
      search_term,
      category,
      role,
      page_number,
      result_per_page,
    }: CommunityUsersQueryDto,
  ) {
    const users = await this.user.all(id, {
      selectFields: [
        'email',
        'email_verified',
        'phone',
        'phone_verified',
        'community_id',
        'revelator_id',
        'gender',
        'updated_at',
      ],
      role,
      category,
      page_number,
      result_per_page,
      search_term,
    });

    return new SuccessResponseDto({ data: users });
  }

  @httpPost(
    '/:id/review',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    inCategory([UserCategory.COMMUNITY_OWNER]),
    autoValidate(IdDto.validationSchema, 'params'),
  )
  public async reviewCommunity(@requestParam() { id }: IdDto) {
    const community = await this.community.get(id);

    if (!community) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Community not found');
    }

    if (community.status === 'active') {
      throw new ApplicationError(
        StatusCodes.CONFLICT,
        'This community is already active and cannot be set to review',
      );
    }

    if (!community.custom_domain_active) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'This community does not have an active domain',
      );
    }

    const subPlans = await this.monetization.getCommunitySubPlans(community.id);

    if (subPlans.length === 0) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        "This community doesn't have monetization setup",
      );
    }

    await this.community.update(community.id, { status: 'review' });

    return new SuccessResponseDto({ message: 'Community now in review' });
  }

  @httpPost(
    '/:id/activate',
    adminOnlyAuth,
    MIDDLEWARE_TOKENS.AuthMiddleware,
    autoValidate(IdDto.validationSchema, 'params'),
  )
  public async activateCommunity(@requestParam() { id }: IdDto) {
    const community = await this.community.get(id);

    if (!community) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Community not found');
    }

    if (!community.custom_domain_active) {
      throw new ApplicationError(
        StatusCodes.CONFLICT,
        'This community does not have an active domain',
      );
    }

    const subPlans = await this.monetization.getCommunitySubPlans(community.id);

    if (subPlans.length === 0) {
      throw new ApplicationError(
        StatusCodes.CONFLICT,
        "This community doesn't have monetization setup",
      );
    }

    if (community.status !== 'review' && community.status !== 'suspended') {
      throw new ApplicationError(
        StatusCodes.CONFLICT,
        'This community is not in review and therefore cannot be activated',
      );
    }

    let genericProduct: ProductObject;

    for (const subPlan of subPlans) {
      const updatedMetadata = {
        ...subPlan.metadata,
        pricing: { ...subPlan.metadata.pricing },
      };

      if (!genericProduct) {
        const products = await this.stripe.searchProduct({
          query: "metadata['plan_type']:'generic'",
        });

        genericProduct = products.data[0];

        genericProduct.metadata = Object.keys(genericProduct.metadata).reduce(
          (acc, key) => {
            acc[key] = safeJsonParse(genericProduct.metadata[key]);
            return acc;
          },
          {} as Record<string, any>,
        );

        updatedMetadata.product_id = genericProduct.id;
      }

      if (!updatedMetadata.pricing?.monthly) {
        const monthlyPrice = await this.stripe.createPrice({
          product: genericProduct.id,
          currency: subPlan.currency,
          unit_amount_decimal: String(subPlan.price_per_month * 100),
          recurring: {
            interval: 'month',
          },
        });
        updatedMetadata.pricing.monthly = monthlyPrice.id;
      }

      if (!updatedMetadata.pricing?.annual) {
        const annuallyPrice = await this.stripe.createPrice({
          product: genericProduct.id,
          currency: subPlan.currency,
          unit_amount_decimal: String(subPlan.price_per_annum * 100),
          recurring: {
            interval: 'year',
          },
        });
        updatedMetadata.pricing.annual = annuallyPrice.id;
      }

      await this.monetization.update(subPlan.id, {
        metadata: updatedMetadata,
      });
    }

    await this.community.update(community.id, { status: 'active' });

    return new SuccessResponseDto();
  }

  @httpPost(
    '/:id/update',
    adminOnlyAuth,
    MIDDLEWARE_TOKENS.AuthMiddleware,
    autoValidate(IdDto.validationSchema, 'params'),
    autoValidate(SuspendReviewCommunityDto.validationSchema),
  )
  public async suspendReviewCommunity(
    @requestParam() { id }: IdDto,
    @requestBody() { status }: SuspendReviewCommunityDto,
  ) {
    const community = await this.community.get(id);

    if (!community) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, 'Community not found');
    }

    await this.community.update(community.id, { status });

    return new SuccessResponseDto();
  }
}
