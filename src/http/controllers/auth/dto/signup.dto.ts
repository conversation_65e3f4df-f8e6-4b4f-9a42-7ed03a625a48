import { ExtendedJoi } from '@app/utils/joi-utils';
import PartialInstantiable from '@app/utils/partial-instantiable';

export class SignupDto extends PartialInstantiable<SignupDto> {
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  country: string;
  password: string;
  service_update: boolean;
  community_id: string;

  static validationSchema = ExtendedJoi.object<SignupDto, true>({
    first_name: ExtendedJoi.string().required(),
    last_name: ExtendedJoi.string().required(),
    email: ExtendedJoi.string().email().lowercase().required(),
    phone: ExtendedJoi.string().phoneNumber({ format: 'e164' }),
    country: ExtendedJoi.string().required(),
    password: ExtendedJoi.string()
      .min(8)
      .max(30)
      .pattern(
        new RegExp(
          '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[!@#$%^&*])[A-Za-z\\d!@#$%^&*]{8,30}$',
        ),
      )
      .required()
      .messages({
        'string.min': 'Password must be at least 8 characters long',
        'string.max': 'Password must be at most 30 characters long',
        'string.pattern.base':
          'Password must include at least one uppercase letter, one lowercase letter, one number, and one special character',
        'any.required': 'Password is required',
      }),
    service_update: ExtendedJoi.boolean().default(false),
    community_id: ExtendedJoi.string().required(),
  });
}
