import Joi from 'joi';

export class SsoCallbackDto {
  provider: 'google' | 'facebook' | 'instagram' | 'tiktok';

  static validationSchema = Joi.object<SsoCallbackDto, true>({
    provider: Joi.string()
      .valid('google', 'facebook', 'instagram', 'tiktok')
      .required(),
  });
}

export class SsoDto {
  code: string;

  static validationSchema = Joi.object<SsoDto, true>({
    code: Joi.string().required(),
  });
}
