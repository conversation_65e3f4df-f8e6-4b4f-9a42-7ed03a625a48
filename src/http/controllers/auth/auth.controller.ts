import { inject } from 'inversify';
import {
  controller,
  httpGet,
  httpPatch,
  httpPost,
  request as httpReq,
  response as httpRes,
  queryParam,
  requestBody,
  requestParam,
} from 'inversify-express-utils';
import {
  MIDDLEWARE_TOKENS,
  MODULE_TOKENS,
  SERVICE_TOKENS,
} from '@app/internal/ioc/tokens';
import { RedisStore } from '@app/internal/redis/store';
import { UserService } from '@app/services/user/user.service';
import { SignupDto } from './dto/signup.dto';
import { autoValidate } from '@app/http/middlewares/validation.middleware';
import { ApplicationError } from '@app/internal/errors';
import { StatusCodes } from 'http-status-codes';
import { generateOtp, generateOtpToken } from '@app/utils/otp-utils';
import { prefixKey } from '@app/utils/prefix-key';
import { ulid } from 'ulid';
import {
  generateRandomPassword,
  hashPassword,
  verifyPassword,
} from '@app/utils/bcrypt-utils';
import { DURATION } from '@app/internal/enums';
import { JobQueueManager } from '@app/internal/bull';
import { emailQueue, SendEmailJob } from '@app/mq/jobs';
import { SuccessResponseDto } from '@app/http/dtos/http-response.dto';
import { VerifyOtpDto } from './dto/verify-otp.dto';
import { TokenAuth } from '@app/internal/token/auth';
import { Claim, RequestWithClaims } from '@app/internal/types';
import { LoginDto } from './dto/login.dto';
import { SetupProfileDto } from './dto/setup-profile.dto';
import { Role, UserCategory } from '@app/services/user/entities/user.entity';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { SsoCallbackDto, SsoDto } from './dto/sso.dto';
import { omit } from 'lodash';
import { GoogleOauthClient } from '@app/modules/google-oauth';
import { loadTemplate, render } from '@app/utils/template.utils';
import { passTempToken } from '@app/http/middlewares/auth.middleware';
import { CommunityService } from '@app/services/community/community.service';
import { ChangePasswordDto } from './dto/change-password.dto';
import { generateUsername } from '@app/utils/post.utils';
import { syncCatch } from '@app/utils/sync-catch';
import { WalletService } from '@app/services/wallet/wallet.service';
import { WalletType } from '@app/services/wallet/entities/wallet.entity';
import { Currency } from '@app/services/transaction/entities/transaction.entity';
import { FacebookOauthClient } from '@app/modules/facebook-oauth';
import { InstagramOauthClient } from '@app/modules/instagram-oauth';
import { TikTokOauthClient } from '@app/modules/tiktok-oauth';
import { SsoInitDto } from './dto/init-sso.dto';
import { env } from '@app/config/env';
import { Response } from 'express';
import { Community } from '@app/services/community/entities/community.entity';
import { PresenceCache } from '@app/internal/presence';

export type OnboardingUser = {
  id: string;
  first_name: string;
  last_name: string;
  display_name?: string;
  email: string;
  email_verified: boolean;
  phone: string;
  country: string;
  password: string;
  service_update: boolean;
  category?: UserCategory;
  username?: string;
  community_id: string;
};

@controller('/auth')
export class AuthController {
  private readonly emailQueue =
    this.jobQueueManager.getQueue<SendEmailJob>(emailQueue);

  constructor(
    @inject(SERVICE_TOKENS.CommunityService)
    private readonly community: CommunityService,
    @inject(MODULE_TOKENS.RedisStore) private readonly redisStore: RedisStore,
    @inject(MODULE_TOKENS.JobQueueManager)
    private readonly jobQueueManager: JobQueueManager,
    @inject(MODULE_TOKENS.TokenAuth) private readonly tokenAuth: TokenAuth,
    @inject(SERVICE_TOKENS.UserService)
    private readonly user: UserService,
    @inject(MODULE_TOKENS.GoogleOauthClient)
    private readonly google: GoogleOauthClient,
    @inject(MODULE_TOKENS.FacebookOauthClient)
    private readonly facebook: FacebookOauthClient,
    @inject(MODULE_TOKENS.InstagramOauthClient)
    private readonly instagram: InstagramOauthClient,
    @inject(MODULE_TOKENS.TikTokOauthClient)
    private readonly tiktok: TikTokOauthClient,
    @inject(SERVICE_TOKENS.WalletService)
    private readonly wallet: WalletService,
    @inject(MODULE_TOKENS.PresenceCache)
    private readonly presence: PresenceCache,
  ) {}

  @httpPost('/signup', autoValidate(SignupDto.validationSchema))
  public async signup(@requestBody() payload: SignupDto) {
    const community = await this.community.get(payload.community_id);

    if (!community) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'Invalid community id',
      );
    }

    const userWithEmail = await this.user.find({
      email: payload.email,
      category: [UserCategory.CREATOR, UserCategory.BRAND, UserCategory.FAN],
      community_id: payload.community_id,
    });

    if (userWithEmail) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'User with this email already exists',
      );
    }

    const userWithPhone = await this.user.find({
      phone: payload.phone,
      category: [UserCategory.CREATOR, UserCategory.BRAND, UserCategory.FAN],
      community_id: payload.community_id,
    });

    if (userWithPhone) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'User with phone number already exists',
      );
    }

    const onboardingKey = prefixKey('signup_new', payload.email);

    const onboardingUser = (await this.redisStore.get<OnboardingUser>(
      onboardingKey,
    )) ?? { id: ulid() };

    const { email, password, ...rest } = payload;

    Object.assign(onboardingUser, {
      email: email,
      password: await hashPassword(password),
      ...rest,
    });

    await this.redisStore.set(onboardingKey, onboardingUser, DURATION.DAYS);

    const otp = generateOtp(6);

    // create public token to search for otp in cache
    const otpToken = generateOtpToken(payload.email, otp);

    const template = loadTemplate('community/otp');
    const htmlData = {
      fname: payload.first_name,
      otp,
      communityName: community.name,
      logoUrl: community.brand_themed_logo,
      color: community.brand_color,
    };

    const html = render(template, htmlData);

    await this.emailQueue.add('email', {
      to: payload.email,
      subject: 'Verify your email 🎶',
      body: html,
    });

    // create verification key making otp itself act as a private key
    const otpVerificationKey = prefixKey(otpToken, otp);

    await this.redisStore.set(
      otpVerificationKey,
      {
        community_id: community.id,
        email: payload.email,
        community_name: community.name,
        first_name: payload.first_name,
      },
      5 * DURATION.MINUTES,
    );

    // save email to enable resend
    await this.redisStore.set(
      otpToken,
      {
        community_id: community.id,
        email: payload.email,
        community_name: community.name,
        first_name: payload.first_name,
      },
      15 * DURATION.MINUTES,
    );

    return new SuccessResponseDto({ data: { otp_token: otpToken } });
  }

  @httpPost('/signup/email/verify', autoValidate(VerifyOtpDto.validationSchema))
  public async verifyEmail(@requestBody() payload: VerifyOtpDto) {
    const otpVerificationKey = prefixKey(payload.otp_token, payload.otp);

    const metadata = await this.redisStore.get<{ email: string }>(
      otpVerificationKey,
    );

    if (!metadata) {
      throw new ApplicationError(StatusCodes.BAD_REQUEST, 'invalid otp');
    }

    // revoke otp by deleting its verification key after it has been used
    await this.redisStore.delete(otpVerificationKey);
    await this.redisStore.delete(payload.otp_token);

    const onboardingKey = prefixKey('signup_new', metadata.email);

    const onboardingUser = await this.redisStore.get<OnboardingUser>(
      onboardingKey,
    );

    // mark email as verified
    Object.assign(onboardingUser, { email_verified: true });

    await this.redisStore.set(onboardingKey, onboardingUser);

    // mark token as token as temporary
    const tokenPayload: Claim = {
      id: onboardingUser.id,
      email: onboardingUser.email,
      temp: true,
    };

    const token = await this.tokenAuth.generate(tokenPayload, DURATION.DAYS);

    return new SuccessResponseDto({ data: { token } });
  }

  @httpPost(
    '/profile/setup',
    passTempToken,
    MIDDLEWARE_TOKENS.AuthMiddleware,
    autoValidate(SetupProfileDto.validationSchema),
  )
  public async setupProfile(
    @requestBody() payload: SetupProfileDto,
    @httpReq() req: RequestWithClaims,
  ) {
    const claim = req.claim;

    const userExists = await this.user
      .getById(claim.id)
      .then((user) => user != null);

    if (userExists) {
      throw new ApplicationError(
        StatusCodes.CONFLICT,
        'Your profile is already complete, please login',
      );
    }

    const onboardingKey = prefixKey('signup_new', claim.email);

    const onboardingUser = await this.redisStore.get<OnboardingUser>(
      onboardingKey,
    );

    const { user_category, ...rest } = payload;

    Object.assign(onboardingUser, {
      category: user_category,
      role: Role.OWNER,
      enterprise_name: `${onboardingUser.first_name} ${onboardingUser.last_name}`,
      service_update: true,
      ...rest,
    });

    const user = await this.user.create(onboardingUser);
    await this.wallet.getOrCreate(user.id, Currency.USD, WalletType.FIAT);

    await this.redisStore.delete(onboardingKey);

    await this.tokenAuth.reset(req.claimId, {
      id: user.id,
      email: user.email,
      category: user.category,
      team_role: user.role,
      vibrate_id: user.vibrate_id,
      enterprise_name: user.enterprise_name,
      revelator_id: user.revelator_id,
      community_id: user.community_id,
      permissions: user.permissions,
      team_id: user?.team_id,
    });

    const community = await this.community.get(user.community_id);

    const template = loadTemplate('community/welcome');
    const htmlData = {
      fname: user.first_name,
      communityName: community.name,
      color: community.brand_color,
      contactEmail: community.support_email ?? '<EMAIL>',
      url: `https://${community.custom_domain}/login`,
    };

    const html = render(template, htmlData);

    await this.emailQueue.add('email', {
      to: user.email,
      subject: 'Welcome Onboard 🎶🚀',
      body: html,
    });

    return new SuccessResponseDto({ data: omit(user, ['password']) });
  }

  @httpPost('/login', autoValidate(LoginDto.validationSchema))
  public async login(@requestBody() payload: LoginDto) {
    const user = await this.user.find({
      email: payload.email,
      category: [
        UserCategory.CREATOR,
        UserCategory.BRAND,
        UserCategory.FAN,
        UserCategory.TEAM_MEMBER,
      ],
      community_id: payload.community_id,
    });

    if (user == null) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'Incorrect email/password',
      );
    }

    const isPasswordCorrect = await verifyPassword(
      payload.password,
      user.password,
    );

    if (!isPasswordCorrect) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'Incorrect email/password',
      );
    }

    await this.user.update(user.id, { last_login: new Date() });

    if (user.category === UserCategory.TEAM_MEMBER) {
      const teamOwner = await this.user.getById(user.id);

      if (teamOwner.category === UserCategory.COMMUNITY_OWNER) {
        throw new ApplicationError(
          StatusCodes.FORBIDDEN,
          'You do not have access to this resource',
        );
      }
    }

    const token = await this.tokenAuth.generate(
      {
        id: user.id,
        email: user.email,
        category: user.category,
        team_role: user.role,
        vibrate_id: user.vibrate_id,
        enterprise_name: user.enterprise_name,
        revelator_id: user.revelator_id,
        community_id: user.community_id,
        permissions: user.permissions,
        team_id: user?.team_id,
      },
      DURATION.DAYS,
    );

    return new SuccessResponseDto({
      data: { token, ...omit(user, ['password']) },
    });
  }

  @httpPost(
    '/forgot-password',
    autoValidate(ForgotPasswordDto.validationSchema),
  )
  public async forgotPassword(
    @httpReq() req: RequestWithClaims,
    @requestBody() payload: ForgotPasswordDto,
  ) {
    const origin = req.headers['origin'];

    const [clientUrl, urlParseErr] = syncCatch(() => new URL(origin));

    if (urlParseErr != null) {
      throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Invalid Request');
    }

    const community = await this.community
      .find({
        custom_domain: clientUrl.hostname,
        custom_domain_active: true,
      })
      .then((communities) => communities[0]);

    if (!community) {
      throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Invalid Request');
    }

    const user = await this.user.find({
      email: payload.email.toLocaleLowerCase(),
      category: [
        UserCategory.CREATOR,
        UserCategory.BRAND,
        UserCategory.FAN,
        UserCategory.TEAM_MEMBER,
      ],
      community_id: community.id,
    });

    if (!user) {
      throw new ApplicationError(StatusCodes.BAD_REQUEST, 'User not found');
    }

    const otp = generateOtp(6);

    const otpToken = generateOtpToken(payload.email, otp);

    const template = loadTemplate('community/otp');
    const htmlData = {
      fname: user.first_name,
      otp,
      communityName: community.name,
      logoUrl: community.brand_themed_logo,
      color: community.brand_color,
    };
    const html = render(template, htmlData);

    await this.emailQueue.add('email', {
      to: user.email,
      subject: 'Reset your password 🎶',
      body: html,
    });

    const otpVerificationKey = prefixKey(otpToken, otp);

    await this.redisStore.set(
      otpVerificationKey,
      {
        community_id: community.id,
        email: payload.email,
        community_name: community.name,
        first_name: user.first_name,
      },
      5 * DURATION.MINUTES,
    );

    // save email to enable resend
    await this.redisStore.set(
      otpToken,
      {
        community_id: community.id,
        email: payload.email,
        community_name: community.name,
        first_name: user.first_name,
      },
      15 * DURATION.MINUTES,
    );

    return new SuccessResponseDto({ data: { otp_token: otpToken } });
  }

  @httpPost('/reset-password', autoValidate(ResetPasswordDto.validationSchema))
  public async resetPassword(@requestBody() payload: ResetPasswordDto) {
    const otpVerificationKey = prefixKey(payload.otp_token, payload.token);

    const metadata = await this.redisStore.get<{ email: string }>(
      otpVerificationKey,
    );

    if (!metadata) {
      throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Invalid Otp');
    }

    await this.redisStore.delete(otpVerificationKey);

    const user = await this.user.find({
      email: metadata.email,
      category: [UserCategory.CREATOR, UserCategory.BRAND, UserCategory.FAN],
    });

    if (!user) {
      throw new ApplicationError(StatusCodes.BAD_REQUEST, 'User not found');
    }

    const community = await this.community.get(user.community_id);

    const password = await hashPassword(payload.password);

    await this.user.update(user.id, { password });

    const template = loadTemplate('community/reset-password');
    const htmlData = {
      fname: user.first_name,
      contactEmail: community.support_email ?? '<EMAIL>',
      communityName: community.name,
      url: `https://${community.custom_domain}/login`,
      color: community.brand_color,
    };
    const html = render(template, htmlData);

    await this.emailQueue.add('email', {
      to: user.email,
      subject: 'Password reset successful 🎶🚀',
      body: html,
    });

    return new SuccessResponseDto({ message: 'Password reset successful' });
  }

  @httpPatch(
    '/change-password',
    MIDDLEWARE_TOKENS.AuthMiddleware,
    MIDDLEWARE_TOKENS.SubscriptionCheckMiddleware,
    autoValidate(ChangePasswordDto.validationSchema),
  )
  public async changePassword(
    @requestBody() payload: ChangePasswordDto,
    @httpReq() { claim }: RequestWithClaims,
  ) {
    const { id } = claim;
    const user = await this.user.getById(id);

    if (!user) {
      throw new ApplicationError(StatusCodes.NOT_FOUND, "User doesn't exist");
    }

    const community = await this.community.get(user.community_id);

    if (!community) {
      throw new ApplicationError(
        StatusCodes.NOT_FOUND,
        "User doesn't belong in a valid community",
      );
    }

    const old_password = await hashPassword(payload.old_password);
    const new_password = await hashPassword(payload.new_password);

    if (old_password !== user.password) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'Old password not correct',
      );
    }

    if (old_password === new_password) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'New password cannot be the same as the old password.',
      );
    }

    await this.user.update(user.id, { password: new_password });

    const template = loadTemplate('community/reset-password');
    const htmlData = {
      fname: user.first_name,
      contactEmail: community.support_email,
      communityName: community.name,
      url: `https://${community.custom_domain}/login`,
      logoUrl: community.brand_themed_logo,
      color: community.brand_color,
    };
    const html = render(template, htmlData);

    await this.emailQueue.add('email', {
      to: user.email,
      subject: 'Password reset successful 🎶🚀',
      body: html,
    });

    return new SuccessResponseDto({ message: 'Password reset successful' });
  }

  @httpGet('/sso/init', autoValidate(SsoInitDto.validationSchema, 'query'))
  public async init(
    @httpReq() req: Request,
    @queryParam() { provider, community_id }: SsoInitDto,
  ) {
    let community: Community;
    if (!community_id) {
      const origin = req.headers['origin'];

      const [clientUrl, urlParseErr] = syncCatch(() => new URL(origin));

      if (urlParseErr != null) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          'Invalid Request Header',
        );
      }

      community = await this.community
        .find({
          custom_domain: clientUrl.hostname,
          custom_domain_active: true,
        })
        .then((d) => d[0]);
    }

    community = await this.community.get(community_id);

    if (!community) {
      throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Invalid Community');
    }

    const redisKey = `sso_state:${provider}`;

    await this.redisStore.set(redisKey, { community_id: community.id });

    const redirectUri = `https://${env.host_name}/auth/sso/${provider}/callback`;

    let url: string;

    switch (provider) {
      case 'google':
        url = this.google.getAuthUrl(redirectUri);
        break;
      case 'facebook':
        url = this.facebook.getAuthUrl(redirectUri);
        break;
      case 'instagram':
        url = this.instagram.getAuthUrl(redirectUri);
        break;
      case 'tiktok':
        url = this.tiktok.getAuthUrl(redirectUri);
        break;
      default:
        throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Unknown provider');
    }

    return new SuccessResponseDto({ data: { url } });
  }

  @httpGet(
    '/sso/:provider/callback',
    autoValidate(SsoCallbackDto.validationSchema, 'params'),
    autoValidate(SsoDto.validationSchema, 'query'),
  )
  public async sso(
    @requestParam() param: SsoCallbackDto,
    @queryParam() { code }: SsoDto,
    @httpRes() res: Response,
  ) {
    const { provider } = param;

    const cache = await this.redisStore.get<{ community_id: string }>(
      `sso_state:${provider}`,
    );
    if (!cache) {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'Invalid or expired state',
      );
    }

    const community_id = cache.community_id;

    const community = await this.community.get(community_id);

    if (!community) {
      throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Invalid community');
    }

    const redirectUri = `https://${env.host_name}/auth/sso/${provider}/callback`;

    const tokens = await (async () => {
      switch (provider) {
        case 'google':
          return await this.google.exchangeCode(code, redirectUri);
        case 'facebook':
          return await this.facebook.exchangeCode(code, redirectUri);
        case 'instagram':
          return await this.instagram.exchangeCode(code, redirectUri);
        case 'tiktok':
          return await this.tiktok.exchangeCode(code, redirectUri);
        default:
          throw new ApplicationError(
            StatusCodes.BAD_REQUEST,
            `Unknown provider ${provider}`,
          );
      }
    })();

    const userInfo = await (async () => {
      switch (provider) {
        case 'google':
          return await this.google.getUserInfo(tokens.access_token);
        case 'facebook':
          return await this.facebook.getUserInfo(tokens.access_token);
        case 'instagram':
          return await this.instagram.getUserInfo(tokens.access_token);
        case 'tiktok':
          return await this.tiktok.getUserInfo(tokens.access_token);
        default:
          throw new ApplicationError(
            StatusCodes.BAD_REQUEST,
            `Unknown provider ${provider}`,
          );
      }
    })();

    const providerField = `${provider}_id`;

    let user = await this.user.find({
      email: userInfo.email,
      community_id,
      category: [UserCategory.CREATOR, UserCategory.BRAND, UserCategory.FAN],
    });

    if (!user) {
      const onboardingKey = prefixKey('signup_new', userInfo.email);

      const onboardingUser = (await this.redisStore.get(onboardingKey)) ?? {
        id: ulid(),
      };

      const userData: any = {
        id: onboardingUser.id,
        email: userInfo.email,
        first_name: userInfo.given_name,
        last_name: userInfo.family_name,
        display_name: userInfo.name,
        password: await hashPassword(generateRandomPassword()),
        username: generateUsername(12),
        service_update: true,
        community_id,
        [providerField]: userInfo.id,
      };

      await this.redisStore.set(onboardingKey, userData, DURATION.DAYS);

      const token = await this.tokenAuth.generate(
        { id: onboardingUser.id, email: userInfo.email, sso: true },
        DURATION.DAYS,
      );

      const redirectUrl = `https://${community.custom_domain}/sso/${provider}/callback?token=${token}`;
      return res.redirect(redirectUrl);
    }

    if (!(user as any)[providerField]) {
      user = await this.user.update(user.id, { [providerField]: userInfo.id });
    }

    const token = await this.tokenAuth.generate(
      {
        id: user.id,
        email: user.email,
        category: user.category,
        team_role: user.role,
        vibrate_id: user.vibrate_id,
        enterprise_name: user.enterprise_name,
        revelator_id: user.revelator_id,
        community_id: user.community_id,
        permissions: user?.permissions,
        team_id: user?.team_id,
      },
      DURATION.DAYS,
    );

    const redirectUrl = `https://${community.custom_domain}/sso/${provider}/callback?token=${token}`;

    return res.redirect(redirectUrl);
  }

  @httpGet('/logout', passTempToken, MIDDLEWARE_TOKENS.AuthMiddleware)
  public async logout(@httpReq() req: RequestWithClaims) {
    await this.tokenAuth.revoke(req.claimId);

    await this.presence.offline(req.claim.id);

    return new SuccessResponseDto();
  }
}
