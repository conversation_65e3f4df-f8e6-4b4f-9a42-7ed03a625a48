import { Response, NextFunction } from 'express';
import { StatusCodes } from 'http-status-codes';
import { RequestWithClaims } from '@app/internal/types';
import { ApplicationError } from '@app/internal/errors';
import { DURATION } from '@app/internal/enums';
import { MODULE_TOKENS, SERVICE_TOKENS } from '@app/internal/ioc/tokens';
import { SubscriptionService } from '@app/services/subscription/subscription.service';
import { inject } from 'inversify';
import { BaseMiddleware } from 'inversify-express-utils';
import { UserService } from '@app/services/user/user.service';
import { UserCategory } from '@app/services/user/entities/user.entity';
import {
  UpsertRevelatorAccountJob,
  upsertRevelatorAccountQueue,
} from '@app/mq/jobs';
import { JobQueueManager } from '@app/internal/bull';

export class SubscriptionCheckMiddleware extends BaseMiddleware {
  @inject(SERVICE_TOKENS.SubscriptionService)
  subscription: SubscriptionService;
  @inject(SERVICE_TOKENS.UserService)
  user: UserService;

  private readonly upsertRevelatorAccountQueue =
    this.jobQueueManager.getQueue<UpsertRevelatorAccountJob>(
      upsertRevelatorAccountQueue,
    );

  constructor(
    @inject(MODULE_TOKENS.JobQueueManager)
    private readonly jobQueueManager: JobQueueManager,
  ) {
    super();
  }

  public async handler(
    { claim }: RequestWithClaims,
    _res: Response,
    next: NextFunction,
  ) {
    try {
      if (claim.admin_auth) {
        next();
        return;
      }

      const user = await this.user.getById(claim?.team_id ?? claim.id);
      if (![UserCategory.FAN, UserCategory.BRAND].includes(user.category)) {
        const subscription = await this.subscription.getCurrentSubscription(
          claim?.team_id ?? claim.id,
        );

        const today = new Date();

        if (subscription && subscription.status === 'active') {
          if (today > subscription.next_payment_date) {
            await this.subscription.update(subscription.id, {
              status: 'paused',
              failed_at: today,
            });

            return next(
              new ApplicationError(
                StatusCodes.FORBIDDEN,
                'Your subscription has expired, please subscribe to a plan',
              ),
            );
          }

          await this.upsertRevelatorAccountQueue.add(
            upsertRevelatorAccountQueue,
            {
              user_id: user.id,
            },
          );
          return next();
        }

        if (
          subscription &&
          subscription.status === 'paused' &&
          subscription.failed_at
        ) {
          const failedAt = new Date(subscription.failed_at);
          const now = new Date();
          const gracePeriodMs = 7 * DURATION.DAYS;
          const elapsedMs = now.getTime() - failedAt.getTime();
          if (elapsedMs > gracePeriodMs) {
            await this.subscription.update(subscription.id, {
              status: 'cancelled',
            });

            return next(
              new ApplicationError(
                StatusCodes.FORBIDDEN,
                'Subscription grace period expired',
              ),
            );
          }
          return next();
        }

        return next(
          new ApplicationError(
            StatusCodes.FORBIDDEN,
            'No active subscription found',
          ),
        );
      } else {
        return next();
      }
    } catch (e) {
      next(e);
    }
  }
}
