import { inject, injectable } from 'inversify';
import { ApplicationError } from '@app/internal/errors';
import { StatusCodes } from 'http-status-codes';
import { Request, Response, NextFunction } from 'express';
import Deasyncify from 'deasyncify';
import { BaseMiddleware } from 'inversify-express-utils';
import { MODULE_TOKENS, SERVICE_TOKENS } from '@app/internal/ioc/tokens';
import { TokenAuth, InvalidTokenError } from '@app/internal/token/auth';
import { Claim } from '@app/internal/types';
import { DURATION } from '@app/internal/enums';
import { SubscriptionService } from '@app/services/subscription/subscription.service';
import { PresenceCache } from '@app/internal/presence';
import { UserService } from '@app/services/user/user.service';
import { UserStatus } from '@app/services/user/entities/user.entity';

export function passTempToken(
  req: Request,
  _res: Response,
  next: NextFunction,
) {
  (req as any).passTempToken = true;

  next();
}

export function adminOnlyAuth(
  req: Request,
  _res: Response,
  next: NextFunction,
) {
  (req as any).adminOnlyAuth = true;

  next();
}

@injectable()
export class AuthMiddleware extends BaseMiddleware {
  @inject(MODULE_TOKENS.TokenAuth) tokenAuth: TokenAuth;
  @inject(SERVICE_TOKENS.SubscriptionService)
  subscriptionService: SubscriptionService;
  @inject(MODULE_TOKENS.PresenceCache) presence: PresenceCache;
  @inject(SERVICE_TOKENS.UserService) private readonly user: UserService;

  public async handler(req: Request, _res: Response, next: NextFunction) {
    try {
      const authorizationHeader = String(req.headers['authorization']);

      const auth_token = authorizationHeader?.split?.(' ')?.[1];

      if (!auth_token) {
        throw new ApplicationError(StatusCodes.UNAUTHORIZED, 'UNAUTHORIZED');
      }

      const [tokenPayload, err] = await Deasyncify.watch<Claim>(
        this.tokenAuth.verify(auth_token),
      );

      if (err != null) {
        if (err instanceof InvalidTokenError) {
          throw new ApplicationError(StatusCodes.UNAUTHORIZED, 'UNAUTHORIZED');
        }

        throw err;
      }

      if (!tokenPayload.temp) {
        // extend token ttl everytime it is used (only extend tokens that are marked to not be temporary)
        await this.tokenAuth.extend(auth_token, 1 * DURATION.DAYS);
      } else if (!(req as any).passTempToken) {
        throw new ApplicationError(StatusCodes.UNAUTHORIZED, 'UNAUTHORIZED');
      }

      if ((req as any).adminOnlyAuth && !tokenPayload.admin_auth) {
        throw new ApplicationError(StatusCodes.UNAUTHORIZED, 'UNAUTHORIZED');
      }

      (req as any).claim = tokenPayload;

      const user = await this.user.getById(tokenPayload.id);

      if (user.status === UserStatus.suspended)
        throw new ApplicationError(
          StatusCodes.FORBIDDEN,
          'Your team member account has been suspended, contact account owner for more assistance',
        );

      if (user.status === UserStatus.blocked)
        throw new ApplicationError(
          StatusCodes.FORBIDDEN,
          'Your account has been blocked, contact community admin for more assistance',
        );

      await this.presence.online(tokenPayload.id);

      // provide token's id to request to enable revoking of access
      (req as any).claimId = await this.tokenAuth.getTokenId(auth_token);

      next();
    } catch (e) {
      next(e);
    }
  }
}

@injectable()
export class OptionalAuthMiddleware extends BaseMiddleware {
  @inject(MODULE_TOKENS.TokenAuth) tokenAuth: TokenAuth;
  @inject(MODULE_TOKENS.PresenceCache) presence: PresenceCache;

  public async handler(req: Request, _res: Response, next: NextFunction) {
    try {
      const authorizationHeader = String(req.headers['authorization']);

      const auth_token = authorizationHeader?.split?.(' ')?.[1];

      if (auth_token) {
        const [tokenPayload, err] = await Deasyncify.watch(
          this.tokenAuth.verify<Claim>(auth_token),
        );

        if (err != null) {
          next();

          return;
        }

        if (!tokenPayload.temp) {
          // extend token ttl everytime it is used (only extend tokens that are marked to not be temporary)
          await this.tokenAuth.extend(auth_token, 1 * DURATION.DAYS);
        }

        (req as any).claim = tokenPayload;

        await this.presence.online(tokenPayload.id);

        // provide token's id to request to enable revoking of access
        (req as any).claimId = await this.tokenAuth.getTokenId(auth_token);
      }

      next();
    } catch (e) {
      next(e);
    }
  }
}
