import {
  subWeeks,
  subMonths,
  subYears,
  differenceInCalendarDays,
  differenceInCalendarMonths,
  endOfMonth,
  startOfMonth,
  addDays,
  startOfDay,
  addMonths,
} from 'date-fns';
import { PeriodFilter } from '@app/http/controllers/analytics/dto/period.dto';
import { ApplicationError } from '@app/internal/errors';
import { StatusCodes } from 'http-status-codes';
import { format } from 'date-fns';

const BEGINNING_OF_TIME = new Date('2024-01-01T00:00:00Z');

export function previousPeriodStart(
  filter?: PeriodFilter,
  currentStart?: Date,
  currentEnd?: Date,
): Date {
  if (filter && currentStart) {
    switch (filter) {
      case PeriodFilter.last_week:
        return subWeeks(currentStart, 1);
      case PeriodFilter.last_month:
        return subMonths(currentStart, 1);
      case PeriodFilter.last_3_months:
        return subMonths(currentStart, 3);
      case PeriodFilter.last_6_months:
        return subMonths(currentStart, 6);
      case PeriodFilter.last_year:
        return subYears(currentStart, 1);
      case PeriodFilter.all_time:
        return BEGINNING_OF_TIME;
      default:
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          'Invalid period filter',
        );
    }
  }

  if (currentStart && currentEnd) {
    const durationMs = currentEnd.getTime() - currentStart.getTime();
    return new Date(currentStart.getTime() - durationMs);
  }

  throw new ApplicationError(
    StatusCodes.BAD_REQUEST,
    'Invalid parameters for previous period start',
  );
}

export function Period(period: PeriodFilter): { from: Date; to: Date } {
  const now = new Date();
  let from: Date;

  switch (period) {
    case PeriodFilter.last_week:
      from = subWeeks(now, 1);
      break;
    case PeriodFilter.last_month:
      from = subMonths(now, 1);
      break;
    case PeriodFilter.last_3_months:
      from = subMonths(now, 3);
      break;
    case PeriodFilter.last_6_months:
      from = subMonths(now, 6);
      break;
    case PeriodFilter.last_year:
      from = subYears(now, 1);
      break;
    default:
      from = BEGINNING_OF_TIME;
  }

  return {
    from,
    to: now,
  };
}

export function PeriodLength(period: PeriodFilter): number {
  const current = new Date();
  switch (period) {
    case PeriodFilter.last_week: {
      const oneWeekAgo = subWeeks(current, 1);
      return differenceInCalendarDays(current, oneWeekAgo);
    }
    case PeriodFilter.last_month: {
      const oneMonthAgo = subMonths(current, 1);
      return differenceInCalendarDays(current, oneMonthAgo);
    }
    case PeriodFilter.last_3_months: {
      const threeMonthsAgo = subMonths(current, 3);
      return differenceInCalendarMonths(current, threeMonthsAgo);
    }
    case PeriodFilter.last_year: {
      const oneYearAgo = subYears(current, 1);
      return differenceInCalendarMonths(current, oneYearAgo);
    }
    case PeriodFilter.last_6_months: {
      const sixMonthsAgo = subMonths(current, 6);
      return differenceInCalendarMonths(current, sixMonthsAgo);
    }
    case PeriodFilter.all_time: {
      const earliestDate = BEGINNING_OF_TIME;
      return differenceInCalendarDays(current, earliestDate);
    }
    default: {
      throw new ApplicationError(
        StatusCodes.BAD_REQUEST,
        'Invalid period filter',
      );
    }
  }
}

export function buildDateRange(
  from: Date,
  to: Date,
): { from: Date; to: Date; label: string }[] {
  const totalDays = differenceInCalendarDays(to, from);
  const isMonthly = totalDays > 60;

  const buckets: { from: Date; to: Date; label: string }[] = [];

  if (isMonthly) {
    const totalMonths = differenceInCalendarMonths(to, from);
    for (let i = 0; i <= totalMonths; i++) {
      const start = startOfMonth(addMonths(from, i));
      const end = addDays(endOfMonth(start), 1);
      const label = format(start, 'yyyy MMM');
      buckets.push({ from: start, to: end, label });
    }
  } else {
    for (let i = 0; i <= totalDays; i++) {
      const start = startOfDay(addDays(from, i));
      const end = addDays(start, 1);
      const label = format(start, 'yyyy MMM d');
      buckets.push({ from: start, to: end, label });
    }
  }

  return buckets;
}
