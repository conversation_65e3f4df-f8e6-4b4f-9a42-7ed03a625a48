import { env } from '@app/config/env';
import { DURATION } from '@app/internal/enums';
import { HttpClient } from '@app/internal/http/client';
import { MODULE_TOKENS } from '@app/internal/ioc/tokens';
import { Logger } from '@app/internal/logger';
import { RedisStore } from '@app/internal/redis/store';
import { inject } from 'inversify';

export type VibrateResponse<T = any> = {
  api_version: string;
  data: T;
};

export interface PaginatedVibrateResponse<T = any> extends VibrateResponse<T> {
  pagination: {
    total: number;
    current: number;
    limit: number;
    offset: number;
  };
}

export interface Vibrate {
  channels(): Promise<VibrateResponse<any>>;

  getArtistByChannel(
    params: GetArtistByChannelParams,
  ): Promise<VibrateResponse>;

  getFanBaseData(params: GetFanBaseParams): Promise<VibrateResponse>;

  getStreamData(params: GetStreamDataParams): Promise<VibrateResponse>;

  getFanBaseDemographic(
    params: GetFanDemographicsParams,
  ): Promise<VibrateResponse>;

  getTopPlaylists(
    params: GetTopPlaylistParams,
  ): Promise<PaginatedVibrateResponse>;

  getSocialEngagement(
    params: GetSocialEngagementParams,
  ): Promise<VibrateResponse>;

  getActivePlaylist(params: GetActivePlaylistParams): Promise<VibrateResponse>;

  getPlaylistReach(params: GetPlaylistReachParams): Promise<VibrateResponse>;

  getTopTracks(params: GetTopTracksParams): Promise<VibrateResponse>;

  getArtistLinks(uuid: string): Promise<VibrateResponse>;

  getFanbaseRank(uuid: string): Promise<VibrateResponse>;

  getSpotifyListeners({
    uuid,
    query,
  }: GetSpotifyListenersParams): Promise<VibrateResponse>;

  getMetricsInLocation({
    uuid,
    platform,
  }: GetMetricsInLocatonParams): Promise<VibrateResponse>;

  getTopPlaylistTracks({
    uuid,
    platform,
    query,
  }: GetTopPlaylistedTracksParams): Promise<Vibrate>;

  getVideos({ uuid, query }: GetVideosParams): Promise<VibrateResponse>;
}

export enum VibrateChannels {
  SPOTIFY = 'spotify',
  YOUTUBE = 'youtube',
  DEEZER = 'deezer',
  SOUNDCLOUD = 'soundcloud',
  AIRPLAY = 'airplay',
  APPLE = 'apple',
  SHAZAM = 'shazam',
  FACEBOOK = 'facebook',
  INSTAGRAM = 'instagram',
  TWITTER = 'twitter',
  TIKTOK = 'tiktok',
}

export enum MusicChannels {
  SPOTIFY = 'spotify',
  YOUTUBE = 'youtube',
  DEEZER = 'deezer',
  SOUNDCLOUD = 'soundcloud',
  AIRPLAY = 'airplay',
}

export enum EngagementChannels {
  SPOTIFY = MusicChannels.SPOTIFY,
  YOUTUBE = MusicChannels.YOUTUBE,
  DEEZER = MusicChannels.DEEZER,
  SOUNDCLOUD = MusicChannels.SOUNDCLOUD,
  AIRPLAY = MusicChannels.AIRPLAY,
  SHAZAM = 'shazam',
  INSTAGRAM = 'instagram',
  TWITTER = 'twitter',
  TIKTOK = 'tiktok',
}

export enum ChannelStreamPath {
  SPOTIFY = 'streams-historical',
  YOUTUBE = 'views-historical',
  SHAZAM = 'shazams-historical',
  SOUNDCLOUD = 'plays-historical',
  AIRPLAY = 'spins-historical',
  INSTAGRAM = 'likes-historical',
  TWITTER = 'likes-historical',
  TIKTOK = 'likes-historical',
}

export type GetArtistByChannelParams = {
  channel: VibrateChannels;
  linkId: string;
};

export type GetFanBaseParams = {
  channel: VibrateChannels;
  uuid: string;
  query?: Record<'from' | 'to', string>;
};

export type GetSpotifyListenersParams = {
  uuid: string;
  query?: Record<'from' | 'to', string>;
};

export type GetStreamDataParams = {
  channel: MusicChannels | EngagementChannels;
  uuid: string;
  channelStreamPath: ChannelStreamPath;
  query?: Record<'from' | 'to', Date>;
};

export type GetFanDemographicsParams = {
  uuid: string;
  platform: string;
  query?: Record<'from' | 'to', Date>;
};

export type GetTopPlaylistParams = {
  uuid: string;
  platform: string;
  query?: Partial<{
    sort: 'position' | 'date_added';
    order: 'asc' | 'desc';
    limit: number;
    offset: number;
  }>;
};

export type GetSocialEngagementParams = GetFanDemographicsParams;

export type GetActivePlaylistParams = GetSocialEngagementParams;

export type GetPlaylistReachParams = GetActivePlaylistParams;

export type GetTopTracksParams = GetPlaylistReachParams;

export type GetMetricsInLocatonParams = Pick<
  GetFanDemographicsParams,
  'uuid' | 'platform'
>;

export enum ChannelStreamLocationsPath {
  AIRPLAY = 'spins-by-country',
  SPOTIFY = 'listeners-by-location',
  YOUTUBE = 'views-by-location',
}

export type GetVideosParams = {
  uuid: string;
  platform: VibrateChannels.YOUTUBE;
  query?: Partial<{
    sort: 'views' | 'release_date';
    timeframe: 'all' | '1w' | '1m' | '12m';
    order: 'asc' | 'desc';
    limit: number;
    offset: number;
  }>;
};

export type GetTopPlaylistedTracksParams = {
  uuid: string;
  platform: VibrateChannels.SPOTIFY;
  query?: Partial<{
    sort: 'active_playlists' | 'playlist_reach' | 'release_date';
    order: 'asc' | 'desc';
    limit: number;
    offset: number;
  }>;
};

export class VibrateClient extends HttpClient implements Vibrate {
  private endpoints = {
    channelList: '/channel/list',
    getArtistByChannel: ({ channel, linkId }: GetArtistByChannelParams) =>
      `/artist/by-channel/${channel}/${linkId}`,
    getFanBase: ({ channel, uuid, query }: GetFanBaseParams) => {
      return query
        ? `/artist/${uuid}/${channel}/fanbase-historical?date-from=${query.from}&date-to=${query.to}`
        : `/artist/${uuid}/${channel}/fanbase-historical`;
    },
    getSpotifyListeners: ({ uuid, query }: GetSpotifyListenersParams) =>
      `/artist/${uuid}/spotify/listeners-historical?date-from=${query.from}&date-to=${query.to}`,
    getStreamData: ({
      channel,
      uuid,
      channelStreamPath,
      query,
    }: GetStreamDataParams) => {
      return query
        ? `/artist/${uuid}/${channel}/${channelStreamPath}?date-from=${
            query.from?.toISOString()?.split('T')[0]
          }&date-to=${query.to?.toISOString()?.split('T')[0]}`
        : `/artist/${uuid}/${channel}/${channelStreamPath}`;
    },

    getFanDemographic: ({ uuid, platform }: GetFanDemographicsParams) => {
      let path = 'audience';

      switch (platform) {
        case 'spotify':
          path = 'listeners-by-city-historical';
          break;
        case 'youtube':
          path = 'views-by-location';
          break;

        default:
          break;
      }

      return `/artist/${uuid}/${platform}/${path}`;
    },

    getTopPlaylists: ({ uuid, platform, query }: GetTopPlaylistParams) => {
      const queryParams = [];

      if (query != null) {
        for (const key in query) {
          queryParams.join(`${key}=${query[key]}`);
        }
      }

      return query
        ? `/artist/${uuid}/${platform}/playlists?${queryParams.join('&')}`
        : `/artist/${uuid}/${platform}/playlists`;
    },

    getSocialEngagement: ({
      uuid,
      platform,
      query,
    }: GetSocialEngagementParams) => {
      let path = 'artist';

      if (platform === 'youtube') {
        path = 'track';
      }

      return query
        ? `/${path}/${uuid}/${platform}/likes-historical?date-from=${
            query.from?.toISOString()?.split('T')[0]
          }&date-to=${query.to?.toISOString()?.split('T')[0]}`
        : `/${path}/${uuid}/${platform}/likes-historical`;
    },

    getActivePlaylist: ({ uuid, platform }: GetActivePlaylistParams) =>
      `/artist/${uuid}/${platform}/playlisted-tracks`,

    getPlaylistReach: ({ uuid, platform }: GetPlaylistReachParams) =>
      `/artist/${uuid}/${platform}/playlist-reach-historical`,

    getTopTracks: ({ uuid, platform }: GetTopPlaylistParams) => {
      const path = platform === 'youtube' ? 'videos' : 'tracks';

      return `/artist/${uuid}/${platform}/${path}`;
    },

    getArtistLinks: (uuid: string) => `/artist/${uuid}/links`,

    getFanbaseRank: (uuid: string) =>
      `/artist/${uuid}/viberate/chart/top-positions`,

    getMetricsInLocation: ({ uuid, platform }: GetMetricsInLocatonParams) => {
      let path = '';

      switch (platform) {
        case VibrateChannels.AIRPLAY:
          path = ChannelStreamLocationsPath.AIRPLAY;
          break;
        case VibrateChannels.SPOTIFY:
          path = ChannelStreamLocationsPath.SPOTIFY;
          break;
        case VibrateChannels.YOUTUBE:
          path = ChannelStreamLocationsPath.YOUTUBE;
      }

      return `/artist/${uuid}/${platform}/${path}`;
    },

    getTopPlaylistedTracks: ({
      uuid,
      query,
      platform,
    }: GetTopPlaylistedTracksParams) => {
      return query != null
        ? `https://data.viberate.com/api/v1/artist/${uuid}/${platform}/playlisted-tracks`
        : `/artist/${uuid}/${platform}/playlisted-tracks?sort=${query.sort}&order=${query.order}&limit=${query.limit}&offset=${query.offset}
        `;
    },

    getVideos: ({ uuid, platform, query }: GetVideosParams) => {
      return query != null
        ? `/artist/${uuid}/${platform}/videos`
        : `/artist/${uuid}/${platform}/videos?sort=${query.sort}&order=${query.order}&timeframe=${query.timeframe}&limit=${query.limit}&offset=${query.offset}`;
    },
  };

  private async makeMemoizedGetQuery(
    methodName: string,
    url: string,
    ttl = 1 * DURATION.WEEK,
  ) {
    const cacheKey = `${VibrateClient.name}:${methodName}:${url}`;

    const cachedResponseData = await this.redisStore.get(cacheKey);

    if (cachedResponseData != null) {
      return cachedResponseData;
    }

    const res = await this.get(url);

    await this.redisStore.set(cacheKey, res.data, ttl);

    return res.data;
  }

  constructor(
    @inject(MODULE_TOKENS.Logger) protected readonly logger: Logger,
    @inject(MODULE_TOKENS.RedisStore) private readonly redisStore: RedisStore,
  ) {
    super({
      baseURL: env.vibrate_base_url,
      headers: {
        Accept: 'application/json',
        'Access-Key': env.vibrate_api_token,
        'Content-Type': 'application/json',
      },
    });
  }

  public async channels() {
    const url = this.endpoints.channelList;

    const res = await this.get(url);

    return res.data;
  }

  public async getArtistByChannel({
    channel,
    linkId,
  }: GetArtistByChannelParams) {
    const url = this.endpoints.getArtistByChannel({ channel, linkId });

    return this.makeMemoizedGetQuery(this.getArtistByChannel.name, url);
  }

  public async getFanBaseData({ channel, uuid, query }: GetFanBaseParams) {
    const url = this.endpoints.getFanBase({ channel, uuid, query });

    return this.makeMemoizedGetQuery(this.getFanBaseData.name, url);
  }

  public async getSpotifyListeners({ uuid, query }: GetSpotifyListenersParams) {
    const url = this.endpoints.getSpotifyListeners({ uuid, query });

    return this.makeMemoizedGetQuery(this.getFanBaseData.name, url);
  }

  public async getStreamData({
    channel,
    uuid,
    channelStreamPath,
    query,
  }: GetStreamDataParams) {
    const url = this.endpoints.getStreamData({
      channel,
      uuid,
      channelStreamPath,
      query,
    });

    return this.makeMemoizedGetQuery(this.getStreamData.name, url);
  }

  public async getFanBaseDemographic({
    uuid,
    platform,
  }: GetFanDemographicsParams) {
    const url = this.endpoints.getFanDemographic({
      uuid,
      platform,
    });

    return this.makeMemoizedGetQuery(this.getFanBaseDemographic.name, url);
  }

  public async getTopPlaylists({
    uuid,
    platform,
    query,
  }: GetTopPlaylistParams) {
    const url = this.endpoints.getTopPlaylists({
      uuid,
      platform,
      query,
    });

    return this.makeMemoizedGetQuery(this.getTopPlaylists.name, url);
  }

  public async getSocialEngagement({
    uuid,
    platform,
    query,
  }: GetSocialEngagementParams) {
    const url = this.endpoints.getSocialEngagement({ uuid, platform, query });

    return this.makeMemoizedGetQuery(this.getSocialEngagement.name, url);
  }

  public async getActivePlaylist({ uuid, platform }: GetActivePlaylistParams) {
    const url = this.endpoints.getActivePlaylist({ uuid, platform });

    return this.makeMemoizedGetQuery(this.getActivePlaylist.name, url);
  }

  public async getPlaylistReach({ uuid, platform }: GetPlaylistReachParams) {
    const url = this.endpoints.getPlaylistReach({ uuid, platform });

    return this.makeMemoizedGetQuery(this.getPlaylistReach.name, url);
  }

  public async getTopTracks({ uuid, platform }: GetTopTracksParams) {
    const url = this.endpoints.getTopPlaylists({ uuid, platform });

    return this.makeMemoizedGetQuery(this.getTopTracks.name, url);
  }

  public async getArtistLinks(uuid: string) {
    const url = this.endpoints.getArtistLinks(uuid);

    return this.makeMemoizedGetQuery(this.getArtistLinks.name, url);
  }

  public async getFanbaseRank(uuid: string) {
    const url = this.endpoints.getFanbaseRank(uuid);

    return this.makeMemoizedGetQuery(this.getFanbaseRank.name, url);
  }

  public async getMetricsInLocation({
    uuid,
    platform,
  }: GetMetricsInLocatonParams) {
    const url = this.endpoints.getMetricsInLocation({ uuid, platform });

    return this.makeMemoizedGetQuery(this.getMetricsInLocation.name, url);
  }

  public async getTopPlaylistTracks({
    uuid,
    platform,
    query,
  }: GetTopPlaylistedTracksParams) {
    const url = this.endpoints.getTopPlaylistedTracks({
      uuid,
      platform,
      query,
    });

    return this.makeMemoizedGetQuery(this.getTopPlaylistTracks.name, url);
  }

  public async getVideos({ uuid, platform, query }: GetVideosParams) {
    const url = this.endpoints.getVideos({ uuid, platform, query });

    return this.makeMemoizedGetQuery(this.getVideos.name, url);
  }
}
