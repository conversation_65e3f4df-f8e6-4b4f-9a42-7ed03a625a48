import { HttpClient } from '@app/internal/http/client';
import { MODULE_TOKENS } from '@app/internal/ioc/tokens';
import { inject, injectable } from 'inversify';
import { SocialOauth } from '../facebook-oauth';
import { Logger } from '@app/internal/logger';
import { env } from '@app/config/env';

@injectable()
export class TikTokOauthClient extends HttpClient implements SocialOauth {
  private readonly endpoints = {
    auth: '/oauth/authorize/',
    token: '/oauth/access_token/',
    userInfo: '/user/info/',
  };

  constructor(@inject(MODULE_TOKENS.Logger) protected readonly logger: Logger) {
    super({ baseURL: 'https://open.tiktokapis.com/v2' });
  }

  getAuthUrl(redirectUri: string) {
    const params = new URLSearchParams({
      client_key: env.tiktok_client_key,
      redirect_uri: redirectUri,
      response_type: 'code',
      scope: 'user.info.basic',
    });
    return `https://open.tiktokapis.com/v2${this.endpoints.auth}?${params}`;
  }

  public async exchangeCode(code: string, redirectUri: string) {
    const res = await this.post(this.endpoints.token, {
      client_key: env.tiktok_client_key,
      client_secret: env.tiktok_client_secret,
      code,
      grant_type: 'authorization_code',
      redirect_uri: redirectUri,
    });
    return { access_token: res.data.data.access_token };
  }

  public async getUserInfo(accessToken: string) {
    const res = await this.get(this.endpoints.userInfo, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'X-Tt-Client-Key': env.tiktok_client_key,
      },
      params: { fields: 'open_id,union_id' },
    });
    return res.data.data.user;
  }
}
