import { PartialExcept } from '@app/internal/types';
import { AxiosRequestConfig } from 'axios';
import PartialInstantiable from '@app/utils/partial-instantiable';

export type SignUpPayload = {
  email: string;
  password: string;
  enterpriseName: string;
  type: 'Launch' | 'Growth';
  partnerUserId: string;
  statementEmailAddress?: string;
  paymentSettings?: {
    providerId: string;
    userId: string;
  };
  firstname?: string;
  lastname?: string;
  maxArtists?: number;
  storeAccess?: {
    type: string;
    storeIds: number[];
  };
};

export type PartnerSignupResponse = {
  userId: string;
  enterpriseId: number;
};

export type PartnerLoginResponse = {
  accessToken: string;
  permissions: {
    enterpriseId: number;
    enterpriseName: string;
    labelId: any;
    publisherId: any;
    artistId: any;
    payeeId: any;
    imageId: any;
    name: string;
    permissionsAccountId: string;
    isOwner: boolean;
    readOnlyContent: boolean;
    readOnlyContracts: boolean;
    readOnlyFinance: boolean;
    readOnlyDistribution: boolean;
    readOnlyPromote: boolean;
    readOnlyDaily: boolean;
    permissionRolesId: number;
    isActive: boolean;
    isDefault: boolean;
    email: string;
    accountType: number;
  }[];
  isAuthorized: boolean;
};

export type AuthRequestArgs<T> = {
  partnerUserId: string;
} & PartialExcept<AxiosRequestConfig<T>, 'url' | 'method'>;

export type StoreListResponse = {
  distributorStoreId: number;
  name: string;
  isActive: boolean;
  shortDescription: any;
  display: boolean;
  supportRingtones: boolean;
  supportVideos: boolean;
  isNew: any;
  deliveryLeadTimeDays: number;
  takedownLeadTimeDays: number;
}[];

export type DistributorListResponse = {
  distributorId: number;
  distributorName: string;
  distributorStoreId: number;
  distributorStoreName: string;
  supportsTrends: boolean;
}[];

export type LanguageListResponse = {
  languageId: number;
  name: string;
  languageCode: string;
  isValidMetadataLanguage: boolean;
}[];

export type CountryListResponse = {
  isO2Code: string;
  code: string;
  name: string;
  countryId: number;
}[];

export type ContributorRoleListResponse = {
  roleId: number;
  name: string;
  priority: number;
}[];

export type ContributorRoleGroupListResponse = {
  contributorRoleGroupId: number;
  name: string;
}[];

export type ContactRoleListResponse = {
  contactRoleId: number;
  name: string;
}[];

export type TimezoneListResponse = {
  text: string;
  timezoneId: number;
}[];

export type TrackPropertyListResponse = {
  trackPropertyId: number;
  name: string;
}[];

export type GenreListResponse = {
  musicStyleId: number;
  name: string;
  parentId: any;
  order: number;
}[];

export type MonetizationPolicyListResponse = {
  distributorStoreId: number;
  policyId: number;
  name: string;
  orderNum: number;
}[];

export enum DistributorStoreId {
  APPLE_MUSIC = 1,
  SPOTIFY = 9,
}

export type SaveArtistPayload = {
  name?: string;
  artistId?: number;
  image?: {
    fileId: string;
    filename: string;
  };
  artistExternalIds?: {
    distributorStoreId: DistributorStoreId;
    profileId: string; //"934023503"
  }[];
  artistLocals?: {
    name: string;
    languageId: number;
  }[];
};

export type SaveArtistResponse = {
  labelId: any;
  labelName: string;
  contactId: any;
  contact: any;
  biography: any;
  yearsActive: any[];
  influencers: any[];
  contemporaries: any[];
  tags: any[];
  isSigned: false;
  musicStyles: any[];
  artistExternalIds: {
    profileId: string;
    distributorStoreId: number;
  }[];
  socialUrls: any[];
  artistLocals: {
    artistId: number;
    name: string;
    languageId: number;
  }[];
  artistsWebsites: any[];
  webAlias: any;
  enterpriseId: number;
  artistId: number;
  name: string;
  image: {
    fileId: string;
    isTemp: boolean;
    filename: string;
    externalUrl: any;
    lastUpdateDate: string;
  };
};

export type UploadImagePayload = {
  coverImage: boolean;
  file: {
    buffer: Buffer;
    contentType: string;
    name: string;
  };
};

export type UploadTrackAudioPayload =
  | {
      file: {
        buffer: Buffer;
        contentType?: string;
        name?: string;
      };
    }
  | { filename: string; externalUrl: string };

export type UploadTrackAudioResponse = {
  fileId: string;
  filename: string;
};

export type ArtistObject = {
  name?: string;
  artistId?: number;
  image?: {
    fileId: string;
    filename: string;
  };
  artistExternalIds?: {
    distributorStoreId: DistributorStoreId;
    profileId: string;
  }[];
  artistLocals?: {
    name: string;
    languageId: number;
  }[];
};

export class TrackObject extends PartialInstantiable<TrackObject> {
  /**
   * Title of the track.
   * -  Required for new tracks.
   * - When provided with an existing trackId, updates the name of the track.
   * - Do not include additional information, like “Remix” or “Uncut”. This should be specified in the version.
   */
  name?: string;
  /**
   * The ID of an existing track to assign to the release. This ID is returned when the track is created.
   *
   * Note:
   * - Do not use the same trackId for more than one release.
   * - Provide an existing trackId only when editing an existing release or assigning an unassigned track.
   * - If the same track (same ISRC) is used on multiple releases, create the track as new for each additional release (generate a new trackId).
   * - Setting trackId to 0 creates a new track (default behavior).
   *
   * Important:
   * - Patching is not supported. When assigning an existing track to a release, the track will be updated based on the provided data. Any existing values for omitted parameters will be deleted.
   *
   */
  trackId?: number;
  /**
   * Name of the track’s primary artist or band.
   *
   * Guidelines:
   * - Should be only the name of one artist.
   * - The artist named in this parameter will be the only artist named for the release in contracts and analytics.
   * - Additional artists should be named as contributors.
   * - If there is more than one primary artist, additional primary artists should be listed as contributors with the role "Primary Artist".
   *
   * Behavior:
   * - When provided without an `artistId`, it tries to find an existing artist with the same name (case-insensitive). If no such artist is found, it creates a new artist.
   * - When provided with an `artistId`, the `artistName` is ignored. The artist associated with the `artistId` will be assigned to the release, and the name of the artist will not be updated.
   *
   * Requirements:
   * - Either an `artistName` or `artistId` must be provided. If neither are provided, the artist will be null, and the metadata for the track will be invalid.
   * - All artists in child enterprises automatically appear in the parent enterprise.
   */
  artistName?: string;

  /**
   * The ID of an existing artist to assign to the track.
   *
   * Note: If `artistId` is provided, `artistName` is ignored.
   */
  artistId?: number;
  tracksLocals?: {
    languageId: number;
    name: string;
    version: string;
  }[];
  artistLocals?: {
    languageId: number;
    name: string;
  }[];
  contributors?: {
    /**
     * ID for an existing contributor.
     * Not relevant for new releases. Should be provided when editing existing releases.
     */
    contributorId?: string;
    roleId: number;
    /**
     * The contributing artist.
     *
     * Unlike all other artists in Revelator, contributing artists with any role other than Primary Artist, Featuring, or Remixer do not require external artist IDs.
     * In other words, contributing artists with the roles Primary Artist, Featuring, or Remixer require Apple and Spotify IDs (same as primary artists);
     * contributing artists with other roles do not require these IDs (they are optional).
     *
     * see https://api-docs.revelator.com/en/catalog-management/#artist-object
     */
    artist: ArtistObject;
  }[];
  languageId: number;
  version: string;
  /**
   * Be sure that your user interface indicates the importance of properly identifying this content.
   * Misidentifying this content can result in your distribution being disabled.
   *
   * Should be true when the song contains any of the following:
   *
   * - Anything unsuitable for children
   * - Strong language
   * - References to violence or abuse
   * - Sexual content
   * - Anything that might be regarded as racist, homophobic, discriminatory or misogynistic
   * - Anything that encourages or celebrates criminal behavior
   */
  explicit: boolean;
  /**
   * One of the following:
   *
   * - 1 - Original song
   * - 2 - Cover song
   * - 3 - Public domain song
   */
  trackType: 1 | 2 | 3;
  /**
   * One or multiple of the following:
   *
   * - 1 - None (If included, no other value should be provided)
   * - 2 - Remix
   * - 3 - Samples or Stock
   * - 4 - Mix or Compilation
   * - 5 - Alternate version
   * - 6 - Special Genre
   * - 7 - Non-Musical Content
   * - 8 - Includes AI
   */
  trackProperties: (1 | 2 | 3 | 4 | 5 | 6 | 7 | 8)[];
  /**
   * One of the following:
   *
   * - For artists already on Apple Music, existing Apple ID for the artist. To find this ID, go to your Apple Music artist page, and copy-paste the numeric part of the URL. Example 1249595
   *
   * - For artists not already on Apple Music, 0. This indicates we should generate an ID for the artist. The second time you distribute a release for a new artist, you must retrieve the artist ID and provide it in the release.
   */
  artistAppleId?: string;
  /**
   * One of the following:
   *
   * - For artists already on Spotify, existing Apple ID for the artist. To find this ID, go to your Spotify artist page, and copy-paste the numeric part of the URL. Example 22bE4uQ6baNwSHPVcDxLCe
   * - For artists not already on Spotify, 0. This indicates we should generate an ID for the artist. The second time you distribute a release for a new artist, you must retrieve the artist ID and provide it in the release.
   */
  artistSpotifyId?: string;
  /**
   * Audio file for the track in WAV format.
   *
   * - Although the audio file is optional when creating a track, it is required to distribute it.
   * - The audio file must be a stereo WAV file. A minimum bit depth of 16 bit and minimum sample rate of 44.1 kHz are recommended.
   * - In line with the general behavior of this API, patching is not supported.
   * When editing an existing release associated with an existing file, you must include this object (with the existing fileId and fileName) to prevent the file from being deleted.
   */
  wav?: {
    filename: string;
    fileId: string;
  };
  composerContentsDTO: {
    roleId: number;
    rightsId: number;
    composerName?: string;
    publisherName?: string;
    /**
     * ID for an existing publisher to assign to the composer.
     *
     * - Only relevant when rightsId is `2`.
     * - Either a publisherId or a publisherName must be provided (when rightsId is 2). Otherwise, no publisher is specified (and the metadata for the track will be incomplete).
     *
     * *Set the `publisherId` to `0` (along with providing a publisherName) to create a new publisher whenever no existing publisher with the provided name exists. Omitting this parameter will cause the publisher (name and ID) to be null.
     */
    publisherId?: number;
    share: number;
  }[];
  isrc: string;
  lyrics: string;
  enterpriseId: number;
  previewStartSeconds: number;
  copyrightC?: string;
  copyrightP?: string;
  primaryMusicStyleId: number;
  secondaryMusicStyleId: number;
}

export type SaveReleasePayload = {
  /**
   * ID for the existing release you want to edit. This ID was returned in the response when the release was created.
   *
   * - When set to 0, creates a new release. (Default)
   *
   * - Inline with the general behavior of this API, patching is not supported. Existing values for omitted parameters will be deleted or overwritten
   */
  releaseId?: number;
  name: string;
  version?: string;
  artistName?: string;
  artistId?: number;
  contributors: {
    /**
     * Not relevant for new releases. Should be provided when editing existing releases.
     */
    contributorId?: string;
    /**
     * - The “primary artist” role (roleId 49) should only be used to name additional primary artists.
     * The first (or only) primary artist should only be indicated as the main artist with the artistName parameter.
     *
     * - Any role with priority 5 should not be used in the contributors array, except in the case of classical music where the “composer” role (roleId 2) is required.
     * Priority 5 indicates the role is a publishing role, which should be provided provided per track in the composerContentsDTO array.
     *
     * - It is strongly encouraged to use a reduced set of contributor roles, such as those visible in the Revelator web interface.
     * Many users will be tempted to add extraneous roles which will result in the release being rejected by top tier DSPs
     */
    roleId: number;
    /**
     * The contributing artist.
     *
     *
     * Unlike all other artists in Revelator, contributing artists with any role other than Primary Artist, Featuring, or Remixer do not require external artist IDs.
     * In other words, contributing artists with the roles Primary Artist, Featuring, or Remixer require Apple and Spotify IDs (same as primary artists);
     * contributing artists with other roles do not require these IDs (they are optional).
     */
    artist: ArtistObject;
  }[];
  hasRecordLabel?: boolean;
  labelId?: any;
  labelName?: any;
  /**
   * One of the following:
   *
   * - For artists already on Apple Music, existing Apple ID for the artist. To find this ID, go to your Apple Music artist page, and copy-paste the numeric part of the URL. Example 1249595
   *
   * - For artists not already on Apple Music, 0. This indicates we should generate an ID for the artist. The second time you distribute a release for a new artist, you must retrieve the artist ID and provide it in the release.
   */
  artistAppleId?: string;
  /**
   * One of the following:
   *
   * - For artists already on Spotify, existing Apple ID for the artist. To find this ID, go to your Spotify artist page, and copy-paste the numeric part of the URL. Example 22bE4uQ6baNwSHPVcDxLCe
   * - For artists not already on Spotify, 0. This indicates we should generate an ID for the artist. The second time you distribute a release for a new artist, you must retrieve the artist ID and provide it in the release.
   */
  artistSpotifyId?: string;
  previouslyReleased: boolean;
  /**
   * UPC/EAN/JAN code for the release.
   *
   * - The UPC is a unique code that every release must have. The system will automatically generate one upon distribution (free of charge) if you omit this parameter. You should never generate a new UPC for a release that already has one.
   * - Required when `previouslyReleased` is `true`
   */
  upc?: number;
  /**
   * Official date the release was previously released in the format “mm/dd/yyyy”.
   *
   * - Required when previouslyReleased is true; otherwise should not be provided.
   * - When distributing the release with the Revelator system, you should not manually update this parameter; you should only set the saleStartDate parameter in the distribution options endpoint.
   *
   * See https://api-docs.revelator.com/en/distribution#setting-distribution-options
   */
  releaseDate?: string;
  releasesLocals: {
    name: string;
    version: string;
    languageId: number;
  }[];
  image?: {
    filename: string;
    fileId: string;
  };
  primaryMusicStyleId?: number;
  secondaryMusicStyleId?: number;
  languageId: number;
  tracks: TrackObject[];
  /**
   * The product copyright holder preceded by the year the rights were obtained.
   * For example: 2008 Acme Inc. The product copyright holder is the person or entity
   * that owns the exclusive rights to the complete product, including both sound recording and artwork.
   */
  copyrightP: string;
  /**
   * The sound recording copyright holder preceded by the year the rights were obtained. For example: 2008 Acme Inc.
   *
   * The sound recording copyright holder is the person or entity that owns the exclusive rights to the sound recording.
   */
  copyrightC: string;
};

export type RetriveClientInfoResponse = {
  enterpriseId: number;
  payeeId: number;
  name: string;
  creationDate: string;
  infringementTrustScore: any;
  infringementTrustLevelName: any;
  totalReleases: number;
  totalTracks: number;
  notesCount: number;
  isLockedOut: boolean;
};

export type ValidateReleaseResponse = {
  objectId: number;
  objectType: string;
  field: string;
  errorMessage: string;
  value: any;
  severity: 1;
  errorCode: 11051;
}[];

export type SetDistributionOptionsPayload = {
  releaseId: number;
  /**
   * The date the release should go live, in the format “mm/dd/yyyy”.
   *
   * - When omitted, the release will be live as soon as possible.
   * - When provided without saleStartTime and saleStartTimezone, the release will be live on the saleStartDate at midnight in every time zone.
   */
  saleStartDate?: string;
  /**
   * The time the release should go live, in the format “hh:mm”.
   *
   * - When provided, saleStartDate is mandatory.
   * - When provided without saleStartTimezone, the release will go live on the saleStartDate at the saleStartTime in every time zone.
   * - When provided with saleStartTimzone, specifies the absolute time that the release should go live. For example, specifying the release should go live at 2pm in Los Angeles will cause it to go live at 5pm in New York.
   */
  saleStartTime?: string;
  /**
   * The timezone for the saleStartTime. The release will go live at the same instant everywhere.
   *
   * -When provided, saleStartTime is mandatory.
   * -Mutually exclusive with saleStartTimezone. Only provide one or none of these two parameters.
   * - Value should be a valid timezoneId.
   *
   *   To lookup timezones, use the GET `/common/lookup/timezoneIds` resource.
   */
  saleStartTimezoneId?: number;
  /**
   * The timezone offset for the saleStartTime. The release will go live at the same instant everywhere.
   *
   * - When provided, saleStartTime is mandatory.
   * - Mutually exclusive with saleStartTimezoneId. Only provide one or none of these two parameters.
   * - Value should be a signed integer indicating an offset from UTC+0.
   *
   * Unless this behavior specifically needed, we recommend using `saleStartTimezoneId` instead.
   */
  saleStartTimezone?: number;
  /**
   * The date the preorder should become available, in the format “mm/dd’yyyy”
   *
   * - When provided, `saleStartDate` is mandatory.
   * - Must be an earlier date than the `saleStartDate`
   */
  salePreOrderDate?: string;
  /**
   * Release-level monetization policy ID(s) for YouTube Content ID, Facebook Rights Manager and TikTok.
   * Array should include only one policy for each store, or none if you are not distributing to that store.
   *
   * See [Monetization Policies](https://api-docs.revelator.com/en/distribution#monetization-policies) for more information and limitations.
   */
  monetizationPolicyIds: number[];
  priceTierIds?: number[];
  trackMonetizationPolicyIds?: { trackId: string; number: number }[];
  countriesExcluded?: {
    countryId?: number;
    isO2Code?: string;
    code?: string;
    name?: string;
  }[];
  countriesIncluded?: {
    countryId?: number;
  }[];
};

export type SetDistributionOptionsResponse = {
  releaseId: number;
  priceTierIds: any;
  trackDefaultPriceTierId: any;
  saleStartDate: any;
  saleStartTime: any;
  saleStartTimezone: any;
  salePreOrderDate: any;
  countriesExcluded?: {
    countryId?: number;
    isO2Code?: string;
    code?: string;
    name?: string;
  }[];
  countriesIncluded?: {
    countryId?: number;
    isO2Code?: string;
    code?: string;
    name?: string;
  }[];
  isLockedForDistribution: boolean;
  monetizationPolicyIds: number[];
  trackMonetizationPolicyIds?: { trackId: string; number: number }[];
  saleStartTimezoneId: any;
  autoDistroOptIn: any;
  priceTierId: any;
  showReleaseLevelPricing: boolean;
};

export type AddToDistributionQueuePayload = {
  stores: number[];
  releaseId: number;
};

export type LabelImage = {
  fileId: string;
  isTemp?: boolean;
  filename: string;
  externalUrl?: string | null;
  lastUpdateDate?: string;
};

export type LabelSocialUrl = {
  url: string;
  socialNetworkId: number;
};

export type Label = {
  creationDate: string;
  createdBy: string;
  lastUpdatedBy: string | null;
  lastUpdateDate: string | null;
  soundScanAbbreviationCAN: string | null;
  soundScanAbbreviationUSA: string | null;
  isrcCode: string | null;
  contactId: number | null;
  socialUrls: LabelSocialUrl[];
  yearsActive: string[];
  influencers: string[];
  contemporaries: string[];
  tags: string[];
  musicStyles: string[];
  labelsWebsites: string[];
  upcCode: string | null;
  description: string | null;
  contact: any | null;
  enterpriseId: number;
  image: LabelImage | null;
  labelId: number;
  name: string;
};

export type SaveLabelPayload = {
  /** required only when you are editing a pre-existing label */
  labelId?: number;
  image?: {
    externalUrl?: string;
    fileId?: string;
    filename?: string;
  };
  description: string;
  name: string;
};

export type SaveLabelResponse = {
  creationDate: string;
  createdBy: string;
  lastUpdatedBy: any;
  lastUpdateDate: any;
  soundScanAbbreviationCAN: any;
  soundScanAbbreviationUSA: any;
  isrcCode: string;
  contactId: number;
  socialUrls: string[];
  yearsActive: string[];
  influencers: string[];
  contemporaries: string[];
  tags: string[];
  musicStyles: string[];
  labelsWebsites: string[];
  upcCode: any;
  description: any;
  contact: {
    contactId: number;
    name: string;
    currencyCode: any;
    phone: any;
    email: any;
    address: any;
    address2: any;
    zipcode: any;
    countryId: any;
    imageId: any;
    image: any;
    isActive: boolean;
    labelId: any;
    publisherId: any;
    artistId: any;
    city: any;
    state: any;
    location: any;
  };
  enterpriseId: number;
  image: LabelImage;
  labelId: number;
  name: string;
};

export type TrackSplits = {
  trackId: number;
  trackTitle: string;
  trackTitleVersion: string | null;
  isrc: string | null;
  trackLength: number;
  artist: {
    artistId: number;
    name: string;
  };
  splits: {
    payeeId: number;
    trackId: number;
    payeeEmail: string;
    payeeName: string;
    percentShare: number;
    isChildEnterprisePayee: boolean;
  }[];
}[];

export type SaveTrackSplitPayload = {
  /** Indicates if it's split should go the child's enterprise accounts itself */
  isChildEnterprisePayee: boolean;
  payeeName?: string;
  payeeEmail?: string;
  percentShare: number;
  trackId: number;
}[];

export type SaveContractPayload = Partial<{
  name: string;
  /** required only when editing pre-existing contract */
  contractId?: number;
  /** `1` - specifies a Distribution Agreement. */
  contractTypeId: number;
  /** Date from which the contract is effective, in the format YYYY-MM-DD. This date is for reference only is not enforced when calculating royalties.   */
  startDate: string;
  /** Date the contract becomes ineffective, in the format YYYY-MM-DD. If your contract is “in perpetuity”, please indicate 2099-12-31 */
  expirationDate: string;
  isActive?: boolean;
  /**
   * Each object represents a payee and details their royalty information.
   * The sum of every non-comission payee’s `sharePercentage` must equal `100`.
   */
  contractPayees: Partial<{
    sharePercentage: number;
    startingBalance: number;
    isCommissionPayee: boolean;
    payee: Partial<{
      /**
       * ID for an existing payee to associate with the contract.
       *
       * - When set to 0, creates a new payee. (Default)
       *
       *  A default payee (and default contract) is automatically created upon account creation.
       *
       * you should never create a new payee for a user for whom you have already created a child account.
       *
       * */
      payeeId?: number;
      /** Name of the payee. When omitted, defaults to the name of the contact. */
      companyName: string;
      /** Contact information for the payee */
      contact: Partial<{
        /** ID for an existing contact.
         * - When set to 0, creates a new contact **(Default)**
         * */
        contactId?: number;
        /** Name of the contact (This Defaults to the `companyName`) */
        name?: string;
        currencyCode: string;
        /** Contact’s email address.
         * This email is used for sending statement notifications when automatic email notifications are enabled.
         * */
        email: string;
      }>;
      /**
       * Currency code for the payee’s currency. Defaults to the parent account’s currency code.
       *
       *  Look up currency codes using the GET `/common/lookup/currencies` [resource](https://api-docs.revelator.com/en/getting-started#common-resources).
       */
      currencyCode?: string;
      /**
       * `2` - Paypal
       * Only necessary when using the Paypal integration for paying the payee.
       */
      paymentProviderId?: number;
      /**
       * Paypal email address for the payee.
       * Only necessary when using the Paypal integration for paying the payee.
       */
      paymentUserId?: string;
    }>;
    /** The permissions the payee has in their payee portal.
     This parameter sets permissions for the payee to log into the Revelator UI. It is not relevant for most API use cases, and is never relevant when the payee already has a user/child account that was created with the signup resource. */
    permission?: Partial<{
      enterpriseId: number;
      enterpriseName: string;
      labelId: number;
      publisherId: number;
      artistId: number;
      payeeId: number;
      imageId: string;
      name: string;
      permissionsAccountId: string;
      isOwner: boolean;
      readOnlyContent: boolean;
      readOnlyContracts: boolean;
      readOnlyFinance: boolean;
      readOnlyDistribution: boolean;
      readOnlyPromote: boolean;
      readOnlyDaily: boolean;
      permissionRolesId: number;
      isActive: boolean;
      email: string;
    }>;
  }>[];
  /**
   * Object represents a licensor (artist or label) and provides information for which of the licensor’s assets should be subject to the contract.
   *
   * *Although `contractLicensors` is an array, the array should include only one object. For additional licensors, please create a separate contract.
   */
  contractLicensors: Partial<{
    /**
     * ID for an existing contract licensor entity.
     * Not relevant for new contracts. Should be provided when editing existing releases.
     */
    id: string;
    /**
     * Type of licensor that will be used to specify the assets to include in the contract.
     * `1` - artist*
     * `2` - label
     *
     * For artist licensors, you can only specify assets where the artist is the primary artist on the release level.
     * Therefore, compilations cannot be specified for artist licensors. Additionally, when `includeFutureAssets` is true,
     * only the primary artist on the asset is considered; contributors are ignored, including contributors with the role “primary”.
     */
    licensorType: number;
    /**
     * ID of the licensor. Either an `artistId` or `labelId`, according to the `licensorType`
     */
    licensorId: number;
    /**
     * - `true` - to include all of the licensor’s current and future assets.
     * When this is true for a licensor, none of the licensor’s assets should
     * be included in the `contractReleases` or `contractTracks` arrays.
     *
     * - `false` - to not include all of the licensor’s current and future assets **(Default)**
     */
    includeFutureAssets: boolean;
  }>[];
  /**
   * Specifies which releases should be included in the contract when only some of the licensors assets are included in the contract.
   * Each object in the array includes a release ID: `{"releaseid": <integer>}`
   *
   * - When all of the licensors assets are included in the contract (includeFutureAssets is true) this parameter is not relevant.
   *
   *  - When a release is included in this array, every track in the release will automatically be subject to the contract. The release’s individual tracks should not additionally be included in the contractTracks array.
   */
  contractReleases: {
    releaseId: number;
  }[];
  /**
   * Specifies which tracks should be included in the contract when only some of the licensors assets are included in the contract,
   * and only when some of the tracks on releases are included in the contract.
   * Each object in the array includes a track ID: `{"trackid": <integer>}`
   *
   * - When all of the licensor’s assets are included in the contract (includeFutureAssets is true) or only complete releases are included in the contract, this parameter is not relevant.
   */
  contractTracks: {
    trackId: number;
  }[];
  contractTerms: Partial<{
    contractTermsId?: string;
    contractTermsRate?: number;
    contractTermsRateTypeId: number;
    isCountriesIncluded?: boolean;
    isDistributorStoresIncluded?: boolean;
    countries: number[];
    /**
     * IDs for the services included or excluded from the contract. See `isDistributorStoresIncluded`.
     *
     * Look up DSP IDs using the GET /common/lookup/stores [resource](https://api-docs.revelator.com/en/getting-started#common-resources).. Provide an access token to retrieve only the DSPs enabled for a specific account.
     */
    distributorStores?: number[];
    /**
     * (1 | 2 | 4)[]
     * Types of releases to include in the contract terms:
     * `1`  - Album
     * `2` - Single
     * `4` - EP
     *
     * An empty array will cause all types to be included for the contract. (Default)
     */
    releaseTypes?: number[]; //
    /**
     *
     *       (| 101
     *       | 102
     *       | 103
     *       | 104
     *       | 105
     *       | 106
     *       | 107
     *       | 109
     *       | 110
     *       | 115
     *       | 116
     *       | 117
     *       | 118
     *       | 123
     *       | 120)[]
     * Delivery channels to include in the contract terms. Each integer in the array is an ID that represents a delivery type; each channel includes multiple delivery types.
     *
     * - Download: `101`, `106`, `123`
     *
     * - Subscription: `102`, `104`, `105`, `107`
     *
     * - Ad supported: `103`, `115`, `109`, `118`
     *
     * - Physical: `110`
     *
     * - Rental: `116`
     *
     * - Digital Licensing: `117`
     *
     * - UGC: `120`
     *
     * An empty array will cause all types to be included for the contract. (Default)
     */
    deliveryTypes: number[];
  }>[];

  /**
   * - `true` - to pay digital mechanicals. Additional attributes are required.
   *
   * - `false` - to not pay digital mechanicals **(Default)**
   */
  payMechanicals?: boolean;
  /**
   * Rate type for mechanicals:
   * - `1` - % of Sales
   *
   * - `2` - Penny Rate
   *
   *  - `3` - Copyright Law
   *
   * Mandatory when `payMechanicals` is true.
   */
  mechanicalsTypeId?: number;
  /**
   * Sales percent (Mandatory when `mechanicalsTypeId` is `1`.)
   */
  mechanicalsSalesPercentage?: number;
  /**
   * Penny rate (Mandatory when `mechanicalsTypeId` is 2.)
   */
  mechanicalsFixedPennyRate?: number;
  /**
   * Rate basis:
   * - `true` - Full
   *
   * - `false` - Minimum
   *
   * Mandatory when `mechanicalsTypeId` is 3.
   */
  mechanicalsIsFullRate?: boolean;
  /**
   * Rate percent (Mandatory when `mechanicalsTypeId` is 3)
   */
  mechanicalsRatePercentage?: number;
}>;

export enum DspStatus {
  /** Metadata for the release was rejected (in parent account) */
  REJECTED_BY_INSPECTOR = -13,
  /** Release hidden from metadata inspection queue (put aside to inspect later) */
  PARKED = -11,
  /** Pending metadata inspection (Before distribution is approved in the parent account) */
  PENDING_APPROVAL = -10,
  /** Initiating distribution (Immediately after metadata approval in the parent account) */
  PENDING = 0,
  /** Waiting to download files hosted at external URLs from the cloud */
  WAITING_FOR_DOWNLOAD_ASSETS = 4,
  /** Downloading files from the cloud */
  DOWNLOADING_EXTERNAL_ASSETS = 5,
  /** Downloading a file failed. Please verify the provided external URL is public and triggers a download of the file (server hosting the file must return a content-disposition header set to “attachment”) */
  DOWNLOADING_EXTERNAL_ASSETS_FAILED = 6,

  PENDING_TRANSFORMATION = 8,

  TRANSFORMATION_IN_PROGRESS = 10,
  /** Encoding audio failed. Re-queueing the release may fix the problem; if not, please contact customer support. */
  TRANSFORMATION_FAILED = 11,
  /** Waiting for metadata validation */
  WAITING_FOR_VALIDATION = 20,
  /**
   * Release has failed the DSP’s unique metadata validation; see the errorMessage for more information.
   *
   * - These validation issues must be fixed by editing the release and re-adding it to the distribution queue. In order to edit parameters that cannot be edited for locked releases, you must unlock, edit, relock, and then add to queue.
   * For more information about which fields can and cannot be edited for locked releases, see Updating Distributed Releases.
   *
   * - If validation failed because of a missing UPC/ISRC and the release’s metadata indicates that Revelator should generate the UPC/ISRC, please contact customer support.
   *
   *
   * Sometimes YouTube will indicate an error that does not require a fix; it is simply a warning/alert.
   *
   *
   * */
  VALIDATION_FAILED = 21,
  /** Waiting for package creation */
  WAITING_FOR_PACKAGE_CREATION = 30,

  CREATING_PACKAGE = 31,
  /**
   * The majority of the time, this indicates a temporary error that will resolve itself; this is due to the system optimizing for speed of delivery vs. 100% delivery on the first try. See the errorMessage for more information.
   *
   * - “The process cannot access the file” - Error will self-resolve in 24 hours or less
   *
   * - “Sequence contains no matching element” - Typically an error when the DSP is YouTube CID (307) or Facebook RM (310), and it means that a monetization policy is missing for at least 1 track.
   *
   *  - “Object reference not set to an instance of an object” - There are many causes for this error; in general it indicates that one of the expected references is missing, like the Artist ID for one of the contributors.
   *
   * */
  CREATING_PACKAGE_FAILED = 32,
  /** Waiting for upload */
  WAITING_FOR_UPLOAD = 40,

  UPLOADING = 41,
  /** Uploading the package failed. Re-queueing the release may fix the problem; if not, please contact customer support. */
  UPLOADING_FAILED = 42,
  /** Uploaded/delivered to the DSP */
  DELIVERED = 50,
  /** Confirmed as available on the DSP */
  ON_STORE = 60,

  WAITING_FOR_REMOVE = 70,
  /** Removing from DSP */
  REMOVING = 71,

  TAKEDOWN_UPLOADING = 72,

  TAKEDOWN_WAITING_FOR_REVELATOR_NETCORE = 75,

  TAKEDOWN_ENQUEUED_BY_REVELATOR_NETCORE = 76,
  /** Taking down the release failed. Re-queueing the takedown may fix the problem; if not, please contact customer support.*/
  REMOVING_FAILED = 77,

  REMOVE_DELIVERED = 78,
  /** Takedown confirmed by DSP */
  TAKEDOWN_PROCESSED = 78,
  /** Processing error */
  PROCESS_ERROR = 100,
}

export type GetDistributionStatusParams = Partial<{
  releaseId: number;
  /** The status of the releases for which to get distribution information. Please see the Query String Text in Distribution Statuses. Including this parameter narrows the response to only this status. */
  status: DspStatus;
}>;

export type DistributionStatusData = {
  releaseId: number;
  distributorStoreId: number;
  albumsCount: number;
  singlesCount: number;
  epCount: number;
  ringtonesCount: number;
  videosCount: number;
  totalQuantity: number;
  totalNet: number;
  releasePriceId: number;
  trackPriceId: number;
  releaseStatus: {
    status: DspStatus;
    statusText: string;
    errorMessage: any;
    addedDate: string;
    deliveryDate: string;
    firstDeliveryDate: string;
    dateLive: any;
    urlInStore: string;
    summarySection: number;
    isError: boolean;
  };
  inProgressCount: number;
  inspectionCount: number;
  licensingCount: number;
  deliveredCount: number;
  mayBeLiveCount: number;
  liveCount: number;
  errorCount: number;
  distributedCount: number;
  takenDownCount: number;
  inTakeDownCount: number;
  allCount: number;
};

export type GetDistributionStatusResponse = {
  totalItemsCount: number;
  pageNumber: number;
  pageSize: number;
  items: DistributionStatusData[];
};

export interface StatementSummary {
  statementId: string;
  statementTypeId: number;

  payorDistributorId: number | null;
  payorDistributorStoreId: number | null;
  payorName: string;
  payorImageId: number | null;
  payorId: number | null;

  payeeName: string;
  payeeImageId: number | null;
  payeeId: number;
  isRoyaltyTokenPayee: boolean;

  isReady: boolean;
  isReprocessing: boolean;
  isExchangeRateConfirmed: boolean;
  isNew: boolean;
  hasErrors: boolean;
  canUnlock: boolean;
  isPayed: boolean;
  eligibleForApproval: boolean;
  payeeIsPayable: boolean;
  sentForPayment: boolean;
  isSplited: boolean;
  isSaleLineGenerationCompleted: boolean | null;

  currencyCode: string;
  targetCurrencyCode: string;
  countryId: number | null;
  exchangeRate: number;

  fileName: string | null;
  filePath: string;
  userStatementDownloadToken: string;

  creationDate: string;
  lastUpdateDate: string | null;
  approvalDate: string | null;
  date: string;
  sentToPayeeAtDate: string | null;

  totalQuantity: number;
  totalQuantityDownload: number;
  totalQuantityStreaming: number;

  totalGross: number;
  totalNet: number;
  totalNetDownload: number;
  totalNetStreaming: number;

  totalTax: number;
  totalAdjustment: number;
  adjustmentPercentage: number | null;

  totalRevenuShare: number;
  totalMechanicals: number;

  errorsAssetsCount: number;
  errorsGross: number;
  matchedAssetsCount: number;

  statementDisplayTypeId: number;
  statementType: string;
  suffix: string | null;
  contractName: string;
  status: number;
  approvedBy: string;

  userStatementsQueueName: string | null;
  statementApprovalQueueApprove: string | null;
  statementApprovalQueueStatus: string | null;
  statementApprovalQueueErrorMessage: string | null;
  statementEmailQueueStatus: string | null;
}

export interface AdditionalCounters {
  totalApprovedCount: number;
  totalUnApprovedCount: number;
  totalErrorCount: number;
  totalNeedFxCount: number;
  totalCount: number;
}

export type RetrieveStatementsResponse = {
  total: number;
  totalItemsCount: number;
  pageNumber: number;
  pageSize: number;
  items: StatementSummary[];

  additionalCounters: AdditionalCounters;
};

export type RetrieveStatementsParams = {
  enterpriseId?: number;
  pageNumber?: number;
  pageSize?: number;
};

export type RetrieveStatementResponse = StatementSummary;

export type TakedownReleasePayload = { releaseId: string; stores: number[] };

export type TrackRetailPriceListResponse = {
  priceTierId: number;
  name: string;
  code: number;
  isForRelease: boolean;
  isForTrack: boolean;
  price: number;
  distributorStoreId: number;
  order: number;
}[];

export type AnalyticsFilter = Partial<{
  dateGranularity: 'Daily' | 'Weekly' | 'Monthly' | 'Yearly' | 'All';
  fromDate: Date;
  toDate: Date;
  metrics: 'all' | 'dayStreams';
  releaseIds: number[];
  distributorIds: number[];
  trackIds: number[];
  artistIds: number[];
  labelId: number[];
  enterpriseId: number[];
  pageSize: number;
  pageNumber: number;
  orderByDescending: boolean;
  orderByProperty: 'streamsCount' | 'netRevenue';
}>;

export type AnalyticsQuery = {
  metrics: 'trends' | 'revenue' | 'consumption';
  aggregation:
    | 'byCountry'
    | 'byDistributor'
    | 'byTrack'
    | 'byRelease'
    | 'byArtist'
    | 'byDeliveryType'
    | 'metricsByDate'
    | 'byDate';
  filters?: AnalyticsFilter;
};

export type FetchContractSummaryListParams = Partial<{
  payeeIds: number[];
  contractLevel: number;
  pageNumber: number;
  pageSize: number;
}>;

export type ContractSummaryPayee = {
  payeeId: number;
  sharePercentage: number;
  startingBalance: number;
  isCommissionPayee: boolean;
  companyName: string;
  email: string;
};

export type ContractSummary = {
  contractId: number;
  startDate: string;
  expirationDate: string;
  expirationNoticeDays: number | null;
  isActive: boolean;
  mechanicalsFixedPennyRate: number | null;
  mechanicalsIsFullRate: boolean | null;
  mechanicalsRatePercentage: number | null;
  mechanicalsSalesPercentage: number | null;
  mechanicalsTypeId: number | null;
  name: string;
  payMechanicals: boolean;
  creationDate: string;
  isRoyaltyTokenContract: boolean;
  contractLevelId: number;
  contractPayees: ContractSummaryPayee[];
};

export type PayeeContact = {
  contactId: number;
  name: string;
  currencyCode: string | null;
  phone: string | null;
  email: string | null;
  address: string | null;
  address2: string | null;
  zipcode: string | null;
  countryId: number | null;
  imageId: string | null;
  image: any | null;
  isActive: boolean;
  labelId: number | null;
  publisherId: number | null;
  artistId: number | null;
  city: string | null;
  state: string | null;
  location: string | null;
};

export type Payee = {
  isCompany: boolean;
  companyOrIndividualID: string | null;
  balance: number;
  minimumPayment: number;
  autoSendStatements: boolean;
  lastStatementSentDate: string | null;
  lastStatementDate: string | null;
  lastPayementDate: string | null;
  paymentProviderId: string | null;
  paymentProviderName: string;
  paymentUserId: string | null;
  contactId: number;
  payorEnterpriseCurrencyCode: string;
  currencyCode: string;
  contact: PayeeContact;
  referrerId: number | null;
  referrer: any | null;
  role: string;
  licensors: any | null;
  isInvited: boolean;
  isInvitationAccepted: boolean | null;
  contractsNames: string[];
  kountStatusId: number | null;
  kountStatusName: string | null;
  isBlocked: boolean;
  enterpriseId: number;
  isEnterpriseVip: boolean;
  isEnterpriseWhiteListed: boolean;
  notesCount: number;
  isLockedOut: boolean;
  userId: string;
  vatNumber: string | null;
  lastPaymentStatus: string;
  vendorId: string | null;
  taxIdNumber: string | null;
  taxDomicileCountryId: number | null;
  paymentInfoRequestedAt: string | null;
  isPortalPayee: boolean;
  isRoyaltyTokenPayee: boolean;
  status: number;
  payeeId: number;
  companyName: string;
  payeeProvider: any | null;
  email: string | null;
};

export type ContractPayeeContact = Partial<{
  contactId: number;
  name: string;
  currencyCode: string | null;
  phone: string | null;
  email: string | null;
  address: string | null;
  address2: string | null;
  zipcode: string | null;
  countryId: number | null;
  imageId: string | null;
  image: any | null;
  isActive: boolean;
  labelId: number;
  publisherId: number;
  artistId: number;
  city: string | null;
  state: string | null;
  location: string | null;
}>;

export type ContractPayeeInfo = Partial<{
  isCompany: boolean;
  role: string;
  balance: number;
  minimumPayment: number;
  paymentProviderId: number | null;
  paymentProviderName: string;
  paymentUserId: string | null;
  contactId: number;
  currencyCode: string;
  isBlocked: boolean;
  isLockedOut: boolean;
  vatNumber: string | null;
  vendorId: string | null;
  taxIdNumber: string | null;
  contact: ContractPayeeContact;
  enterpriseId: number;
  isPortalPayee: boolean;
  taxDomicileCountryId: number | null;
  payeeId: number;
  companyName: string;
  payeeProvider: any | null;
  email: string | null;
}>;

export type ContractPayee = {
  sharePercentage: number;
  startingBalance: number;
  isCommissionPayee: boolean;
  permission: any | null;
  payee: ContractPayeeInfo;
};

export type ContractTerm = {
  contractTermsId: string;
  contractTermsRateTypeId: number;
  contractTermsRate: number;
  isCountriesIncluded: boolean;
  isDistributorStoresIncluded: boolean;
  countries: number[];
  deliveryTypes: number[];
  releaseTypes: number[];
  distributorStores: number[];
  isCover: boolean | null;
};

export type ContractLicensor = {
  id: string;
  licensorType: number;
  licensorId: number;
  includeFutureAssets: boolean;
  licensorName: string;
  enterpriseId: number;
  contractLevelId: number;
};

export type Contract = {
  contractId: number;
  name: string;
  contractTypeId: number;
  contractLevelId: number;
  createdByEnterpriseId: number | null;
  payorEnterpriseCurrencyCode: string;
  payorName: string;
  amount: number;
  startDate: string;
  expirationDate: string;
  creationDate: string;
  expirationNoticeDays: number;
  accountingPeriodId: number;
  mechanicalsTypeId: number | null;
  mechanicalsSalesPercentage: number | null;
  mechanicalsIsFullRate: boolean | null;
  mechanicalsRatePercentage: number | null;
  mechanicalsFixedPennyRate: number | null;
  payMechanicals: boolean;
  isRoyaltyTokenContract: boolean;
  isActive: boolean;
  status: number;
  userStatementsCount: number;
  contractTerms: ContractTerm[];
  contractReleases: any[];
  contractTracks: any[];
  contractLicensors: ContractLicensor[];
  contractPayees: ContractPayee[];
};

export type FetchContractSummaryListResponse = {
  pageNumber: number;
  pageSize: number;
  totalItemsCount: number;
  totalPagesCount: number;
  items: ContractSummary[];
};

export type AnalyticsResponse<
  T extends 'comprehensive_analysis' | 'key_metrics' = 'comprehensive_analysis',
> = T extends 'comprehensive_analysis'
  ? {
      totals: {
        trackDownloadsCount: number;
        releaseDownloadsCount: number;
        streamsCount: number;
        downloadsCount: number;
        streamsRevenue: number;
        downloadsRevenue: number;
        netRevenue: number;
      };
      fromDate: string;
      toDate: string;
      totalItemsCount: number;
      pageNumber: number;
      pageSize: number;
      items: any[];
    }
  : {
      enterpriseId: number;
      date: string;
      metrics: {
        metric: string;
        value: number;
      }[];
    }[];

interface ExternalId {
  profileId: string;
  distributorStoreId: number;
}

interface File {
  fileId: string;
  isTemp: boolean;
  filename: string;
  externalUrl: null | string;
  lastUpdateDate: string;
}

interface AudioFile {
  audioId: string;
  fileFormat: number;
  audioFilename: string;
  audioChannels: number;
  audioBitDepth: number;
  audioSampleRate: number;
  audioBitrate: number;
  audioSeconds: number;
  audioSize: number;
}

interface RecordingVersion {
  isrc: string;
  recordingVersionType: number;
  audioFiles: AudioFile[];
}

interface SimplifiedRecordingVersion {
  isrc: string;
  recordingVersionType: number;
  audioFiles: {
    audioId: string;
    audioFilename: string;
    fileFormat: number;
  }[];
}

interface Artist {
  labelId: null | number;
  labelName: string;
  contactId: null | string;
  contact: null | any;
  biography: null | string;
  yearsActive: any[];
  influencers: any[];
  contemporaries: any[];
  tags: any[];
  isSigned: boolean;
  musicStyles: any[];
  artistExternalIds: ExternalId[];
  socialUrls: any[];
  artistLocals: any[];
  artistsWebsites: any[];
  webAlias: null | string;
  enterpriseId: number;
  artistId: number;
  name: string;
  image: null | File;
}

interface Contributor {
  contributorId: string;
  roleId: number;
  isPrimary: boolean;
  trackId: number;
  releaseId: null | number;
  artist: Artist;
}

interface Release {
  releaseId: number;
  name: string;
  version: null | string;
  releaseDate: null | string;
  releaseTypeId: number;
  upc: number;
  isLockedForDistribution: boolean;
  isDolbyAtmosReadOnly: boolean;
  isIngested: null | boolean;
  enterpriseId: number;
  assetId: number;
  image: File;
  artist: null | any;
  contributors: null | any[];
}

interface Composer {
  contributorId: number;
  composerId: number;
  composerName: string;
  share: null | number;
  publisherId: null | number;
  publisherName: string;
  publisherAdminId: null | number;
  publisherAdminName: null | string;
  proId: null | number;
  rightsId: number;
  proRegistrationId: null | string;
  roleId: number;
  composersLocals: any[];
}

interface Monetization {
  trackId: number;
  distributorStoreId: number;
  policyId: number;
  isEligible: null | boolean;
  optIn: null | boolean;
  isPaid: null | boolean;
  isLive: boolean | null;
  transactionId: null | string;
  transactionDate: null | string;
}

interface AcrCloudScan {
  scanStartTimeSeconds: number;
  matches: null | any[];
  error: string;
}

interface AcrCloud {
  timestampUtc: string;
  scans: AcrCloudScan[];
}

interface Track {
  enterpriseId: number;
  trackId: number;
  name: string;
  spotifyId: string;
  artistId: number;
  artistName: string;
  artistAppleId: string;
  artistSpotifyId: string;
  artistExternalIds: ExternalId[];
  labelId: number;
  labelName: string;
  discNumber: null | number;
  trackLength: number;
  channels: number;
  sampleRate: number;
  bitDepth: number;
  bitrate: number;
  trackVendorId: null | string;
  isrc: string;
  version: string | null;
  copyrightC: null | string;
  copyrightP: string;
  description: null | string;
  languageId: number;
  explicit: boolean;
  lyrics: null | string;
  playingCount: number;
  appleId: null | string;
  priceTierId: null | number;
  rdioId: null | string;
  previewStartSeconds: number;
  totalSales: number;
  image: null | File;
  wav: File;
  flac: null | File;
  trackRecordingVersions: RecordingVersion[];
  fileExtension: string;
  isLockedForDistribution: boolean;
  isDolbyAtmosReadOnly: boolean;
  primaryMusicStyleId: number;
  secondaryMusicStyleId: number;
  previouslyReleased: boolean;
  trackType: number;
  licenseRequestStatus: null | string;
  isAudioValid: null | boolean;
  tracksLocals: any[];
  artistLocals: any[];
  contributors: Contributor[];
  catalog: null | any;
  compositions: any[];
  releaseIds: number[];
  releases: Release[];
  composerContentsDTO: Composer[];
  isFullyLocked: boolean;
  copyTrackId: null | number;
  isIngested: boolean;
  isLicensePaid: null | boolean;
  distributorStoreGenreIds: any[];
  assetId: number;
  monetizations: Monetization[];
  acrCloud: AcrCloud;
  royaltyToken: null | string;
  trackProperties: number[];
}

export interface ReleaseContent {
  createdBy: string;
  createdByEmail: string;
  createdByUserId: string;
  enterpriseEmail: string;
  enterpriseOwnerId: string;
  lastPayeePaymentDate: string;
  lastPayeePaymentAmount: number;
  isEnterpriseVip: boolean;
  isEnterpriseWhiteListed: boolean;
  payeeReferrerName: null | string;
  description: null | string;
  descriptionTitle: null | string;
  tags: null | string[];
  languageId: number;
  copyrightC: string;
  copyrightP: string;
  recordingLocation: null | string;
  recordingDate: null | string;
  parentalAdvisory: null | string;
  producedBy: null | string;
  mixedBy: null | string;
  masteredBy: null | string;
  masteredDate: null | string;
  productionCredits: null | string;
  totalSales: number;
  enterpriseId: number;
  payeeOwner: number;
  hasRecordLabel: boolean;
  previouslyReleased: boolean;
  releasesLocals: any[];
  contributors: any[];
  artistLocals: any[];
  tracks: Track[];
  enterpriseImageId: string;
  featureFmSmartLink: null | string;
  distributorStoreGenreIds: any[];
  upc: number;
  isrc: null | string;
  trackISRC: string;
  trackRecordingVersions: SimplifiedRecordingVersion[];
  artistId: number;
  artistName: string;
  artistAppleId: string;
  artistSpotifyId: string;
  artistExternalIds: ExternalId[];
  catalog: null | any;
  version: null | string;
  artistImageId: string;
  labelId: number;
  labelName: string;
  releaseDate: string;
  creationDate: string;
  primaryMusicStyleId: number;
  secondaryMusicStyleId: number;
  warningsCount: number;
  notesCount: number;
  payeeNotesCount: number;
  approvedDate: null | string;
  kountStatusId: null | number;
  kountStatusName: null | string;
  isFullyLocked: boolean;
  releaseId: number;
  name: string;
  releaseTypeId: number;
  isLockedForDistribution: boolean;
  isDolbyAtmosReadOnly: boolean;
  isIngested: boolean;
  assetId: number;
  image: File;
  artist: null | any;
}

export interface Revelator {
  /**
   * @param partnerUserId - client's id (unique identifier for clients on revelator platform)
   */
  login(partnerUserId: string): Promise<PartnerLoginResponse>;

  /**
   *
   * @param partnerUserId
   * @param enterpriseId
   */
  getEnterpriseAuth(
    partnerUserId: string,
    enterpriseId?: number,
  ): Promise<{
    accessToken: string;
    permissions: PartnerLoginResponse['permissions'];
  }>;

  signUp(payload: SignUpPayload): Promise<PartnerSignupResponse>;

  /**
   * @param partnerUserId - client's id (unique identifier for clients on revelator platform)
   */
  listStores(partnerUserId: string): Promise<StoreListResponse>;

  listDistributors(): Promise<DistributorListResponse>;

  listLanguages(): Promise<LanguageListResponse>;

  listCountries(): Promise<CountryListResponse>;

  listContributorRoles(): Promise<ContributorRoleListResponse>;

  listContributorRoleGroups(): Promise<ContributorRoleGroupListResponse>;

  listContactRoles(): Promise<ContactRoleListResponse>;

  listTimezones(): Promise<TimezoneListResponse>;

  listTrackProperties(): Promise<TrackPropertyListResponse>;

  listGenres(): Promise<GenreListResponse>;

  /**
   * @param partnerUserId - client's id (unique identifier for clients on revelator platform)
   */
  listMonetizationPolicies(
    partnerUserId: string,
  ): Promise<MonetizationPolicyListResponse>;

  /**
   * @param partnerUserId - client's id (unique identifier for clients on revelator platform)
   */
  listTrackRetailPriceTiers(
    partnerUserId: string,
  ): Promise<TrackRetailPriceListResponse>;

  getUserPermissions(
    partnerUserId: string,
  ): Promise<PartnerLoginResponse['permissions']>;

  /**
   * @param partnerUserId - client's id (unique identifier for clients on revelator platform)
   * @param payload
   */
  uploadImage(
    partnerUserId: string,
    payload: UploadImagePayload,
  ): Promise<string>;

  /**
   * @param partnerUserId - client's id (unique identifier for clients on revelator platform)
   * @param payload
   */
  uploadTrackAudio(
    partnerUserId: string,
    payload: UploadTrackAudioPayload,
  ): Promise<UploadTrackAudioResponse>;

  /**
   * @param partnerUserId - client's id (unique identifier for clients on revelator platform)
   * @param payload
   */
  analytics<
    T extends
      | 'comprehensive_analysis'
      | 'key_metrics' = 'comprehensive_analysis',
  >(
    partnerUserId: string,
    payload: AnalyticsQuery,
  ): Promise<AnalyticsResponse<T>>;

  /**
   * @param partnerUserId - client's id (unique identifier for clients on revelator platform)
   * @param payload
   */
  saveArtist(
    partnerUserId: string,
    payload: SaveArtistPayload,
  ): Promise<SaveArtistResponse>;

  /**
   * @description - creates or edit a label
   * @param partnerUserId - client's id (unique identifier for clients on revelator platform)
   * @param payload
   */
  saveLabel(
    partnerUserId: string,
    payload: SaveLabelPayload,
  ): Promise<SaveLabelResponse>;

  /**
   * Fetches a label by ID from Revelator API
   * @param partnerUserId - client's id (unique identifier for clients on revelator platform)
   * @param labelId - ID of the label to fetch
   */
  getLabel(partnerUserId: string, labelId: number): Promise<Label>;

  /**
   * @description - retrieve all the content of a release
   * @param partnerUserId - client's id (unique identifier for clients on revelator platform)
   * @param releaseId - id of the release
   */
  getReleaseContent(
    partnerUserId: string,
    releaseId: number,
  ): Promise<ReleaseContent>;

  /**
   * @description - creates or edit a release
   * @param partnerUserId - client's id (unique identifier for clients on revelator platform)
   * @param payload
   */
  saveRelease(partnerUserId: string, payload: SaveReleasePayload): Promise<any>;

  /**
   * @param partnerUserId - client's id (unique identifier for clients on revelator platform)
   * @param enterpriseId
   */
  retrieveClientInfo(
    partnerUserId: string,
    enterpriseId: number,
  ): Promise<RetriveClientInfoResponse>;

  /**
   * @param partnerUserId - client's id (unique identifier for clients on revelator platform)
   * @param releaseId
   */
  validateRelease(
    partnerUserId: string,
    releaseId: number,
  ): Promise<ValidateReleaseResponse>;

  /**
   * @param partnerUserId - client's id (unique identifier for clients on revelator platform)
   * @param releaseId
   */
  getTrackSplits(
    partnerUserId: string,
    releaseId: number,
  ): Promise<TrackSplits>;

  /**
   * @param partnerUserId - client's id (unique identifier for clients on revelator platform)
   * @param payload
   */
  saveTrackSplit(
    partnerUserId: string,
    payload: SaveTrackSplitPayload,
  ): Promise<any>;

  /**
   * @param partnerUserId - client's id (unique identifier for clients on revelator platform)
   * @param payload
   */
  saveContract(
    partnerUserId: string,
    payload: SaveContractPayload,
  ): Promise<Contract>;

  /**
   * Fetches a list of contract summaries from Revelator API
   * @param partnerUserId - client's id (unique identifier for clients on revelator platform)
   * @param params - parameters for filtering contracts (payeeIds, pageNumber, pageSize)
   */
  fetchContractSummaryList(
    partnerUserId: string,
    params?: FetchContractSummaryListParams,
  ): Promise<FetchContractSummaryListResponse>;

  /**
   * Fetches a single contract by ID from Revelator API
   * @param partnerUserId - client's id (unique identifier for clients on revelator platform)
   * @param contractId - ID of the contract to fetch
   */
  fetchContract(partnerUserId: string, contractId: number): Promise<Contract>;

  /**
   * Fetches a payee by ID from Revelator API
   * @param partnerUserId - client's id (unique identifier for clients on revelator platform)
   * @param payeeId - ID of the payee to fetch
   */
  fetchPayee(partnerUserId: string, payeeId: number): Promise<Payee>;

  /**
   * @param partnerUserId - client's id (unique identifier for clients on revelator platform)
   * @param payload
   */
  setDistributionOptions(
    partnerUserId: string,
    payload: SetDistributionOptionsPayload,
  ): Promise<any>;

  /**
   * @param partnerUserId - client's id (unique identifier for clients on revelator platform)
   * @param payload
   */
  addDistributionToQueue(
    partnerUserId: string,
    payload: AddToDistributionQueuePayload,
  ): Promise<any>;

  /**
   * @param partnerUserId - client's id (unique identifier for clients on revelator platform)
   * @param payload
   */
  getDistributionStatus(
    partnerUserId: string,
    payload: GetDistributionStatusParams,
  ): Promise<GetDistributionStatusResponse>;

  /**
   * @description - Retrieves all revenue statements for client.
   *
   * @param partnerUserId - client's id (unique identifier for clients on revelator platform)
   * @param params
   */
  retrieveStatements(
    partnerUserId: string,
    params?: RetrieveStatementsParams,
  ): Promise<RetrieveStatementsResponse>;

  /**
   * @description - Retrieves only the specified statement and its parameters.
   *
   * @param partnerUserId - client's id (unique identifier for clients on revelator platform)
   * @param statementId - Id of the statement about to be retrieved
   */
  retrieveStatement(
    partnerUserId: string,
    statementId: string,
  ): Promise<RetrieveStatementResponse>;

  /**
   * @description - Downloads the Details revenue report as a .csv file for the specified statement ID.
   *
   * @param partnerUserId - client's id (unique identifier for clients on revelator platform)
   * @param statementId - Id of the statement about to be downloaded
   */
  downloadStatement(
    partnerUserId: string,
    statementId: string,
    enterpriseId?: number,
  ): Promise<Buffer>;

  /**
   * @description - Takedown a release from one or more stores.
   *
   * @param partnerUserId - client's id (unique identifier for clients on revelator platform)
   * @param payload
   */
  takedownRelease(
    partnerUserId: string,
    payload: TakedownReleasePayload,
  ): Promise<any>;
}
