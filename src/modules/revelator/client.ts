import { HttpClient } from '@app/internal/http/client';
import { DURATION } from '@app/internal/enums';
import { AxiosError } from 'axios';
import { env } from '@app/config/env';
import Deasyncify from 'deasyncify';
import { cloneDeep, omit } from 'lodash';
import { inject } from 'inversify';
import { MODULE_TOKENS } from '@app/internal/ioc/tokens';
import { Logger } from '@app/internal/logger';
import { RedisStore } from '@app/internal/redis/store';
import { prefixKey } from '@app/utils/prefix-key';
import FormData from 'form-data';
import {
  AddToDistributionQueuePayload,
  AuthRequestArgs,
  GetDistributionStatusParams,
  GetDistributionStatusResponse,
  TrackRetailPriceListResponse,
  PartnerLoginResponse,
  PartnerSignupResponse,
  RetriveClientInfoResponse,
  Revelator,
  SaveArtistPayload,
  SaveArtistResponse,
  SaveContractPayload,
  SaveLabelPayload,
  SaveLabelResponse,
  SaveReleasePayload,
  SaveTrackSplitPayload,
  SetDistributionOptionsPayload,
  SignUpPayload,
  TakedownReleasePayload,
  UploadImagePayload,
  UploadTrackAudioPayload,
  UploadTrackAudioResponse,
  ValidateReleaseResponse,
  AnalyticsQuery,
  AnalyticsResponse,
  ReleaseContent,
  TrackSplits,
  FetchContractSummaryListParams,
  FetchContractSummaryListResponse,
  Contract,
  Payee,
  Label,
  RetrieveStatementsResponse,
  RetrieveStatementResponse,
  RetrieveStatementsParams,
} from './index';

export class RevelatorSignupError extends Error {
  constructor(public error: any, public response?: any) {
    super('Error signing up on revelator');
  }
}

export class RevelatorClient extends HttpClient implements Revelator {
  private endpoints = {
    // Account
    signup: '/partner/account/signup',
    login: '/partner/account/login',
    upgrade: '/partner/account/upgrade',

    // Misc Lists
    storeList: '/common/lookup/stores',
    distributorList: '/common/lookup/distributors',
    languageList: '/common/lookup/languages',
    contributorRoleList: '/common/lookup/contributorRoles',
    timezoneList: '/common/lookup/timezoneIds',
    countryList: '/common/lookup/countries',
    trackPropertyList: '/common/lookup/trackProperties',
    genreList: '/common/lookup/musicstyles',
    monetizationPolicyList: 'common/lookup/MonetizationPolicies',
    trackRetailPriceTierList: '/common/lookup/pricetiers',

    // Artists
    createArtist: '/artists',

    // Labels
    editLabel: (enterpriseId: number) =>
      `/api/enterprises/${enterpriseId}/labels`,
    getLabel: (labelId: number) => `/content/label/${labelId}`,

    // Catalog Management
    releaseSave: '/content/release/save',
    getReleaseContent: (releaseId: number) => `/content/release/${releaseId}`,
    uploadImages: '/media/image/upload',
    uploadAudio: '/media/audio/upload/wav',
    uploadAudioExternal: '/media/audio/pullexternal/wav',

    retrieveClientInfo: (enterpriseId: number) =>
      `enterprise/clients/${enterpriseId}`,

    // Track Splits
    getTrackSplits: (releaseId: number) =>
      `/contracts/splits/releases/${releaseId}`,
    saveTrackSplit: (enterpriseId: number) =>
      `/api/enterprises/${enterpriseId}/contracts/splits/save`,

    // Contracts

    saveContract: '/accounting/contract/save',
    fetchContractSummaryList: (params: FetchContractSummaryListParams) => {
      const queryParams: string[] = [];

      if (params.payeeIds && params.payeeIds.length > 0) {
        params.payeeIds.forEach((id, index) => {
          queryParams.push(`payeeIds[${index}]=${id}`);
        });
      }

      if (params.contractLevel) {
        queryParams.push(`contractLevel=${params.contractLevel}`);
      }

      if (params.pageNumber) {
        queryParams.push(`pageNumber=${params.pageNumber}`);
      }

      if (params.pageSize) {
        queryParams.push(`pageSize=${params.pageSize}`);
      }

      return `/accounting/contracts${
        queryParams.length > 0 ? `?${queryParams.join('&')}` : ''
      }`;
    },
    fetchContract: (contractId: number) => `/accounting/contract/${contractId}`,
    fetchPayee: (payeeId: number) => `/accounting/payee/${payeeId}`,

    // Analytics
    analyticsQuery: ({ metrics, aggregation, filters }: AnalyticsQuery) => {
      let url = `/analytics/${metrics}/${aggregation}`;

      const queryParamList = [];

      for (const field in filters) {
        let value = filters[field];

        if (field == 'distributorIds' || field == 'artistIds') {
          filters[field].forEach((value: number) => {
            queryParamList.push(`${field}=${value}`);
          });

          continue;
        } else if (field == 'fromDate' || field == 'toDate') {
          if (filters[field] == null) continue;

          value = (filters[field] as Date).toISOString().split('T')[0];
        }

        if (Array.isArray(value)) {
          value = `[${filters[field].toString()}]`;
        }

        queryParamList.push(`${field}=${value}`);
      }

      if (queryParamList.length > 0) {
        const queryParam = queryParamList.join('&');

        url = `${url}?${queryParam}`;
      }

      return url;
    },

    // Takedowns

    takedownRelease: (releaseId: string) =>
      `/distribution/release/takedown?releaseId=${releaseId}`,

    // Distributions
    validateRelease: (releaseId: number) =>
      `/distribution/release/${releaseId}/validate`,
    setDistributionOptions: `/content/release/retail/save`,
    addReleaseToDistributionQueue: (releaseId: number) =>
      `/distribution/release/addtoqueue?releaseId=${releaseId}`,
    getDistributionStatus: (params: GetDistributionStatusParams) => {
      const queryParams: string[] = [];

      for (const key in params) {
        queryParams.push(`${key}=${params[key]}`);
      }
      return `/distribution/store/all${
        queryParams.length > 0 ? `?${queryParams.join('&')}` : ''
      }`;
    },

    // Revenue statements
    retrieveStatements: ({
      enterpriseId,
      pageNumber,
      pageSize,
    }: RetrieveStatementsParams): string => {
      const paging = new URLSearchParams();

      if (pageNumber !== undefined)
        paging.set('pageNumber', String(pageNumber));

      if (pageSize !== undefined) paging.set('pageSize', String(pageSize));

      const qs = paging.toString();
      return `/finance/salereport/all?approved=true&enterpriseId=${enterpriseId}${
        qs ? `&${qs}` : ''
      }`;
    },
    retrieveStatement: (statementId: string) => {
      const id = encodeURIComponent(statementId);
      return `/finance/salereport/${id}`;
    },
    downloadStatement: (statementId: string) => {
      const id = encodeURIComponent(statementId);
      return `/finance/salereport/download/details?statementId=${id}`;
    },
  } as const;

  private async memoGetRequest(
    methodName: string,
    url: string,
    ttl = 12 * DURATION.HOURS,
  ) {
    const cacheKey = `${RevelatorClient.name}:${methodName}:${url}`;

    const cachedResponseData = await this.redisStore.get(cacheKey);

    if (cachedResponseData != null) {
      return cachedResponseData;
    }

    const res = await this.get(url);

    await this.redisStore.set(cacheKey, res.data, ttl);

    return res.data;
  }

  private async getAuth(
    partnerUserId: string,
    email: string,
    enterpriseName: string,
  ) {
    return this.login(partnerUserId);
  }

  private async authRequest<T>({
    partnerUserId,
    email,
    enterpriseName,
    ...config
  }: AuthRequestArgs<T>) {
    const auth = await this.getAuth(partnerUserId, email, enterpriseName);

    if (!config.headers) {
      config.headers = {};
    }

    config.headers['Authorization'] = `Bearer ${auth.accessToken}`;

    return this.request(config);
  }

  private async memoAuthGetRequest(
    partnerUserId: string,
    email: string,
    enterpriseName: string,
    methodName: string,
    url: string,
    ttl = 12 * DURATION.HOURS,
  ) {
    const cacheKey = `${RevelatorClient.name}:${methodName}:${url}`;

    const cachedResponseData = await this.redisStore.get(cacheKey);

    if (cachedResponseData != null) {
      return cachedResponseData;
    }

    const res = await this.authRequest({
      url,
      method: 'GET',
      partnerUserId,
      email,
      enterpriseName,
    });

    await this.redisStore.set(cacheKey, res.data, ttl);

    return res.data;
  }

  constructor(
    @inject(MODULE_TOKENS.Logger) protected readonly logger: Logger,
    @inject(MODULE_TOKENS.RedisStore) private readonly redisStore: RedisStore,
  ) {
    super({
      baseURL: env.revelator_base_url,
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    });
  }

  public async getDistributionStatus(
    partnerUserId: string,
    email: string,
    enterpriseName: string,
    payload: GetDistributionStatusParams,
  ): Promise<GetDistributionStatusResponse> {
    const url = this.endpoints.getDistributionStatus(payload);

    const res = await this.authRequest({
      url,
      method: 'GET',
      partnerUserId,
      email,
      enterpriseName,
    });

    return res.data;
  }

  public async addDistributionToQueue(
    partnerUserId: string,
    email: string,
    enterpriseName: string,
    payload: AddToDistributionQueuePayload,
  ): Promise<any> {
    const url = this.endpoints.addReleaseToDistributionQueue(payload.releaseId);

    const data = JSON.stringify(payload.stores);

    const res = await this.authRequest({
      url,
      method: 'POST',
      data,
      partnerUserId,
      email,
      enterpriseName,
    });

    return res.data;
  }

  public async setDistributionOptions(
    partnerUserId: string,
    email: string,
    enterpriseName: string,
    payload: SetDistributionOptionsPayload,
  ): Promise<any> {
    const url = this.endpoints.setDistributionOptions;

    const data = JSON.stringify(payload);

    const res = await this.authRequest({
      url,
      method: 'POST',
      data,
      partnerUserId,
      email,
      enterpriseName,
    });

    return res.data;
  }

  public async validateRelease(
    partnerUserId: string,
    email: string,
    enterpriseName: string,
    releaseId: number,
  ): Promise<ValidateReleaseResponse> {
    const url = this.endpoints.validateRelease(releaseId);

    const res = await this.authRequest({
      url,
      method: 'GET',
      partnerUserId,
      email,
      enterpriseName,
    });

    return res.data;
  }

  public async retrieveClientInfo(
    partnerUserId: string,
    email: string,
    enterpriseName: string,
    enterpriseId: number,
  ): Promise<RetriveClientInfoResponse> {
    const url = this.endpoints.retrieveClientInfo(enterpriseId);

    const res = await this.authRequest({
      url,
      method: 'GET',
      partnerUserId,
      email,
      enterpriseName,
    });

    return res.data;
  }

  public async login(partnerUserId: string): Promise<PartnerLoginResponse> {
    let shouldReAuthenticate = false;

    const cacheKey = prefixKey(`${RevelatorClient.name}:login`, partnerUserId);

    // retrieve auth from cache
    const auth = await this.redisStore.get<
      PartnerLoginResponse & { timestamp: string }
    >(cacheKey);

    if (auth) {
      const timestamp = new Date(auth.timestamp);
      const currentTime = Date.now();

      const shouldCheckValidity =
        currentTime - timestamp.getTime() > 10 * DURATION.MINUTES;

      if (!shouldCheckValidity) {
        return omit(auth, ['timestamp']);
      }

      // reset timestamp to start from now
      const tokenTTL = await this.redisStore.ttl(cacheKey);

      await this.redisStore.set(
        cacheKey,
        { ...auth, timestamp: new Date() },
        tokenTTL,
      );

      // test if auth is valid
      const authTestUrl = this.endpoints.retrieveClientInfo(
        auth.permissions[0].enterpriseId,
      );

      const [, err] = await Deasyncify.watch(
        this.get(authTestUrl, {
          headers: { Authorization: `Bearer ${auth.accessToken}` },
        }),
      );

      if (err) {
        if (err instanceof AxiosError) {
          if (err?.response?.status === 401) {
            shouldReAuthenticate = true;
          }
        }

        throw err;
      }
    } else {
      shouldReAuthenticate = true;
    }

    if (!shouldReAuthenticate) return omit(auth, ['timestamp']);

    const url = this.endpoints.login;

    const data = JSON.stringify({
      partnerApiKey: env.revelator_api_token,
      partnerUserId,
    });

    const res = await this.post(url, data);

    // save auth to cache with timestamp
    await this.redisStore.set(
      cacheKey,
      { ...res.data, timestamp: new Date() },
      30 * DURATION.MINUTES,
    );

    return res.data;
  }

  public async signUp(payload: SignUpPayload): Promise<PartnerSignupResponse> {
    const url = this.endpoints.signup;

    const data = JSON.stringify({
      ...payload,
      partnerAPIKey: env.revelator_api_token,
    });

    const res = await this.post(url, data);

    return res.data;
  }

  public listStores(
    partnerUserId: string,
    email: string,
    enterpriseName: string,
  ) {
    return this.memoAuthGetRequest(
      partnerUserId,
      email,
      enterpriseName,
      this.listStores.name,
      this.endpoints.storeList,
    );
  }

  public listDistributors(): Promise<any> {
    return this.memoGetRequest(
      this.listDistributors.name,
      this.endpoints.distributorList,
    );
  }

  public async listLanguages() {
    return this.memoGetRequest(
      this.listLanguages.name,
      this.endpoints.languageList,
    );
  }

  public async listCountries() {
    return this.memoGetRequest(
      this.listCountries.name,
      this.endpoints.countryList,
    );
  }

  public async listContributorRoles() {
    return this.memoGetRequest(
      this.listContributorRoles.name,
      this.endpoints.contributorRoleList,
    );
  }

  public async listTimezones() {
    return this.memoGetRequest(
      this.listTimezones.name,
      this.endpoints.timezoneList,
    );
  }

  public async listTrackProperties() {
    return this.memoGetRequest(
      this.listTrackProperties.name,
      this.endpoints.trackPropertyList,
    );
  }

  public async listGenres() {
    return this.memoGetRequest(this.listGenres.name, this.endpoints.genreList);
  }

  public async listMonetizationPolicies(
    partnerUserId: string,
    email: string,
    enterpriseName: string,
  ) {
    return this.memoAuthGetRequest(
      partnerUserId,
      email,
      enterpriseName,
      this.listMonetizationPolicies.name,
      this.endpoints.monetizationPolicyList,
    );
  }

  public async listTrackRetailPriceTiers(
    partnerUserId: string,
    email: string,
    enterpriseName: string,
  ): Promise<TrackRetailPriceListResponse> {
    return this.memoAuthGetRequest(
      partnerUserId,
      email,
      enterpriseName,
      this.listTrackRetailPriceTiers.name,
      this.endpoints.trackRetailPriceTierList,
    );
  }

  public async uploadImage(
    partnerUserId: string,
    email: string,
    enterpriseName: string,
    { file, coverImage }: UploadImagePayload,
  ): Promise<string> {
    const url = this.endpoints.uploadImages;

    const data = new FormData();

    data.append('file', file.buffer, {
      filename: file.name,
      contentType: file.contentType,
    });

    data.append('cover', String(coverImage));

    const res = await this.authRequest({
      url,
      method: 'POST',
      partnerUserId,
      email,
      enterpriseName,
      data,
      headers: data.getHeaders(),
    });

    return res.data;
  }

  public async uploadTrackAudio(
    partnerUserId: string,
    email: string,
    enterpriseName: string,
    payload: UploadTrackAudioPayload,
  ): Promise<UploadTrackAudioResponse> {
    if ('file' in payload) {
      const { file } = payload;
      const url = this.endpoints.uploadAudio;

      const data = new FormData();

      data.append('file', file.buffer, {
        filename: file.name,
        contentType: file.contentType,
      });

      const res = await this.authRequest({
        url,
        method: 'POST',
        partnerUserId,
        email,
        enterpriseName,
        data,
        headers: data.getHeaders(),
      });

      return res.data;
    }

    const { filename, externalUrl } = payload;

    const url = this.endpoints.uploadAudioExternal;

    const data = JSON.stringify({ filename, externalUrl });

    const res = await this.authRequest({
      url,
      method: 'POST',
      partnerUserId,
      email,
      enterpriseName,
      data,
    });

    return res.data;
  }

  public async saveArtist(
    partnerUserId: string,
    email: string,
    enterpriseName: string,
    payload: SaveArtistPayload,
  ): Promise<SaveArtistResponse> {
    const url = this.endpoints.createArtist;

    const res = await this.authRequest({
      url,
      method: 'POST',
      partnerUserId,
      email,
      enterpriseName,
      data: JSON.stringify(payload),
    });

    return res.data;
  }

  public async saveRelease(
    partnerUserId: string,
    email: string,
    enterpriseName: string,
    payload: SaveReleasePayload,
  ): Promise<any> {
    const url = this.endpoints.releaseSave;

    const auth = await this.getAuth(partnerUserId, email, enterpriseName);

    for (const track of payload.tracks) {
      track.enterpriseId = auth.permissions[0].enterpriseId;
    }

    this.logger.log({ payload });

    const data = JSON.stringify(payload);

    const res = await this.post(url, data, {
      headers: { Authorization: `Bearer ${auth.accessToken}` },
    });

    return res.data;
  }

  public async saveLabel(
    partnerUserId: string,
    email: string,
    enterpriseName: string,
    payload: SaveLabelPayload,
  ): Promise<SaveLabelResponse> {
    const auth = await this.getAuth(partnerUserId, email, enterpriseName);

    const url = this.endpoints.editLabel(auth.permissions[0].enterpriseId);

    const data = JSON.stringify(payload);

    const res = await this.post(url, data, {
      headers: { Authorization: `Bearer ${auth.accessToken}` },
    });

    return res.data;
  }

  /**
   * Fetches a label by ID from Revelator API
   * @param partnerUserId - client's id (unique identifier for clients on revelator platform)
   * @param email - client's email (required to setup their revelator account if they don't have one already)
   * @param enterpriseName - client's enterpriseName (required to setup their revelator account if they don't have one already)
   * @param labelId - ID of the label to fetch
   */
  public async getLabel(
    partnerUserId: string,
    email: string,
    enterpriseName: string,
    labelId: number,
  ): Promise<Label> {
    const url = this.endpoints.getLabel(labelId);

    const res = await this.authRequest({
      url,
      method: 'GET',
      partnerUserId,
      email,
      enterpriseName,
    });

    return res.data;
  }

  public async getTrackSplits(
    partnerUserId: string,
    email: string,
    enterpriseName: string,
    releaseId: number,
  ): Promise<TrackSplits> {
    const url = this.endpoints.getTrackSplits(releaseId);

    const res = await this.authRequest({
      url,
      method: 'GET',
      partnerUserId,
      email,
      enterpriseName,
    });

    return res.data;
  }

  public async saveTrackSplit(
    partnerUserId: string,
    email: string,
    enterpriseName: string,
    payload: SaveTrackSplitPayload,
  ): Promise<any> {
    const auth = await this.getAuth(partnerUserId, email, enterpriseName);

    const url = this.endpoints.saveTrackSplit(auth.permissions[0].enterpriseId);

    const data = JSON.stringify(payload);

    const res = await this.post(url, data, {
      headers: { Authorization: `Bearer ${auth.accessToken}` },
    });

    return res.data;
  }

  public async saveContract(
    partnerUserId: string,
    email: string,
    enterpriseName: string,
    payload: SaveContractPayload,
  ): Promise<any> {
    const url = this.endpoints.saveContract;

    const res = await this.authRequest({
      url,
      method: 'POST',
      partnerUserId,
      email,
      enterpriseName,
      data: JSON.stringify(payload),
    });

    return res.data;
  }

  public async takedownRelease(
    partnerUserId: string,
    email: string,
    enterpriseName: string,
    payload: TakedownReleasePayload,
  ): Promise<any> {
    const url = this.endpoints.takedownRelease(payload.releaseId);

    const data = JSON.stringify(payload.stores);

    const res = await this.authRequest({
      url,
      method: 'POST',
      partnerUserId,
      email,
      enterpriseName,
      data,
    });

    return res.data;
  }

  public async analytics<
    T extends
      | 'comprehensive_analysis'
      | 'key_metrics' = 'comprehensive_analysis',
  >(
    partnerUserId: string,
    email: string,
    enterpriseName: string,
    payload: AnalyticsQuery,
  ): Promise<AnalyticsResponse<T>> {
    const url = this.endpoints.analyticsQuery(payload);

    if (url.length >= 1900 && payload.filters?.artistIds?.length > 0) {
      const batchedOps: Promise<any>[] = [];

      let artistFilterPerBatch = 100;

      let start = 0;
      let end = artistFilterPerBatch;

      while (start < payload.filters.artistIds.length) {
        const artistIds = payload.filters.artistIds.slice(start, end);

        const batchedPayload = {
          ...payload,
          filters: { ...payload.filters, artistIds },
        };

        let newUrl = this.endpoints.analyticsQuery(batchedPayload);

        while (newUrl.length >= 1900) {
          artistFilterPerBatch -= 5;
          end = start + artistFilterPerBatch;

          batchedPayload.filters.artistIds = payload.filters.artistIds.slice(
            start,
            end,
          );

          newUrl = this.endpoints.analyticsQuery(batchedPayload);
        }

        batchedOps.push(
          this.authRequest({
            url: newUrl,
            method: 'GET',
            partnerUserId,
            email,
            enterpriseName,
          }).then((res) => res.data),
        );

        start = end;
        end += artistFilterPerBatch;
      }

      const results = await Promise.all(batchedOps);

      let aggregation: any;

      for (const data of results) {
        if (!aggregation) {
          aggregation = cloneDeep(data);
          continue;
        }

        for (const field in data.totals) {
          aggregation.totals[field] += data.totals[field];
        }

        if (aggregation.totalItemsCount) {
          aggregation.totalItemsCount += data.totalItemsCount;
        }

        if (aggregation.pageSize) {
          aggregation.pageSize += data.pageSize;
        }

        if (aggregation.items) {
          aggregation.items.push(...data.items);
        }
      }

      if (payload.aggregation === 'byCountry') {
        aggregation.items = aggregation.items.reduce((acc, item) => {
          const existingItem = acc.find(
            (i) => i.metadata.iso2Code === item.metadata.iso2Code,
          );

          if (existingItem) {
            for (const field in item) {
              if (field === 'metadata') {
                continue;
              }

              existingItem[field] += item[field];
            }
          } else {
            acc.push(item);
          }

          return acc;
        }, []);
      }

      if (payload.filters?.orderByProperty) {
        aggregation.items.sort((a, b) => {
          if (payload.filters?.orderByDescending) {
            return (
              b[payload.filters.orderByProperty] -
              a[payload.filters.orderByProperty]
            );
          }

          return (
            a[payload.filters.orderByProperty] -
            b[payload.filters.orderByProperty]
          );
        });
      }

      if (payload.filters?.pageNumber) {
        const pageNumber = payload.filters.pageNumber;
        const pageSize = payload.filters?.pageSize || 10;

        aggregation.pageNumber = pageNumber;
        aggregation.pageSize = pageSize;
        aggregation.totalItemsCount = aggregation.items.length;

        aggregation.items = aggregation.items.slice(
          (pageNumber - 1) * pageSize,
          pageNumber * pageSize,
        );
      }

      return aggregation;
    }

    const res = await this.authRequest({
      url,
      method: 'GET',
      partnerUserId,
      email,
      enterpriseName,
    });

    return res.data;
  }

  public async getReleaseContent(
    partnerUserId: string,
    email: string,
    enterpriseName: string,
    releaseId: number,
  ): Promise<ReleaseContent> {
    const url = this.endpoints.getReleaseContent(releaseId);

    return this.memoAuthGetRequest(
      partnerUserId,
      email,
      enterpriseName,
      this.getReleaseContent.name,
      url,
      15 * DURATION.MINUTES,
    );
  }

  /**
   * Fetches a list of contract summaries from Revelator API
   * @param partnerUserId - client's id (unique identifier for clients on revelator platform)
   * @param email - client's email (required to setup their revelator account if they don't have one already)
   * @param enterpriseName - client's enterpriseName (required to setup their revelator account if they don't have one already)
   * @param params - parameters for filtering contracts (payeeIds, pageNumber, pageSize)
   */
  public async fetchContractSummaryList(
    partnerUserId: string,
    email: string,
    enterpriseName: string,
    params: FetchContractSummaryListParams = {},
  ): Promise<FetchContractSummaryListResponse> {
    const url = this.endpoints.fetchContractSummaryList(params);

    const res = await this.authRequest({
      url,
      method: 'GET',
      partnerUserId,
      email,
      enterpriseName,
    });

    return res.data;
  }

  /**
   * Fetches a single contract by ID from Revelator API
   * @param partnerUserId - client's id (unique identifier for clients on revelator platform)
   * @param email - client's email (required to setup their revelator account if they don't have one already)
   * @param enterpriseName - client's enterpriseName (required to setup their revelator account if they don't have one already)
   * @param contractId - ID of the contract to fetch
   */
  public async fetchContract(
    partnerUserId: string,
    email: string,
    enterpriseName: string,
    contractId: number,
  ): Promise<Contract> {
    const url = this.endpoints.fetchContract(contractId);

    const res = await this.authRequest({
      url,
      method: 'GET',
      partnerUserId,
      email,
      enterpriseName,
    });

    // Return the contract data directly, ensuring all required fields are present
    const contract = res.data;
    return {
      contractId: contract.contractId,
      name: contract.name,
      contractTypeId: contract.contractTypeId,
      contractLevelId: contract.contractLevelId,
      createdByEnterpriseId: contract.createdByEnterpriseId,
      payorEnterpriseCurrencyCode: contract.payorEnterpriseCurrencyCode,
      payorName: contract.payorName,
      amount: contract.amount,
      startDate: contract.startDate,
      expirationDate: contract.expirationDate,
      creationDate: contract.creationDate,
      expirationNoticeDays: contract.expirationNoticeDays,
      accountingPeriodId: contract.accountingPeriodId,
      mechanicalsTypeId: contract.mechanicalsTypeId,
      mechanicalsSalesPercentage: contract.mechanicalsSalesPercentage,
      mechanicalsIsFullRate: contract.mechanicalsIsFullRate,
      mechanicalsRatePercentage: contract.mechanicalsRatePercentage,
      mechanicalsFixedPennyRate: contract.mechanicalsFixedPennyRate,
      payMechanicals: contract.payMechanicals,
      isRoyaltyTokenContract: contract.isRoyaltyTokenContract,
      isActive: contract.isActive,
      status: contract.status,
      userStatementsCount: contract.userStatementsCount,
      contractTerms: contract.contractTerms || [],
      contractReleases: contract.contractReleases || [],
      contractTracks: contract.contractTracks || [],
      contractLicensors: contract.contractLicensors || [],
      contractPayees: contract.contractPayees || [],
    };
  }

  /**
   * Fetches a payee by ID from Revelator API
   * @param partnerUserId - client's id (unique identifier for clients on revelator platform)
   * @param email - client's email (required to setup their revelator account if they don't have one already)
   * @param enterpriseName - client's enterpriseName (required to setup their revelator account if they don't have one already)
   * @param payeeId - ID of the payee to fetch
   */
  public async fetchPayee(
    partnerUserId: string,
    email: string,
    enterpriseName: string,
    payeeId: number,
  ): Promise<Payee> {
    const url = this.endpoints.fetchPayee(payeeId);

    const res = await this.authRequest({
      url,
      method: 'GET',
      partnerUserId,
      email,
      enterpriseName,
    });

    return res.data;
  }

  public async retrieveStatements(
    partnerUserId: string,
    email: string,
    enterpriseName: string,
    params: RetrieveStatementsParams,
  ): Promise<RetrieveStatementsResponse> {
    const url = this.endpoints.retrieveStatements(params);

    const res = await this.authRequest({
      url,
      method: 'GET',
      partnerUserId,
      email,
      enterpriseName,
    });

    return res.data;
  }

  public async retrieveStatement(
    partnerUserId: string,
    email: string,
    enterpriseName: string,
    statementId: string,
  ): Promise<RetrieveStatementResponse> {
    const url = this.endpoints.retrieveStatement(statementId);

    const res = await this.authRequest({
      url,
      method: 'GET',
      partnerUserId,
      email,
      enterpriseName,
    });

    return res.data;
  }

  public async downloadStatement(
    partnerUserId: string,
    email: string,
    enterpriseName: string,
    statementId: string,
  ): Promise<void> {
    const url = this.endpoints.downloadStatement(statementId);

    const res = await this.authRequest({
      url,
      method: 'GET',
      partnerUserId,
      email,
      enterpriseName,
    });

    return res.data;
  }
}
