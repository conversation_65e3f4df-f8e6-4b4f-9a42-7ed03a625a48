import { HttpClient } from '@app/internal/http/client';
import { injectable } from 'inversify';
import { SocialOauth } from '../facebook-oauth';
import { env } from '@app/config/env';

@injectable()
export class InstagramOauthClient extends HttpClient implements SocialOauth {
  private endpoints = {
    auth: 'https://api.instagram.com/oauth/authorize',
    token: 'https://api.instagram.com/oauth/access_token',
    userInfo: 'https://graph.instagram.com/me',
  };

  getAuthUrl(redirectUri: string) {
    const params = new URLSearchParams({
      client_id: env.facebook_client_id,
      redirect_uri: redirectUri,
      response_type: 'code',
      scope: 'user_profile',
      config_id: env.instagram_config_id,
    });
    return `${this.endpoints.auth}?${params}`;
  }

  public async exchangeCode(code: string, redirectUri: string) {
    const res = await this.post(this.endpoints.token, null, {
      params: {
        client_id: env.facebook_client_id,
        client_secret: env.facebook_client_secret,
        grant_type: 'authorization_code',
        redirect_uri: redirectUri,
        code,
      },
    });
    return { access_token: res.data.access_token };
  }

  public async getUserInfo(accessToken: string) {
    const res = await this.get(this.endpoints.userInfo, {
      params: {
        access_token: accessToken,
        fields: 'id,username',
      },
    });
    return res.data;
  }
}
