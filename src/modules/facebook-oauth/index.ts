import { env } from '@app/config/env';
import { HttpClient } from '@app/internal/http/client';
import { injectable } from 'inversify';

export interface SocialOauth {
  getAuthUrl(redirectUri: string): string;
  exchangeCode(code: string, redirectUri: string): Promise<any>;
  getUserInfo(accessToken: string): Promise<any>;
}

@injectable()
export class FacebookOauthClient extends HttpClient implements SocialOauth {
  private readonly endpoints = {
    auth: 'https://www.facebook.com/v20.0/dialog/oauth',
    token: 'https://graph.facebook.com/v20.0/oauth/access_token',
    userInfo: 'https://graph.facebook.com/me',
  };

  getAuthUrl(redirectUri: string) {
    const params = new URLSearchParams({
      client_id: env.facebook_client_id,
      redirect_uri: redirectUri,
      response_type: 'code',
      scope: 'email,public_profile',
      config_id: env.facebook_config_id,
    });
    return `${this.endpoints.auth}?${params}`;
  }

  public async exchangeCode(code: string, redirectUri: string) {
    const res = await this.get(this.endpoints.token, {
      params: {
        client_id: env.facebook_client_id,
        client_secret: env.facebook_client_secret,
        code,
        redirect_uri: redirectUri,
      },
    });
    return res.data;
  }

  async getUserInfo(accessToken: string) {
    const res = await this.get(this.endpoints.userInfo, {
      params: { access_token: accessToken, fields: 'id,name,email' },
    });
    return res.data;
  }
}
