import { HttpClient } from '@app/internal/http/client';
import { injectable } from 'inversify';
import { env } from '@app/config/env';
import { SocialOauth } from '../facebook-oauth';

@injectable()
export class GoogleOauthClient extends HttpClient implements SocialOauth {
  private readonly endpoints = {
    auth: 'https://accounts.google.com/o/oauth2/v2/auth',
    token: 'https://oauth2.googleapis.com/token',
    userInfo: 'https://www.googleapis.com/oauth2/v2/userinfo',
  };

  getAuthUrl(redirectUri: string) {
    const params = new URLSearchParams({
      client_id: env.google_app_id,
      redirect_uri: redirectUri,
      response_type: 'code',
      scope: 'openid email profile',
    });
    return `${this.endpoints.auth}?${params}`;
  }

  public async exchangeCode(code: string, redirectUri: string) {
    const res = await this.post(this.endpoints.token, {
      code,
      client_id: env.google_app_id,
      client_secret: env.google_app_secret,
      redirect_uri: redirectUri,
      grant_type: 'authorization_code',
    });
    return res.data; // includes access_token
  }

  public async getUserInfo(accessToken: string): Promise<any> {
    const url = this.endpoints.userInfo;

    const Authorization = `Bearer ${accessToken}`;

    const res = await this.get(url, {
      headers: { Authorization },
    });

    return res.data;
  }
}
