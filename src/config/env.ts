import * as Joi from 'joi';
import { AppEnv } from '@app/internal/enums';
import { DataValidationError, validate } from '@app/utils/joi-utils';
import dotenv from 'dotenv';
import { mapKeys } from 'lodash';

export type Env = {
  admin_client_base_url: string;
  aws_access_key: string;
  aws_region: string;
  aws_secret_key: string;

  bullmq_url: string;

  cloudfront_distribution_id: string;
  cloudfront_region: string;

  host_name: string;

  web_client_base_url: string;
  web_client_host_name: string;

  db_retry: number;
  db_url: string;
  db_ssl: boolean;

  knex_debug: boolean;

  node_env: AppEnv;

  port: number;
  ws_port: number;

  redis_url: string;

  s3_bucket: string;
  source_email: string;

  token_auth_secret: string;

  vibrate_base_url: string;
  vibrate_api_token: string;

  revelator_api_token: string;
  revelator_base_url: string;
  revelator_enterprise_name: string;
  revelator_parent_account_id: string;

  google_app_id: string;
  google_app_secret: string;

  tiktok_client_key: string;
  tiktok_client_secret: string;

  facebook_client_id: string;
  facebook_client_secret: string;
  facebook_config_id: string;

  instagram_config_id: string;

  spotify_client_id: string;
  spotify_client_secret: string;

  stripe_api_key: string;
  stripe_webhook_secret: string;

  external_account_id: string;

  paystack_secret_key: string;
};

export const env = <Env & Record<string, any>>{};

const envValidationSchema = Joi.object<Env, true>({
  admin_client_base_url: Joi.string().required(),

  aws_access_key: Joi.string().required(),
  aws_region: Joi.string().required(),
  aws_secret_key: Joi.string().required(),

  bullmq_url: Joi.string().required(),

  cloudfront_distribution_id: Joi.string().required(),
  cloudfront_region: Joi.string().required(),

  host_name: Joi.string().required(),

  web_client_base_url: Joi.string().required(),
  web_client_host_name: Joi.string().required(),

  db_retry: Joi.number().default(3),
  db_url: Joi.string().required(),
  db_ssl: Joi.boolean().default(false),

  knex_debug: Joi.bool().when('node_env', {
    is: Joi.string().equal(AppEnv.DEVELOPMENT, AppEnv.STAGING),
    then: Joi.bool().default(true),
    otherwise: Joi.bool().default(false),
  }),

  node_env: Joi.string()
    .valid(AppEnv.DEVELOPMENT, AppEnv.TEST, AppEnv.STAGING, AppEnv.PRODUCTION)
    .default(AppEnv.DEVELOPMENT),

  port: Joi.number().default(8000),
  ws_port: Joi.number().required(),

  redis_url: Joi.string().required(),

  s3_bucket: Joi.string().required(),
  source_email: Joi.string().required(),

  token_auth_secret: Joi.string().required(),

  vibrate_base_url: Joi.string().required(),
  vibrate_api_token: Joi.string().required(),

  revelator_api_token: Joi.string().required(),
  revelator_base_url: Joi.string().required(),
  revelator_enterprise_name: Joi.string().required(),
  revelator_parent_account_id: Joi.string().required(),

  google_app_id: Joi.string().required(),
  google_app_secret: Joi.string().required(),

  tiktok_client_key: Joi.string().required(),
  tiktok_client_secret: Joi.string().required(),

  instagram_config_id: Joi.string().required(),

  facebook_client_id: Joi.string().required(),
  facebook_client_secret: Joi.string().required(),
  facebook_config_id: Joi.string().required(),

  spotify_client_id: Joi.string().required(),
  spotify_client_secret: Joi.string().required(),

  stripe_api_key: Joi.string().required(),
  stripe_webhook_secret: Joi.string().required(),

  external_account_id: Joi.string().required(),

  paystack_secret_key: Joi.string().required(),
}).unknown(true);

export class MissingEnvError extends Error {
  constructor(error: DataValidationError) {
    super(
      `Unable to load environment:\n${JSON.stringify(error.messages, null, 2)}`,
    );
  }
}

export function loadEnv() {
  try {
    dotenv.config();

    const processedEnv = mapKeys(process.env, (_, key) => {
      return key.toLowerCase();
    });

    const validatedEnv = validate(processedEnv, envValidationSchema);

    Object.assign(env, validatedEnv);
    Object.freeze(env);
  } catch (e) {
    if (e instanceof DataValidationError) {
      throw new MissingEnvError(e);
    }
    throw e;
  }
}
