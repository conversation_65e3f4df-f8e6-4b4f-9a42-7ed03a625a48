import { Container } from 'inversify';
import { postgresFactory } from '@app/config/postgres';
import { Knex } from 'knex';
import { Repository } from '@app/internal/postgres/repository';
import { UserService } from '@app/services/user/user.service';
import {
  MIDDLEWARE_TOKENS,
  MODULE_TOKENS,
  SERVICE_TOKENS,
} from '@app/internal/ioc/tokens';
import { Logger } from '@app/internal/logger';
import { defaultSerializers } from '@app/internal/logger/serializers';
import { configureRedisCache } from './redis';
import { initBull } from './bull';
import { RedisStore } from '@app/internal/redis/store';
import { JobQueueManager, getQueue } from '@app/internal/bull';
import { SES } from '@app/modules/ses';
import { SESClient } from '@aws-sdk/client-ses';
import { env } from './env';
import { SNSClient } from '@aws-sdk/client-sns';
import { S3Client } from '@aws-sdk/client-s3';
import { S3 } from '@app/modules/s3';
import { SNS } from '@app/modules/sns';
import { TokenAuth } from '@app/internal/token/auth';
import { TokenStore } from '@app/internal/token/store';
import {
  AuthMiddleware,
  OptionalAuthMiddleware,
} from '@app/http/middlewares/auth.middleware';
import { VibrateClient } from '@app/modules/vibrate';
import { GoogleOauthClient } from '@app/modules/google-oauth';
import { TrackService } from '@app/services/track/track.service';
import { ArtistService } from '@app/services/artist/artist.service';
import { PostService } from '@app/services/post/post.service';
import { MediaService } from '@app/services/media/media.service';
import { SpotifyClient } from '@app/modules/spotify';
import { TrackDraftService } from '@app/services/track/track-draft.service';
import { Revelator } from '@app/modules/revelator';
import { ReleaseService } from '@app/services/release/release.service';
import { ACMClient } from '@aws-sdk/client-acm';
import { ACM } from '@app/modules/acm';
import { CloudFrontClient } from '@aws-sdk/client-cloudfront';
import { CloudFront } from '@app/modules/cloudfront';
import { CommunityService } from '@app/services/community/community.service';
import { IP2C, IP2CClient } from '@app/modules/ip2c';
import { InviteService } from '@app/services/invite/invite.service';
import { LabelService } from '@app/services/label/label.service';
import { RevelatorClient } from '@app/modules/revelator/client';
import { NotificationService } from '@app/services/notification/notification.service';
import { WebSocketServer } from '@app/internal/websocket';
import { StripeClient } from '@app/modules/stripe/client';
import { SubscriptionService } from '@app/services/subscription/subscription.service';
import { MonetizationService } from '@app/services/monetization/monetization.service';
import { WalletService } from '@app/services/wallet/wallet.service';
import { TransactionService } from '@app/services/transaction/transaction.service';
import { SubscriptionCheckMiddleware } from '@app/http/middlewares/subscription-check.middleware';
import { AdminService } from '@app/services/admin/admin.service';
import { BeneficiaryService } from '@app/services/beneficiary/beneficiary.service';
import { Paystack } from '@app/modules/paystack';
import { PaystackClient } from '@app/modules/paystack/client';
import { ReportsService } from '@app/services/reports/reports.service';
import { FacebookOauthClient, SocialOauth } from '@app/modules/facebook-oauth';
import { InstagramOauthClient } from '@app/modules/instagram-oauth';
import { TikTokOauthClient } from '@app/modules/tiktok-oauth';
import { RoyaltyRunService } from '@app/services/royalty/royalty.service';
import { PresenceCache } from '@app/internal/presence';

export async function configureAppContainer(): Promise<Container> {
  const container = new Container();

  const logger = new Logger({
    name: 'makerverse',
    serializers: defaultSerializers(),
  });

  container.bind<Logger>(MODULE_TOKENS.Logger).toConstantValue(logger);

  const pg = await postgresFactory(logger);

  container.bind<Knex>(MODULE_TOKENS.KnexClient).toConstantValue(pg);

  container.bind<Repository>(MODULE_TOKENS.Repository).to(Repository);

  const redis = await configureRedisCache(logger);

  container
    .bind<RedisStore>(MODULE_TOKENS.RedisStore)
    .toConstantValue(new RedisStore(redis));

  container
    .bind<PresenceCache>(MODULE_TOKENS.PresenceCache)
    .to(PresenceCache)
    .inSingletonScope();

  const tokenStore = new TokenStore(env.token_auth_secret, redis);

  const tokenAuth = new TokenAuth(tokenStore);

  container
    .bind<TokenStore>(MODULE_TOKENS.TokenStore)
    .toConstantValue(tokenStore);

  container.bind<TokenAuth>(MODULE_TOKENS.TokenAuth).toConstantValue(tokenAuth);
  container
    .bind<JobQueueManager>(MODULE_TOKENS.JobQueueManager)
    .toConstantValue({ getQueue });

  container.bind<AdminService>(SERVICE_TOKENS.AdminService).to(AdminService);
  container
    .bind<CommunityService>(SERVICE_TOKENS.CommunityService)
    .to(CommunityService);
  container
    .bind<SubscriptionService>(SERVICE_TOKENS.SubscriptionService)
    .to(SubscriptionService);

  container.bind<UserService>(SERVICE_TOKENS.UserService).to(UserService);
  container.bind<TrackService>(SERVICE_TOKENS.TrackService).to(TrackService);
  container
    .bind<TrackDraftService>(SERVICE_TOKENS.TrackDraftService)
    .to(TrackDraftService);
  container
    .bind<ReleaseService>(SERVICE_TOKENS.ReleaseService)
    .to(ReleaseService);
  container.bind<ArtistService>(SERVICE_TOKENS.ArtistService).to(ArtistService);
  container.bind<PostService>(SERVICE_TOKENS.PostService).to(PostService);
  container.bind<MediaService>(SERVICE_TOKENS.MediaService).to(MediaService);
  container.bind<InviteService>(SERVICE_TOKENS.inviteService).to(InviteService);
  container.bind<LabelService>(SERVICE_TOKENS.LabelService).to(LabelService);
  container
    .bind<MonetizationService>(SERVICE_TOKENS.MonetizationService)
    .to(MonetizationService);
  container
    .bind<NotificationService>(SERVICE_TOKENS.NotificationService)
    .to(NotificationService);

  container.bind<WalletService>(SERVICE_TOKENS.WalletService).to(WalletService);
  container
    .bind<TransactionService>(SERVICE_TOKENS.TransactionService)
    .to(TransactionService);

  container
    .bind<BeneficiaryService>(SERVICE_TOKENS.BeneficiaryService)
    .to(BeneficiaryService);

  container
    .bind<ReportsService>(SERVICE_TOKENS.ReportsService)
    .to(ReportsService);

  container
    .bind<RoyaltyRunService>(SERVICE_TOKENS.RoyaltyRunService)
    .to(RoyaltyRunService);

  const awsServiceConfig = {
    region: env.aws_region,
    credentials: {
      accessKeyId: env.aws_access_key,
      secretAccessKey: env.aws_secret_key,
    },
  };

  const acmClient = new ACMClient({
    ...awsServiceConfig,
    region: env.cloudfront_region,
  });

  container.bind<ACMClient>(MODULE_TOKENS.ACMClient).toConstantValue(acmClient);

  container.bind<ACM>(MODULE_TOKENS.ACM).to(ACM);

  const cloudFrontClient = new CloudFrontClient({
    ...awsServiceConfig,
    region: env.cloudfront_region,
  });

  container
    .bind<CloudFrontClient>(MODULE_TOKENS.CloudFrontClient)
    .toConstantValue(cloudFrontClient);

  container.bind<CloudFront>(MODULE_TOKENS.CloudFront).to(CloudFront);

  const s3Client = new S3Client(awsServiceConfig);

  container.bind<S3Client>(MODULE_TOKENS.S3Client).toConstantValue(s3Client);

  container.bind<S3>(MODULE_TOKENS.S3).to(S3);

  const snsClient = new SNSClient(awsServiceConfig);

  container.bind<SNSClient>(MODULE_TOKENS.SNSClient).toConstantValue(snsClient);

  container.bind<SNS>(MODULE_TOKENS.SNS).to(SNS);

  const sesClient = new SESClient(awsServiceConfig);

  container.bind<SESClient>(MODULE_TOKENS.SESClient).toConstantValue(sesClient);

  container.bind(MODULE_TOKENS.SES).to(SES);

  container.bind(MODULE_TOKENS.Vibrate).to(VibrateClient);

  container.bind(MODULE_TOKENS.Stripe).to(StripeClient);

  container.bind<Paystack>(MODULE_TOKENS.Paystack).to(PaystackClient);

  container.bind<Revelator>(MODULE_TOKENS.Revelator).to(RevelatorClient);

  container.bind<IP2C>(MODULE_TOKENS.IP2C).to(IP2CClient);

  container.bind(MODULE_TOKENS.Spotify).to(SpotifyClient);

  container.bind(MODULE_TOKENS.GoogleOauthClient).to(GoogleOauthClient);
  container
    .bind<SocialOauth>(MODULE_TOKENS.FacebookOauthClient)
    .to(FacebookOauthClient);
  container
    .bind<SocialOauth>(MODULE_TOKENS.InstagramOauthClient)
    .to(InstagramOauthClient);
  container
    .bind<SocialOauth>(MODULE_TOKENS.TikTokOauthClient)
    .to(TikTokOauthClient);

  container
    .bind(MODULE_TOKENS.SocketServer)
    .to(WebSocketServer)
    .inSingletonScope();

  await initBull(container);

  container
    .bind<AuthMiddleware>(MIDDLEWARE_TOKENS.AuthMiddleware)
    .to(AuthMiddleware);

  container
    .bind<OptionalAuthMiddleware>(MIDDLEWARE_TOKENS.OptionalAuthMiddleware)
    .to(OptionalAuthMiddleware);

  container
    .bind<SubscriptionCheckMiddleware>(
      MIDDLEWARE_TOKENS.SubscriptionCheckMiddleware,
    )
    .to(SubscriptionCheckMiddleware);

  return container;
}
