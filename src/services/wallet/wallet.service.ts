import { inject, injectable } from 'inversify';
import { ulid } from 'ulid';
import { MODULE_TOKENS } from '@app/internal/ioc/tokens';
import { Repository } from '@app/internal/postgres/repository';
import { Wallet, WalletStatus, WalletType } from './entities/wallet.entity';
import {
  Transaction,
  TransactionStatus,
  TransactionType,
  PaymentType,
  Currency,
} from '@app/services/transaction/entities/transaction.entity';
import { CreateTransactionData } from '../transaction/transaction.service';
import { ApplicationError } from '@app/internal/errors';
import { StatusCodes } from 'http-status-codes';

export type CreateWalletData = Omit<Wallet, 'id' | 'created_at' | 'updated_at'>;
export type UpdateWalletData = Partial<
  Omit<Wallet, 'id' | 'created_at' | 'updated_at'>
>;

export type CreditWallet = Omit<
  Wallet,
  | 'user_id'
  | 'type'
  | 'balance'
  | 'available_balance'
  | 'status'
  | 'created_at'
  | 'updated_at'
> &
  Pick<
    CreateTransactionData,
    | 'amount'
    | 'payment_method'
    | 'payment_type'
    | 'currency'
    | 'metadata'
    | 'description'
    | 'subscription_id'
    | 'community_id'
    | 'fee'
    | 'reference'
    | 'beneficiary_id'
    | 'conversion_rate'
  >;

export type DebitWallet = Pick<Wallet, 'id' | 'currency'> &
  Pick<
    CreateTransactionData,
    | 'amount'
    | 'payment_method'
    | 'payment_type'
    | 'currency'
    | 'metadata'
    | 'description'
    | 'subscription_id'
    | 'community_id'
    | 'fee'
    | 'reference'
    | 'beneficiary_id'
    | 'conversion_rate'
  >;

@injectable()
export class WalletService {
  private readonly qb = this.repo.createBuilder('wallets');

  constructor(
    @inject(MODULE_TOKENS.Repository)
    private readonly repo: Repository<Wallet>,
  ) {}

  public async create(data: CreateWalletData): Promise<Wallet> {
    return this.qb()
      .insert({ id: ulid(), ...data })
      .returning('*')
      .then(([val]) => val);
  }

  public async update(id: string, data: UpdateWalletData): Promise<Wallet> {
    return this.qb()
      .where({ id })
      .update(data)
      .returning('*')
      .then(([val]) => val);
  }

  public async get(id: string): Promise<Wallet> {
    return this.qb().where({ id }).first('*');
  }

  public async getOrCreate(
    user_id: string,
    currency: Currency,
    type: WalletType,
  ): Promise<Wallet> {
    const wallet = await this.qb()
      .where({ user_id, currency, type })
      .first('*');

    if (wallet) {
      return wallet;
    }

    return this.create({
      user_id,
      balance: 0,
      available_balance: 0,
      currency: Currency.USD,
      type,
      status: WalletStatus.ACTIVE,
    });
  }

  public async credit(
    data: CreditWallet,
  ): Promise<{ updatedWallet: Wallet; transaction: Transaction }> {
    return this.repo.kx.transaction(async (trx) => {
      const [updatedWallet] = await trx<Wallet>('wallets')
        .where({ id: data.id })
        .update(
          {
            balance: trx.raw('balance + ?', [data.amount]),
            available_balance: trx.raw('available_balance + ?', [data.amount]),
            updated_at: new Date(),
          },
          '*',
        );

      if (!updatedWallet) {
        throw new ApplicationError(
          StatusCodes.NOT_FOUND,
          'Wallet not found for credit operation',
        );
      }

      const [transaction] = await trx<Transaction>('transactions')
        .insert({
          id: ulid(),
          user_id: updatedWallet.user_id,
          wallet_id: updatedWallet.id,
          community_id: data.community_id,
          subscription_id: data?.subscription_id,
          beneficiary_id: data?.beneficiary_id,
          conversion_rate: data?.conversion_rate,
          status: TransactionStatus.SUCCESSFUL,
          amount: data.amount,
          fee: data?.fee ?? 0,
          currency: Currency.USD,
          payment_method: data.payment_method,
          reference: data.reference,
          payment_type: data.payment_type,
          type: TransactionType.CREDIT,
          description: data.description || 'Wallet Credit',
          metadata: data?.metadata,
          updated_at: new Date(),
        })
        .returning('*');

      return { updatedWallet, transaction };
    });
  }

  /**
   * Before using the function, ensure the amount is not less than 0
   *
   * */
  public async debit(
    data: DebitWallet,
  ): Promise<{ updatedWallet: Wallet; transaction: Transaction }> {
    return this.repo.kx.transaction(async (trx) => {
      const [updatedWallet] = await trx<Wallet>('wallets')
        .where({ id: data.id })
        .andWhere('balance', '>=', data.amount)
        .andWhere('available_balance', '>=', data.amount)
        .update(
          {
            balance: trx.raw('balance - ?', [data.amount]),
            available_balance: trx.raw('available_balance - ?', [data.amount]),
            updated_at: new Date(),
          },
          '*',
        );

      if (!updatedWallet) {
        throw new ApplicationError(
          StatusCodes.BAD_REQUEST,
          'Insufficient funds for this transaction',
        );
      }

      const [transaction] = await trx<Transaction>('transactions')
        .insert({
          id: ulid(),
          user_id: updatedWallet.user_id,
          wallet_id: updatedWallet.id,
          community_id: data.community_id,
          subscription_id: data?.subscription_id,
          beneficiary_id: data?.beneficiary_id,
          conversion_rate: data?.conversion_rate,
          status: TransactionStatus.SUCCESSFUL,
          amount: data.amount,
          fee: data?.fee ?? 0,
          currency: Currency.USD,
          payment_method: data.payment_method,
          reference: data.reference,
          payment_type: PaymentType.WITHDRAWAL,
          type: TransactionType.DEBIT,
          description: data.description || 'Wallet Debit',
          metadata: data?.metadata,
          updated_at: new Date(),
        })
        .returning('*');

      return { updatedWallet, transaction };
    });
  }

  /**
   * Confirm a wallet has enough cleared funds for a withdrawal
   * (debit amount + fee).
   *
   * @param wallet_id  – The wallet you want to test.
   * @param amount     – The intended debit in minor units (e.g. cents).
   * @param fee        – The withdrawal fee in the same units.
   * @returns          – true  → wallet can be debited,
   *                     false → insufficient funds / inactive wallet.
   */
  public async canWithdraw(
    wallet_id: string,
    amount: number,
    fee: number,
  ): Promise<boolean> {
    if (amount <= 0 || fee < 0) return false;

    const total = amount + fee;
    const wallet = await this.get(wallet_id);
    if (!wallet) return false;

    return (
      wallet.status === WalletStatus.ACTIVE &&
      wallet.balance >= total &&
      wallet.available_balance >= total
    );
  }
}
