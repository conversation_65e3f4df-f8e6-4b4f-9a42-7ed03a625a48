import { MODULE_TOKENS } from '@app/internal/ioc/tokens';
import { Repository } from '@app/internal/postgres/repository';
import { inject, injectable } from 'inversify';
import { DistributionStatus, Release } from './entities/release.entity';
import {
  OffsetPaginationOption,
  OffsetPaginationResult,
  OffsetPaginationResultBuilder,
} from '@app/internal/postgres/pagination';
import { Track } from '../track/entities/track.entity';
import { RequiredExcept } from '@app/internal/types';
import { ulid } from 'ulid';

export type CreateReleaseData = Omit<
  RequiredExcept<
    Release,
    | 'status'
    | 'in_distribution_queue'
    | 'label_id'
    | 'catalog_id'
    | 'was_previously_released'
    | 'release_code'
    | 'release_locals'
    | 'secondary_genre'
  >,
  'id' | 'created_at' | 'updated_at'
> & {
  tracks: Pick<Track, 'id'>[];
  artists: {
    id: string;
    role: number;
    is_primary: boolean;
    is_main: boolean;
  }[];
};

export type UpdateReleaseData = Partial<
  Omit<Release, 'id' | 'created_at'> & {
    artists: {
      id: string;
      role: number;
      is_primary: boolean;
      is_main: boolean;
      delete: boolean;
    }[];
  }
>;

@injectable()
export class ReleaseService {
  constructor(
    @inject(MODULE_TOKENS.Repository)
    private readonly repo: Repository<Release>,
  ) {}

  public async findManyWithRevelatorId(ids: string[]): Promise<Release[]> {
    const queryCond = [];

    const bindings = {};

    let idx = 0;

    for (const id of ids) {
      queryCond.push(`r.revelator_release_id = :rid${idx}`);

      bindings[`rid${idx}`] = id;

      idx++;
    }

    return this.repo
      .raw(
        `SELECT * FROM releases r WHERE ${queryCond.join(' OR ')} `,
        bindings,
      )
      .then(({ rows }) => rows);
  }

  public async create(data: CreateReleaseData): Promise<Release> {
    const { tracks, artists, ...rest } = data;

    return this.repo.kx.transaction(async (trx) => {
      const release = await trx('releases')
        .insert({ ...rest, id: ulid() })
        .returning('*')
        .then(([r]) => r);

      const releaseTrackValues = [];

      for (const track of tracks) {
        releaseTrackValues.push({
          track_id: track.id,
          release_id: release.id,
        });
      }

      await trx('release_tracks').insert(releaseTrackValues);

      if (artists.length > 0) {
        const releaseArtistValues = [];

        for (const artist of artists) {
          const is_primary = artist.is_main
            ? !!artist.is_main
            : !!artist.is_primary;

          releaseArtistValues.push({
            release_id: release.id,
            artist_id: artist.id,
            role: artist.role,
            is_primary,
            is_main: !!artist.is_main,
          });
        }

        await trx('release_artists').insert(releaseArtistValues);
      }

      return release;
    });
  }

  public async update(
    id: string,
    { artists, ...rest }: UpdateReleaseData,
  ): Promise<Release> {
    return this.repo.kx.transaction(async (trx) => {
      if (artists != null) {
        for (const artist of artists) {
          const existingRecord = await trx('release_artists')
            .where({ release_id: id, artist_id: artist.id })
            .first('*');

          const removeArtist = existingRecord && artist.delete;

          if (removeArtist) {
            await trx('release_artists')
              .where({ release_id: id, artist_id: artist.id })
              .delete();
          } else if (existingRecord) {
            await trx('release_artists')
              .where({ release_id: id, artist_id: artist.id })
              .update({
                role: artist.role,
                is_primary: artist.is_primary,
                is_main: artist.is_main,
              });
          } else {
            await trx('release_artists').insert({
              release_id: id,
              artist_id: artist.id,
              role: artist.role,
              is_primary: artist.is_primary,
              is_main: artist.is_main,
            });
          }
        }
      }

      const updatedRelease = await trx<Release>('releases')
        .where({ id })
        .update(rest)
        .returning('*')
        .then(([r]) => r);

      return updatedRelease;
    });
  }

  public async getById(id: string): Promise<
    Release & {
      tracks: Track[];
      artists: {
        id: string;
        role: number;
        is_primary: boolean;
        is_main: boolean;
      }[];
    }
  > {
    const query = `WITH creator_release AS (SELECT * FROM releases r WHERE r.id = :id),
                        tracks_on_release AS (SELECT rt.release_id AS release_id,
                                                     json_agg(t)   AS tracks
                                              FROM tracks t
                                                       JOIN release_tracks rt ON t.id = rt.track_id
                                                       JOIN creator_release cr ON rt.release_id = cr.id
                                              GROUP BY rt.release_id),
                        artists_on_release AS (SELECT ra.release_id AS release_id,
                                                      json_agg(json_build_object('id', a.id,
                                                                                 'name', a.name,
                                                                                 'role', ra.role,
                                                                                 'is_primary', ra.is_primary,
                                                                                 'is_main', ra.is_main,
                                                                                 'localized_names', a.localized_names,
                                                                                 'spotify_id', a.spotify_id,
                                                                                 'apple_music_id', a.apple_music_id,
                                                                                 'user_id', a.user_id,
                                                                                 'image', a.image,
                                                                                 'revelator_id', a.revelator_id,
                                                                                 'created_at', a.created_at,
                                                                                 'updated_at', a.updated_at
                                                               ))   AS artists
                                               FROM artists a
                                                        JOIN release_artists ra ON a.id = ra.artist_id
                                                        JOIN creator_release cr ON ra.release_id = cr.id
                                               GROUP BY ra.release_id)
                   SELECT cr.*, tor.tracks AS tracks, aor.artists AS artists
                   FROM creator_release cr
                            LEFT JOIN tracks_on_release tor ON cr.id = tor.release_id
                            LEFT JOIN artists_on_release aor ON cr.id = aor.release_id`;

    return this.repo.raw(query, { id }).then(({ rows }) => rows[0]);
  }

  public async get(
    id: string,
    user_id: string,
  ): Promise<
    Release & {
      tracks: Track[];
      artists: {
        id: string;
        role: number;
        is_primary: boolean;
        is_main: boolean;
      }[];
    }
  > {
    const query = `WITH creator_release AS (SELECT *
                                            FROM releases r
                                            WHERE r.id = :id
                                              AND r.user_id = :user_id),
                        tracks_on_release AS (SELECT rt.release_id AS release_id,
                                                     json_agg(t)   AS tracks
                                              FROM tracks t
                                                       JOIN release_tracks rt ON t.id = rt.track_id
                                                       JOIN creator_release cr ON rt.release_id = cr.id
                                              GROUP BY rt.release_id),
                        artists_on_release AS (SELECT ra.release_id AS release_id,
                                                      json_agg(json_build_object('id', a.id,
                                                                                 'name', a.name,
                                                                                 'role', ra.role,
                                                                                 'is_primary', ra.is_primary,
                                                                                 'is_main', ra.is_main,
                                                                                 'localized_names', a.localized_names,
                                                                                 'spotify_id', a.spotify_id,
                                                                                 'apple_music_id', a.apple_music_id,
                                                                                 'user_id', a.user_id,
                                                                                 'image', a.image,
                                                                                 'revelator_id', a.revelator_id,
                                                                                 'created_at', a.created_at,
                                                                                 'updated_at', a.updated_at
                                                               ))   AS artists
                                               FROM artists a
                                                        JOIN release_artists ra ON a.id = ra.artist_id
                                                        JOIN creator_release cr ON ra.release_id = cr.id
                                               GROUP BY ra.release_id)
                   SELECT cr.*, tor.tracks AS tracks, aor.artists AS artists
                   FROM creator_release cr
                            LEFT JOIN tracks_on_release tor ON cr.id = tor.release_id
                            LEFT JOIN artists_on_release aor ON cr.id = aor.release_id
    `;

    return this.repo.raw(query, { id, user_id }).then(({ rows }) => rows[0]);
  }

  public async getCommunityReleases(
    community_id: string,
    {
      page_number,
      result_per_page,
      status,
    }: OffsetPaginationOption & Partial<Pick<Release, 'status'>>,
  ) {
    const pageBuilder = new OffsetPaginationResultBuilder<Release>();

    const countParams: Record<string, any> = { community_id };
    const statusFilter = status ? 'WHERE r.status = :status' : '';

    if (status) {
      countParams.status = status;
    }

    const totalCount = await this.repo
      .raw(
        `SELECT count(r.id) AS total
       FROM releases r
       JOIN users u
         ON r.user_id = u.id AND u.community_id = :community_id ${statusFilter}`,
        countParams,
      )
      .then(({ rows }) => rows[0].total);

    if (!page_number) {
      page_number = 1;
    }

    if (!result_per_page) {
      result_per_page = 10;
    }

    const pager = pageBuilder
      .totalRecordCount(totalCount)
      .pageNumber(page_number)
      .resultPerPage(result_per_page);

    const query = `WITH community_releases AS (
                   SELECT r.*
                   FROM releases r
                   JOIN users u
                     ON r.user_id = u.id AND u.community_id = :community_id ${
                       status ? 'WHERE r.status = :status' : ''
                     }
                   ORDER BY r.created_at DESC
                   LIMIT :result_per_page OFFSET :offset
                 ),
                 tracks_on_release AS (
                   SELECT rt.release_id AS release_id,
                          json_agg(t) AS tracks
                   FROM tracks t
                   JOIN release_tracks rt ON t.id = rt.track_id
                   JOIN community_releases cr ON rt.release_id = cr.id
                   GROUP BY rt.release_id
                 ),
                 artists_on_release AS (
                   SELECT ra.release_id AS release_id,
                          json_agg(a) AS artists
                   FROM artists a
                   JOIN release_artists ra ON a.id = ra.artist_id
                   JOIN community_releases cr ON ra.release_id = cr.id
                   GROUP BY ra.release_id
                 )
                 SELECT cr.*, tor.tracks AS tracks, aor.artists AS artists
                 FROM community_releases cr
                 LEFT JOIN tracks_on_release tor ON cr.id = tor.release_id
                 LEFT JOIN artists_on_release aor ON cr.id = aor.release_id
                 ORDER BY cr.created_at DESC`;

    const releaseParams: Record<string, any> = {
      community_id,
      result_per_page,
      offset: pager.offset(),
    };

    if (status) {
      releaseParams.status = status;
    }

    const releases = await this.repo
      .raw(query, releaseParams)
      .then(({ rows }) => rows);

    return pager.build(releases);
  }

  public async getCreatorReleases(
    user_id: string,
    {
      page_number,
      result_per_page,
      status,
    }: OffsetPaginationOption & Partial<Pick<Release, 'status'>>,
  ): Promise<OffsetPaginationResult<Release>> {
    const pageBuilder = new OffsetPaginationResultBuilder<Release>();

    if (!page_number) {
      page_number = 1;
    }
    if (!result_per_page) {
      result_per_page = 10;
    }

    let statusFilter = '';
    const countParams: Record<string, any> = { user_id };
    if (status) {
      statusFilter = 'AND r.status = :status';
      countParams.status = status;
    }

    const totalCount = await this.repo
      .raw(
        `SELECT count(r.id) AS total
       FROM releases r
       WHERE r.user_id = :user_id ${statusFilter}`,
        countParams,
      )
      .then(({ rows }) => rows[0].total);

    const pager = pageBuilder
      .totalRecordCount(totalCount)
      .pageNumber(page_number)
      .resultPerPage(result_per_page);

    let releaseStatusFilter = '';
    const queryParams: Record<string, any> = {
      user_id,
      result_per_page,
      offset: pager.offset(),
    };
    if (status) {
      releaseStatusFilter = 'AND r.status = :status';
      queryParams.status = status;
    }

    const query = `WITH creator_releases AS (
                   SELECT *
                   FROM releases r
                   WHERE r.user_id = :user_id ${releaseStatusFilter}
                   ORDER BY r.created_at DESC
                   LIMIT :result_per_page OFFSET :offset
                 ),
                 tracks_on_release AS (
                   SELECT rt.release_id AS release_id,
                          json_agg(t) AS tracks
                   FROM tracks t
                   JOIN release_tracks rt ON t.id = rt.track_id
                   JOIN creator_releases cr ON rt.release_id = cr.id
                   GROUP BY rt.release_id
                 ),
                 artists_on_release AS (
                   SELECT ra.release_id AS release_id,
                          json_agg(a) AS artists
                   FROM artists a
                   JOIN release_artists ra ON a.id = ra.artist_id
                   JOIN creator_releases cr ON ra.release_id = cr.id
                   GROUP BY ra.release_id
                 )
                 SELECT cr.*, tor.tracks AS tracks, aor.artists AS artists
                 FROM creator_releases cr
                 LEFT JOIN tracks_on_release tor ON cr.id = tor.release_id
                 LEFT JOIN artists_on_release aor ON cr.id = aor.release_id
                 ORDER BY cr.created_at DESC`;

    const releases = await this.repo
      .raw(query, queryParams)
      .then(({ rows }) => rows);

    return pager.build(releases);
  }

  public async listReleases({
    search_term,
    status,
    user_id,
    community_id,
    page_number = 1,
    result_per_page = 10,
  }: OffsetPaginationOption &
    Partial<{
      search_term: string;
      user_id: string;
      community_id: string;
      status: DistributionStatus;
    }>): Promise<OffsetPaginationResult<Release>> {
    const pageBuilder = new OffsetPaginationResultBuilder<Release>();

    let countQuery = `
      SELECT count(r.id) AS total
      FROM releases r
    `;

    let searchQuery = `
      WITH matched_releases AS (
        SELECT r.*, json_build_object('id', u.id,
                                      'email', u.email,
                                      'first_name', u.first_name,
                                      'last_name', u.last_name,
                                      'display_name',
                                      u.display_name,
                                      'username', u.username,
                                      'category', u.category,
                                      'profile_picture', u.profile_picture) as creator
        FROM releases r
    `;

    const queryParams: Record<string, any> = {};

    if (user_id) {
      countQuery += ` JOIN users u ON r.user_id = u.id WHERE r.user_id = :user_id`;
      searchQuery += ` JOIN users u ON r.user_id = u.id WHERE r.user_id = :user_id`;
      queryParams.user_id = user_id;
    } else if (community_id) {
      countQuery += ` JOIN users u ON r.user_id = u.id AND u.community_id = :community_id`;
      searchQuery += ` JOIN users u ON r.user_id = u.id AND u.community_id = :community_id`;
      queryParams.community_id = community_id;
    }

    if (status) {
      if (user_id || community_id) {
        countQuery += ` AND r.status = :status`;
        searchQuery += ` AND r.status = :status`;
      } else {
        countQuery += ` WHERE r.status = :status`;
        searchQuery += ` WHERE r.status = :status`;
      }

      queryParams.status = status;
    }

    if (search_term) {
      const searchPattern = `%${search_term}%`;

      const searchCondition = `
      (r.id = :search_term OR
       r.title ILIKE :search_pattern OR
       r.revelator_release_id = :search_term)
    `;

      if (status || user_id || community_id) {
        countQuery += ` AND ${searchCondition}`;
        searchQuery += ` AND ${searchCondition}`;
      } else {
        countQuery += ` WHERE ${searchCondition}`;
        searchQuery += ` WHERE ${searchCondition}`;
      }

      queryParams.search_term = search_term;
      queryParams.search_pattern = searchPattern;
    }

    const totalCount = await this.repo
      .raw(countQuery, queryParams)
      .then(({ rows }) => rows[0].total);

    const pager = pageBuilder
      .totalRecordCount(totalCount)
      .pageNumber(page_number)
      .resultPerPage(result_per_page);

    searchQuery += `
      ORDER BY r.created_at DESC
      LIMIT :result_per_page
      OFFSET :offset
    ),
    tracks_on_release AS (
      SELECT rt.release_id AS release_id,
             json_agg(t) AS tracks
      FROM tracks t
      JOIN release_tracks rt ON t.id = rt.track_id
      JOIN matched_releases mr ON rt.release_id = mr.id
      GROUP BY rt.release_id
    ),
    artists_on_release AS (
      SELECT ra.release_id AS release_id,
             json_agg(a) AS artists
      FROM artists a
      JOIN release_artists ra ON a.id = ra.artist_id
      JOIN matched_releases mr ON ra.release_id = mr.id
      GROUP BY ra.release_id
    )
    SELECT mr.*, tor.tracks AS tracks, aor.artists AS artists
    FROM matched_releases mr
    LEFT JOIN tracks_on_release tor ON mr.id = tor.release_id
    LEFT JOIN artists_on_release aor ON mr.id = aor.release_id
    ORDER BY mr.created_at DESC
    `;

    queryParams.result_per_page = result_per_page;
    queryParams.offset = pager.offset();

    const result = await this.repo
      .raw(searchQuery, queryParams)
      .then(({ rows }) => rows);

    return pager.build(result);
  }
}
