import { inject, injectable } from 'inversify';
import { MODULE_TOKENS } from '@app/internal/ioc/tokens';
import { Repository } from '@app/internal/postgres/repository';
import { Label } from '@app/services/label/entitiy/label.entity';
import { ulid } from 'ulid';
import { PartialExcept } from '@app/internal/types';
import { Release } from '@app/services/release/entities/release.entity';
import {
  OffsetPaginationOption,
  OffsetPaginationResult,
  OffsetPaginationResultBuilder,
} from '@app/internal/postgres/pagination';

export type CreateLabelData = PartialExcept<
  Pick<Label, 'name' | 'description' | 'image' | 'revelator_id' | 'user_id'>,
  'name' | 'revelator_id' | 'user_id'
>;

@injectable()
export class LabelService {
  private readonly qb = this.repo.createBuilder('labels');

  constructor(
    @inject(MODULE_TOKENS.Repository) private readonly repo: Repository<Label>,
  ) {}

  public async create(data: CreateLabelData): Promise<Label> {
    return this.qb()
      .insert({
        ...data,
        id: ulid(),
      })
      .returning('*')
      .then(([l]) => l);
  }

  public async update(
    id: string,
    data: Partial<CreateLabelData>,
  ): Promise<Label> {
    return this.qb()
      .where({ id })
      .update(data)
      .returning('*')
      .then(([l]) => l);
  }

  public async get(id: string, user_id: string): Promise<Label> {
    return this.qb().where({ id }).andWhere({ user_id }).first('*');
  }

  public async releaseAppearedOn(id: string): Promise<Release[]> {
    return this.repo
      .raw(
        `SELECT r.*
         FROM releases r
                  JOIN labels l ON r.label_id = l.id
         WHERE l.id = :id`,
        { id },
      )
      .then(({ rows }) => rows);
  }

  public async getById(id: string): Promise<Label> {
    return this.qb().where({ id }).first('*');
  }

  public async getCreatorLabels(
    user_id: string,
    { page_number, result_per_page }: OffsetPaginationOption,
  ): Promise<OffsetPaginationResult<Label>> {
    const pageBuilder = new OffsetPaginationResultBuilder<Label>();

    const totalCount: number = await this.repo
      .raw(
        `SELECT count(l.id) AS total
         FROM labels l
         WHERE l.user_id = :user_id`,
        { user_id },
      )
      .then(({ rows }) => rows[0].total);

    if (!page_number) {
      page_number = 1;
    }

    if (!result_per_page) {
      result_per_page = 10;
    }

    const pager = pageBuilder
      .totalRecordCount(totalCount)
      .pageNumber(page_number)
      .resultPerPage(result_per_page);

    const query = `SELECT *
                   FROM labels l
                   WHERE user_id = :user_id
                   ORDER BY l.created_at DESC
                   LIMIT :result_per_page OFFSET :offset`;

    const labels = await this.repo
      .raw(query, {
        user_id,
        result_per_page,
        offset: pager.offset(),
      })
      .then(({ rows }) => rows);

    return pager.build(labels);
  }

  public async getAllLabelsInCommunity(
    community_id: string,
    { page_number, result_per_page }: OffsetPaginationOption,
  ): Promise<OffsetPaginationResult<Label>> {
    const pageBuilder = new OffsetPaginationResultBuilder<Label>();

    const totalCount = await this.repo
      .raw(
        `SELECT count(l.id) AS total
         FROM labels l
                  JOIN users u ON l.user_id = u.id
         WHERE u.community_id = :community_id`,
        { community_id },
      )
      .then(({ rows }) => rows[0].total);

    if (!page_number) {
      page_number = 1;
    }

    if (!result_per_page) {
      result_per_page = 10;
    }

    const pager = pageBuilder
      .totalRecordCount(totalCount)
      .pageNumber(page_number)
      .resultPerPage(result_per_page);

    const query = `SELECT l.*
                   FROM labels l
                            JOIN users u ON l.user_id = u.id
                   WHERE u.community_id = :community_id
                   ORDER BY l.created_at DESC
                   LIMIT :result_per_page OFFSET :offset`;

    const labels = await this.repo
      .raw(query, {
        community_id,
        result_per_page,
        offset: pager.offset(),
      })
      .then(({ rows }) => rows);

    return pager.build(labels);
  }

  public async searchLabels({
    page_number = 1,
    result_per_page = 10,
    search_term,
    user_id,
    community_id,
  }: OffsetPaginationOption &
    Partial<{
      search_term: string;
      user_id: string;
      community_id: string;
    }>): Promise<OffsetPaginationResult<Label>> {
    const pageBuilder = new OffsetPaginationResultBuilder<Label>();

    let countQuery = `
        SELECT count(l.id) AS total
        FROM labels l
    `;

    let searchQuery = `
        SELECT l.*,
               json_build_object('id', u.id,
                                 'email', u.email,
                                 'first_name', u.first_name,
                                 'last_name', u.last_name,
                                 'display_name',
                                 u.display_name,
                                 'username', u.username,
                                 'category', u.category,
                                 'profile_picture', u.profile_picture) as created_by
        FROM labels l
    `;

    const queryParams: Record<string, any> = {};

    if (user_id) {
      countQuery += ` JOIN users u ON l.user_id = u.id WHERE l.user_id = :user_id`;
      searchQuery += ` JOIN users u ON l.user_id = u.id WHERE l.user_id = :user_id`;
      queryParams.user_id = user_id;
    } else if (community_id) {
      countQuery += ` JOIN users u ON l.user_id = u.id AND u.community_id = :community_id`;
      searchQuery += ` JOIN users u ON l.user_id = u.id AND u.community_id = :community_id`;
      queryParams.community_id = community_id;
    }

    if (search_term) {
      const searchPattern = `%${search_term}%`;

      const searchCondition = `
      (l.id = :search_term OR
       l.name ILIKE :search_pattern OR
       l.revelator_id = :search_term)
    `;

      if (user_id || community_id) {
        countQuery += ` AND ${searchCondition}`;
        searchQuery += ` AND ${searchCondition}`;
      } else {
        countQuery += ` WHERE ${searchCondition}`;
        searchQuery += ` WHERE ${searchCondition}`;
      }

      queryParams.search_term = search_term;
      queryParams.search_pattern = searchPattern;
    }

    const totalCount = await this.repo
      .raw(countQuery, queryParams)
      .then(({ rows }) => rows[0].total);

    const pager = pageBuilder
      .totalRecordCount(totalCount)
      .pageNumber(page_number)
      .resultPerPage(result_per_page);

    searchQuery += `
      ORDER BY l.created_at DESC
      LIMIT :result_per_page
      OFFSET :offset
    `;

    queryParams.result_per_page = result_per_page;
    queryParams.offset = pager.offset();

    const result = await this.repo
      .raw(searchQuery, queryParams)
      .then(({ rows }) => rows);

    return pager.build(result);
  }
}
