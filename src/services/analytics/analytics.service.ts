import { inject, injectable } from 'inversify';
import { MODULE_TOKENS } from '@app/internal/ioc/tokens';
import { Repository } from '@app/internal/postgres/repository';
import {
  subDays,
  differenceInMilliseconds,
  format,
  subMilliseconds,
} from 'date-fns';

export type CommunitySubPlanTotalityAnalyticsParams = {
  community_id: string;
};

export type PlanAnalyticsTotalityData = {
  plan_id: string;
  plan_name: string;
  subscribers: number;
  subscriber_growth_rate: number;
  subscriber_percentage: number;
  total_subscribers: number;
  total_subscriber_growth: number;
};

export type RevenueAnalyticsTotalityData = {
  plan_id: string;
  plan_name: string;
  revenue: number;
  revenue_growth_rate: number;
  revenue_percentage: number;
  total_revenue: number;
  total_revenue_growth: number;
};

export type CommunitySubPlanTotalityAnalyticsData = {
  total: number;
  growth_rate: number;
  stats: PlanAnalyticsTotalityData[];
};

export type CommunitySubRevenueTotalityAnalyticsData = {
  total: number;
  growth_rate: number;
  stats: RevenueAnalyticsTotalityData[];
};

export type CommunitySubscriberStatSummaryParams = {
  community_id: string;
  from: Date;
  to: Date;
};

export type CommunitySubscriberStatSummary = {
  total_subscribers: number;
  total_subscriber_growth_rate: number;
  active_subscribers: number;
  active_subscriber_growth_rate: number;
  new_subscribers: number;
  new_subscriber_growth_rate: number;
  cancelled_subscribers: number;
  cancelled_subscriber_growth_rate: number;
  pending_subscribers: number;
  pending_subscriber_growth_rate: number;
};

export type AgeDemographicsData = {
  age_group: string;
  subscribers: number;
  percentage: number;
  total: number;
};

export type GenderDemographicsData = {
  gender: string;
  subscribers: number;
  percentage: number;
  total: number;
};

export type CountryStatsSummary = {
  country: string;
  subscribers: number;
  percentage: number;
  total: number;
};

export type DemographicsAnalyticsParams = {
  community_id: string;
  plan_id?: string;
  status?: 'active' | 'cancelled' | 'paused';
};

type CountryAnalyticsParams = {
  community_id: string;
  plan_id?: string;
  status?: 'active' | 'cancelled' | 'paused';
};

export type SubscriberGrowthTrendParams = {
  community_id: string;
  plan_id?: string;
  status?: 'active' | 'cancelled' | 'paused';
  plan_interval?: 'monthly' | 'annual';
  start_date: Date;
  end_date: Date;
  interval: 'day' | 'week' | 'month' | 'year';
};

@injectable()
export class AnalyticsService {
  constructor(
    @inject(MODULE_TOKENS.Repository)
    private readonly repo: Repository<any>,
  ) {}

  public async communitySubPlanTotalityAnalytics({
    community_id,
  }: CommunitySubPlanTotalityAnalyticsParams) {
    const query = `with cp as (select sp.id                                 as plan_id,
                                      sp.name                               as plan_name,
                                      sp.metadata -> 'pricing' -> 'monthly' as monthly_pricing_id,
                                      sp.metadata -> 'pricing' -> 'annual'  as annual_pricing_id
                               from subscription_plans sp
                               where community_id = :community_id),
                        sd as (select cp.plan_id          as plan_id,
                                      s.user_id           as subscriber_id,
                                      s.last_payment_date as last_payment_date
                               from cp
                                        join subscriptions s on
                                   (s.metadata -> 'price_id' = cp.monthly_pricing_id or
                                    s.metadata -> 'price_id' = cp.annual_pricing_id)),
                        curr as (select sd.plan_id,
                                        count(sd.subscriber_id) as subscribers
                                 from sd
                                 where sd.last_payment_date >= current_date - interval '1 month'
                                 group by sd.plan_id),
                        prev as (select sd.plan_id, count(sd.subscriber_id) as subscribers
                                 from sd
                                 where last_payment_date >= current_date - interval '2 months'
                                   and last_payment_date < current_date - interval '1 month'
                                 group by sd.plan_id),
                        total_curr as (select SUM(curr.subscribers) as total_subscribers from curr),
                        total_prev as (select SUM(prev.subscribers) as total_subscribers from prev),
                        f as (select cp.plan_id                    as plan_id,
                                     cp.plan_name                  as plan_name,
                                     coalesce(curr.subscribers, 0) as subscribers,
                                     (coalesce((curr.subscribers - prev.subscribers) / nullif(prev.subscribers, 0), 0) *
                                      100)::numeric(13, 2)         as subscriber_growth_rate
                              from cp
                                       left join prev
                                                 on prev.plan_id = cp.plan_id
                                       left join curr on curr.plan_id = cp.plan_id)
                   select f.*,
                          (coalesce(f.subscribers / nullif(tc.total_subscribers, 0), 0) * 100)::numeric(13, 2) as subscriber_percentage,
                          tc.total_subscribers                                                                 as total_subscribers,
                          (coalesce(abs(tc.total_subscribers - tp.total_subscribers) / nullif(tp.total_subscribers, 0),
                                    0) *
                           100)::numeric(13, 2)                                                                as total_subscriber_growth
                   from f,
                        total_curr tc,
                        total_prev tp
                   order by f.subscribers desc`;

    const stats = await this.repo
      .raw(query, { community_id })
      .then(({ rows }) => <PlanAnalyticsTotalityData[]>rows);

    const data: CommunitySubPlanTotalityAnalyticsData = {
      total: stats.length > 0 ? stats[0].total_subscribers : 0,
      growth_rate: stats.length > 0 ? stats[0].total_subscriber_growth : 0,
      stats,
    };

    return data;
  }

  public async communitySubRevenueTotalityAnalytics({
    community_id,
  }: CommunitySubPlanTotalityAnalyticsParams) {
    const query = `with cp as (select sp.id                                 as plan_id,
                                      sp.name                               as plan_name,
                                      sp.price_per_month                    as price_per_month,
                                      sp.price_per_annum                    as price_per_annum,
                                      sp.metadata -> 'pricing' -> 'monthly' as monthly_pricing_id,
                                      sp.metadata -> 'pricing' -> 'annual'  as annual_pricing_id
                               from subscription_plans sp
                               where community_id = :community_id),
                        sd as (select cp.plan_id          as plan_id,
                                      s.user_id           as subscriber_id,
                                      case
                                          when s.metadata -> 'price_id' = cp.monthly_pricing_id then cp.price_per_month
                                          when s.metadata -> 'price_id' = cp.annual_pricing_id then cp.price_per_annum
                                          end             as price,
                                      s.last_payment_date as last_payment_date
                               from cp
                                        join subscriptions s on
                                   (s.metadata -> 'price_id' = cp.monthly_pricing_id or
                                    s.metadata -> 'price_id' = cp.annual_pricing_id)),
                        curr as (select sd.plan_id,
                                        sum(sd.price) as revenue
                                 from sd
                                 where sd.last_payment_date >= current_date - interval '1 month'
                                 group by sd.plan_id),
                        prev as (select sd.plan_id, sum(sd.price) as revenue
                                 from sd
                                 where last_payment_date >= current_date - interval '2 months'
                                   and last_payment_date < current_date - interval '1 month'
                                 group by sd.plan_id),
                        total_curr as (select SUM(curr.revenue) as total_revenue from curr),
                        total_prev as (select SUM(prev.revenue) as total_revenue from prev),
                        f as (select cp.plan_id                as plan_id,
                                     cp.plan_name              as plan_name,
                                     coalesce(curr.revenue, 0) as revenue,
                                     (coalesce((curr.revenue - prev.revenue) / nullif(prev.revenue, 0), 0) *
                                      100)::numeric(13, 2)     as revenue_growth_rate
                              from cp
                                       left join prev
                                                 on prev.plan_id = cp.plan_id
                                       left join curr on curr.plan_id = cp.plan_id)
                   select f.*,
                          (coalesce(f.revenue / nullif(tc.total_revenue, 0), 0) * 100)::numeric(13, 2) as revenue_percentage,
                          tc.total_revenue                                                             as total_revenue,
                          (coalesce(abs(tc.total_revenue - tp.total_revenue) / nullif(tp.total_revenue, 0),
                                    0) *
                           100)::numeric(13, 2)                                                        as total_revenue_growth
                   from f,
                        total_curr tc,
                        total_prev tp
                   order by f.revenue desc`;

    const stats = await this.repo
      .raw(query, { community_id })
      .then(({ rows }) => rows);

    const data: CommunitySubRevenueTotalityAnalyticsData = {
      total: stats.length > 0 ? stats[0].total_revenue : 0,
      growth_rate: stats.length > 0 ? stats[0].total_revenue_growth : 0,
      stats,
    };

    return data;
  }

  public async communitySubscriberStatSummary({
    community_id,
    from,
    to,
  }: CommunitySubscriberStatSummaryParams): Promise<CommunitySubscriberStatSummary> {
    const query = `with cp as (select sp.id                                 as plan_id,
                                      sp.metadata -> 'pricing' -> 'monthly' as monthly_pricing_id,
                                      sp.metadata -> 'pricing' -> 'annual'  as annual_pricing_id
                               from subscription_plans sp
                               where community_id = :community_id),
                        sd as (select s.user_id           as subscriber_id,
                                      s.status            as subscription_status,
                                      s.created_at        as created_at,
                                      s.last_payment_date as last_payment_date
                               from cp
                                        join subscriptions s on
                                   (s.metadata -> 'price_id' = cp.monthly_pricing_id or
                                    s.metadata -> 'price_id' = cp.annual_pricing_id)),
                        curr as (select count(sd.subscriber_id) as subscribers
                                 from sd
                                 where sd.last_payment_date between :from_date and :to_date),
                        prev as (select count(sd.subscriber_id) as subscribers
                                 from sd
                                 where last_payment_date between :prev_from_date and :prev_to_date),
                        active as (select count(sd.subscriber_id) as subscribers
                                   from sd
                                   where sd.last_payment_date between :from_date and :to_date
                                     and sd.subscription_status = 'active'),
                        active_prev as (select count(sd.subscriber_id) as subscribers
                                        from sd
                                        where last_payment_date between :prev_from_date and :prev_to_date
                                          and subscription_status = 'active'),
                        paused as (select COUNT(subscriber_id) AS subscribers
                                   from sd
                                   where last_payment_date between :from_date and :to_date
                                     and subscription_status = 'paused'),
                        paused_prev as (select COUNT(subscriber_id) as subscribers
                                        from sd
                                        where last_payment_date between :prev_from_date and :prev_to_date
                                          and subscription_status = 'paused'),

                        cancelled as (select COUNT(subscriber_id) AS subscribers
                                      from sd
                                      where last_payment_date between :from_date and :to_date
                                        and subscription_status = 'cancelled'),
                        cancelled_prev AS (select COUNT(subscriber_id) AS subscribers
                                           from sd
                                           where last_payment_date between :prev_from_date and :prev_to_date
                                             and subscription_status = 'cancelled'),

                        new_subscribers as (select count(sd.subscriber_id) AS subscribers
                                            from sd
                                            where date(sd.created_at) = date(sd.last_payment_date)
                                              and sd.last_payment_date between :from_date and :to_date),
                        new_subscribers_prev as (select count(sd.subscriber_id) AS subscribers
                                                 from sd
                                                 where date(sd.created_at) = date(sd.last_payment_date)
                                                   and sd.last_payment_date between :prev_from_date and :prev_to_date)


                   select c.subscribers         as total_subscribers,
                          (coalesce(abs(c.subscribers - p.subscribers) / nullif(p.subscribers, 0), 0) *
                           100)::numeric(13, 2) as total_subscriber_growth,

                          a.subscribers         as active_subscribers,
                          (coalesce(abs(a.subscribers - ap.subscribers) / nullif(ap.subscribers, 0), 0) *
                           100)::numeric(13, 2) as active_subscriber_growth,

                          pd.subscribers        as paused_subscribers,
                          (coalesce(abs(pd.subscribers - pdp.subscribers) / nullif(pdp.subscribers, 0), 0) *
                           100)::numeric(13, 2) as paused_subscriber_growth,

                          cd.subscribers        as cancelled_subscribers,
                          (coalesce(abs(cd.subscribers - cdp.subscribers) / nullif(cdp.subscribers, 0), 0) *
                           100)::numeric(13, 2) as cancelled_subscriber_growth,

                          ns.subscribers        as new_subscribers,
                          (coalesce(abs(ns.subscribers - nsp.subscribers) / nullif(nsp.subscribers, 0), 0) *
                           100)::numeric(13, 2) as new_subscriber_growth
                   from curr c,
                        prev p,
                        active a,
                        active_prev ap,
                        paused pd,
                        paused_prev pdp,
                        cancelled cd,
                        cancelled_prev cdp,
                        new_subscribers ns,
                        new_subscribers_prev nsp`;

    const duration = differenceInMilliseconds(to, from);

    const prevTo = subDays(from, 1);

    const prevFrom = subMilliseconds(prevTo, duration);

    const prevFromDate = format(prevFrom, 'yyyy-MM-dd');
    const prevToDate = format(prevTo, 'yyyy-MM-dd');

    return this.repo
      .raw(query, {
        community_id,
        from_date: from,
        to_date: to,
        prev_from_date: prevFromDate,
        prev_to_date: prevToDate,
      })
      .then(({ rows }) => rows);
  }

  public async subscriberDemographics({
    community_id,
    plan_id,
    status,
  }: DemographicsAnalyticsParams) {
    const binding: Record<string, any> = {
      community_id,
    };
    const whereClause = [];

    if (status) {
      whereClause.push(`s.status = :status`);
      binding.status = status;
    }

    if (plan_id) {
      whereClause.push(`cp.plan_id = :plan_id`);
      binding.plan_id = plan_id;
    }

    const ageDemographicQuery = `with cp as (select sp.id                                 as plan_id,
                                                    sp.metadata -> 'pricing' -> 'monthly' as monthly_pricing_id,
                                                    sp.metadata -> 'pricing' -> 'annual'  as annual_pricing_id
                                             from subscription_plans sp
                                             where community_id = :community_id),
                                      ud as (select u.id    as user_id,
                                                    case
                                                        when date_of_birth is null then 'unknown'
                                                        when date_of_birth <= current_date - interval '65 years'
                                                            then '65-'
                                                        when date_of_birth > current_date - interval '65 years' and
                                                             date_of_birth <= current_date - interval '45 years'
                                                            then '45-64'
                                                        when date_of_birth > current_date - interval '45 years' and
                                                             date_of_birth <= current_date - interval '35 years'
                                                            then '35-44'
                                                        when date_of_birth > current_date - interval '35 years' and
                                                             date_of_birth <= current_date - interval '25 years'
                                                            then '25-34'
                                                        when date_of_birth > current_date - interval '25 years' and
                                                             date_of_birth <= current_date - interval '18 years'
                                                            then '18-24'
                                                        when date_of_birth > current_date - interval '18 years' and
                                                             date_of_birth <= current_date - interval '13 years'
                                                            then '13-17'
                                                        else 'unknown'
                                                        end as age_group
                                             from cp
                                                      join subscriptions s on
                                                 (s.metadata -> 'price_id' = cp.monthly_pricing_id or
                                                  s.metadata -> 'price_id' = cp.annual_pricing_id)
                                                      join users u on u.id = s.user_id
                                                 ${
                                                   whereClause.length > 0
                                                     ? `where ${whereClause.join(
                                                         ' and ',
                                                       )}`
                                                     : ''
                                                 }),
                                      age_groups as (select '65-' as age_group
                                                     union all
                                                     select '45-64'
                                                     union all
                                                     select '35-44'
                                                     union all
                                                     select '25-34'
                                                     union all
                                                     select '18-24'
                                                     union all
                                                     select '13-17'
                                                     union all
                                                     select 'unknown'),
                                      total as (select count(ud.user_id)::numeric(13, 2) as subscribers
                                                from ud),
                                      gg as (select ud.age_group, count(ud.user_id)::numeric(13, 2) as subscribers
                                             from ud
                                             group by ud.age_group),
                                      final as (select ag.age_group,
                                                       coalesce(gg.subscribers, 0) as subscribers
                                                from age_groups ag
                                                         left join gg on ag.age_group = gg.age_group)
                                 select final.age_group,
                                        final.subscribers,
                                        (coalesce(final.subscribers / nullif(total.subscribers, 0), 0) * 100)::numeric(13, 2) as percentage,
                                        total.subscribers                                                                     as total
                                 from final,
                                      total`;

    const ageDemographicQueryData = await this.repo
      .raw(ageDemographicQuery, binding)
      .then(({ rows }) => <AgeDemographicsData[]>rows);

    const genderDemographicQuery = `with cp as (select sp.id                                 as plan_id,
                                                       sp.metadata -> 'pricing' -> 'monthly' as monthly_pricing_id,
                                                       sp.metadata -> 'pricing' -> 'annual'  as annual_pricing_id
                                                from subscription_plans sp
                                                where community_id = :community_id),
                                         ud as (select u.id                          as user_id,
                                                       coalesce(u.gender, 'unknown') as gender
                                                from cp
                                                         join subscriptions s on
                                                    (s.metadata -> 'price_id' = cp.monthly_pricing_id or
                                                     s.metadata -> 'price_id' = cp.annual_pricing_id)
                                                         join users u on u.id = s.user_id
                                                    ${
                                                      whereClause.length > 0
                                                        ? `where ${whereClause.join(
                                                            ' and ',
                                                          )}`
                                                        : ''
                                                    }),
                                         total as (select count(ud.user_id)::numeric(13, 2) as subscribers from ud),
                                         gg as (select ud.gender, count(ud.user_id)::numeric(13, 2) as subscribers
                                                from ud
                                                group by ud.gender),
                                         gc AS (SELECT 'male' AS gender
                                                UNION ALL
                                                SELECT 'female'
                                                UNION ALL
                                                SELECT 'unknown'),
                                         f as (select gc.gender, coalesce(gg.subscribers, 0) as subscribers
                                               from gc
                                                        left join gg on gg.gender = gc.gender)
                                    select f.gender                                                                          as gender,
                                           f.subscribers                                                                     as subscribers,
                                           (coalesce(f.subscribers / nullif(total.subscribers, 0), 0) * 100)::numeric(13, 2) as percentage,
                                           total.subscribers                                                                 as total
                                    from f,
                                         total
                                    order by f.subscribers desc`;

    const genderDemographicsData = await this.repo
      .raw(genderDemographicQuery, binding)
      .then(({ rows }) => <GenderDemographicsData[]>rows);

    return {
      age_demographics: ageDemographicQueryData,
      gender_demographics: genderDemographicsData,
    };
  }

  public async subscriberCountryAnalytics({
    community_id,
    plan_id,
    status,
  }: CountryAnalyticsParams) {
    const binding: Record<string, any> = {
      community_id,
    };
    const whereClause = [];

    if (status) {
      whereClause.push(`s.status = :status`);
      binding.status = status;
    }

    if (plan_id) {
      whereClause.push(`cp.plan_id = :plan_id`);
      binding.plan_id = plan_id;
    }

    const query = `with cp as (select sp.id                                 as plan_id,
                                      sp.metadata -> 'pricing' -> 'monthly' as monthly_pricing_id,
                                      sp.metadata -> 'pricing' -> 'annual'  as annual_pricing_id
                               from subscription_plans sp
                               where community_id = :community_id),
                        ud as (select u.id                           as user_id,
                                      coalesce(u.country, 'unknown') as country
                               from cp
                                        join subscriptions s on
                                   (s.metadata -> 'price_id' = cp.monthly_pricing_id or
                                    s.metadata -> 'price_id' = cp.annual_pricing_id)
                                        join users u on u.id = s.user_id
                                   ${
                                     whereClause.length > 0
                                       ? `where ${whereClause.join(' and ')}`
                                       : ''
                                   }),
                        total as (select count(ud.user_id)::numeric(13, 2) as subscribers
                                  from ud),
                        gg as (select ud.country, count(ud.user_id)::numeric(13, 2) as subscribers
                               from ud
                               group by ud.country)
                   select gg.country,
                          gg.subscribers,
                          (coalesce(gg.subscribers / nullif(total.subscribers, 0), 0) * 100)::numeric(13, 2) as percentage,
                          total.subscribers                                                                  as total
                   from gg,
                        total
                   order by gg.subscribers desc`;

    const country_stats = await this.repo
      .raw(query, binding)
      .then(({ rows }) => <CountryStatsSummary[]>rows);

    return {
      total_subscribers: country_stats.length > 0 ? country_stats[0].total : 0,
      country_stats,
    };
  }

  public async subscriptionRevenueCountryAnalytics({
    community_id,
    plan_id,
    status,
  }: CountryAnalyticsParams) {
    const binding: Record<string, any> = {
      community_id,
    };
    const whereClause = [];

    if (status) {
      whereClause.push(`s.status = :status`);
      binding.status = status;
    }

    if (plan_id) {
      whereClause.push(`cp.plan_id = :plan_id`);
      binding.plan_id = plan_id;
    }

    const query = `with cp as (select sp.id                                 as plan_id,
                                      sp.price_per_month                    as price_per_month,
                                      sp.price_per_annum                    as price_per_annum,
                                      sp.metadata -> 'pricing' -> 'monthly' as monthly_pricing_id,
                                      sp.metadata -> 'pricing' -> 'annual'  as annual_pricing_id
                               from subscription_plans sp
                               where community_id = :community_id),
                        ud as (select u.id                           as user_id,
                                      case
                                          when s.metadata -> 'price_id' = cp.monthly_pricing_id then cp.price_per_month
                                          when s.metadata -> 'price_id' = cp.annual_pricing_id then cp.price_per_annum
                                          end                        as price,
                                      coalesce(u.country, 'unknown') as country
                               from cp
                                        join subscriptions s on
                                   (s.metadata -> 'price_id' = cp.monthly_pricing_id or
                                    s.metadata -> 'price_id' = cp.annual_pricing_id)
                                        join users u on u.id = s.user_id
                                   ${
                                     whereClause.length > 0
                                       ? `where ${whereClause.join(' and ')}`
                                       : ''
                                   }),
                        total as (select sum(ud.price) as revenue
                                  from ud),
                        gg as (select ud.country, sum(ud.price) as revenue
                               from ud
                               group by ud.country)
                   select gg.country,
                          gg.revenue,
                          (coalesce(gg.revenue / nullif(total.revenue, 0), 0) * 100)::numeric(13, 2) as percentage,
                          total.revenue                                                              as total
                   from gg,
                        total
                   order by gg.revenue desc`;

    const country_stats = await this.repo
      .raw(query, binding)
      .then(({ rows }) => <CountryStatsSummary[]>rows);

    return {
      total_revenue: country_stats.length > 0 ? country_stats[0].total : 0,
      country_stats,
    };
  }

  public async subscriberGrowthTrend({
    community_id,
    start_date,
    end_date,
    interval,
    plan_interval,
    plan_id,
    status,
  }: SubscriberGrowthTrendParams) {
    const binding: Record<string, any> = {
      community_id,
      start_date,
      end_date,
      interval,
    };
    const whereClause = [];

    if (plan_interval) {
      whereClause.push(`sd.interval = :plan_interval`);
      binding.plan_interval = plan_interval;
    }

    if (status) {
      whereClause.push(`sd.subscription_status = :status`);
      binding.status = status;
    }

    if (plan_id) {
      whereClause.push(`sd.plan_id = :plan_id`);
      binding.plan_id = plan_id;
    }

    const query = `with cp as (select sp.id                                 as plan_id,
                                      sp.metadata -> 'pricing' -> 'monthly' as monthly_pricing_id,
                                      sp.metadata -> 'pricing' -> 'annual'  as annual_pricing_id
                               from subscription_plans sp
                               where community_id = :community_id),
                        sd as (select u.id                as user_id,
                                      s.last_payment_date as last_payment_date,
                                      s.status            as subscription_status,
                                      case
                                          when s.metadata -> 'price_id' = cp.monthly_pricing_id
                                              then 'monthly'
                                          when s.metadata -> 'price_id' = cp.annual_pricing_id
                                              then 'annual'
                                          end             as interval
                               from cp
                                        join subscriptions s on
                                   (s.metadata -> 'price_id' = cp.monthly_pricing_id or
                                    s.metadata -> 'price_id' = cp.annual_pricing_id)
                                        join users u on u.id = s.user_id),
                        tsd as (select date_trunc(:interval, sd.last_payment_date) as period,
                                       count(sd.user_id)                           as subscribers
                                from sd
                                where sd.last_payment_date between :start_date and :end_date ${
                                  whereClause.length > 0
                                    ? `and ${whereClause.join(' and ')}`
                                    : ''
                                }
                                group by period
                                order by period)

                   select date(tsd.period)                                     as peroid,
                          tsd.subscribers,
                          sum(tsd.subscribers) over (rows unbounded preceding) as total
                   from tsd`;

    return this.repo.raw(query, binding).then(({ rows }) => rows);
  }

  public async subscriptionRevenueGrowthTrend({
    community_id,
    start_date,
    end_date,
    interval,
    plan_interval,
    plan_id,
    status,
  }: SubscriberGrowthTrendParams) {
    const binding: Record<string, any> = {
      community_id,
      start_date,
      end_date,
      interval,
    };
    const whereClause = [];

    if (plan_interval) {
      whereClause.push(`sd.interval = :plan_interval`);
      binding.plan_interval = plan_interval;
    }

    if (status) {
      whereClause.push(`sd.subscription_status = :status`);
      binding.status = status;
    }

    if (plan_id) {
      whereClause.push(`sd.plan_id = :plan_id`);
      binding.plan_id = plan_id;
    }

    const query = `with cp as (select sp.id                                 as plan_id,
                                      sp.price_per_month                    as price_per_month,
                                      sp.price_per_annum                    as price_per_annum,
                                      sp.metadata -> 'pricing' -> 'monthly' as monthly_pricing_id,
                                      sp.metadata -> 'pricing' -> 'annual'  as annual_pricing_id
                               from subscription_plans sp
                               where community_id = :community_id),
                        sd as (select u.id                as user_id,
                                      s.last_payment_date as last_payment_date,
                                      s.status            as subscription_status,
                                      case
                                          when s.metadata -> 'price_id' = cp.monthly_pricing_id
                                              then 'monthly'
                                          when s.metadata -> 'price_id' = cp.annual_pricing_id
                                              then 'annual'
                                          end             as interval,
                                      case
                                          when s.metadata -> 'price_id' = cp.monthly_pricing_id
                                              then cp.price_per_month
                                          when s.metadata -> 'price_id' = cp.annual_pricing_id
                                              then cp.price_per_annum
                                          end             as price
                               from cp
                                        join subscriptions s on
                                   (s.metadata -> 'price_id' = cp.monthly_pricing_id or
                                    s.metadata -> 'price_id' = cp.annual_pricing_id)
                                        join users u on u.id = s.user_id),
                        tsd as (select date_trunc(:interval, sd.last_payment_date) as period,
                                       sum(sd.price)                               as revenue
                                from sd
                                where sd.last_payment_date between :start_date and :end_date ${
                                  whereClause.length > 0
                                    ? `and ${whereClause.join(' and ')}`
                                    : ''
                                }
                                group by period
                                order by period)

                   select date(tsd.period)                                     as peroid,
                          tsd.revenue,
                          sum(tsd.revenue) over (rows unbounded preceding) as total
                   from tsd`;

    return this.repo.raw(query, binding).then(({ rows }) => rows);
  }
}
