import { BaseEntity } from '@app/internal/postgres/base.entity';
import { Role } from '@app/services/user/entities/user.entity';

export enum InviteStatus {
  pending = 'pending',
  accepted = 'accepted',
}

export class Invitation extends BaseEntity<Invitation> {
  email: string;
  community_id: string;
  team_id: string;
  role: Role;
  permissions: object[];
  expiry: Date;
  status: InviteStatus;
}
