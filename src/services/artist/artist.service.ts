import { MODULE_TOKENS } from '@app/internal/ioc/tokens';
import { Repository } from '@app/internal/postgres/repository';
import { inject, injectable } from 'inversify';
import { Artist } from './entity/artist.entity';
import { ulid } from 'ulid';
import { LocalizedName } from '../track/entities/track.entity';
import {
  OffsetPaginationOption,
  OffsetPaginationResult,
  OffsetPaginationResultBuilder,
} from '@app/internal/postgres/pagination';

export type CreateArtistData = Partial<
  Omit<Artist, 'created_at' | 'updated_at' | 'id'>
>;

@injectable()
export class ArtistService {
  private readonly qb = this.repo.createBuilder('artists');

  constructor(
    @inject(MODULE_TOKENS.Repository) private readonly repo: Repository<Artist>,
  ) {}

  public async getById(id: string): Promise<Artist> {
    return this.qb().where({ id }).first('*');
  }

  public async getManyByRevelatorId(ids: string[]): Promise<Artist[]> {
    const queryCond = [];

    const bindings = {};

    let idx = 0;

    for (const id of ids) {
      queryCond.push(`a.revelator_id = :arid${idx}`);

      bindings[`arid${idx}`] = id;

      idx++;
    }

    return this.repo
      .raw(
        `SELECT *
            FROM artists a
            WHERE ${queryCond.join(' OR ')}`,
        bindings,
      )
      .then(({ rows }) => rows);
  }

  public async getManyById(ids: string[]): Promise<Artist[]> {
    const queryCond = [];

    const bindings = {};

    let idx = 0;

    for (const id of ids) {
      queryCond.push(`a.id = :aid${idx}`);

      bindings[`aid${idx}`] = id;

      idx++;
    }

    return this.repo
      .raw(
        `SELECT *
            FROM artists a
            WHERE ${queryCond.join(' OR ')}`,
        bindings,
      )
      .then(({ rows }) => rows);
  }

  public async getCommunityArtists(
    community_id: string,
    { page_number, result_per_page }: OffsetPaginationOption,
  ): Promise<OffsetPaginationResult<Artist>> {
    const pageBuilder = new OffsetPaginationResultBuilder<Artist>();

    const totalCount: number = await this.repo
      .raw(
        `SELECT count(*) AS total
         FROM artists a
                  JOIN users u ON a.user_id = u.id AND u.community_id = :community_id`,
        { community_id },
      )
      .then(({ rows }) => rows[0].total);

    if (!page_number) {
      page_number = 1;
    }

    if (!result_per_page) {
      result_per_page = 10;
    }

    const pager = pageBuilder
      .totalRecordCount(totalCount)
      .pageNumber(page_number)
      .resultPerPage(result_per_page);

    const result = await this.repo
      .raw(
        `SELECT a.*
         FROM artists a
                  JOIN users u ON a.user_id = u.id AND u.community_id = :community_id
         ORDER BY a.created_at DESC
         LIMIT :result_per_page OFFSET :offset`,
        { community_id, result_per_page, offset: pager.offset() },
      )
      .then(({ rows }) => rows);

    return pager.build(result);
  }

  public async getUserArtists(
    user_id: string,
    { page_number, result_per_page }: OffsetPaginationOption,
  ): Promise<OffsetPaginationResult<Artist>> {
    const pageBuilder = new OffsetPaginationResultBuilder<Artist>();

    const totalCount: number = await this.repo
      .raw(
        `SELECT count(*) AS total
         FROM artists a
         WHERE a.user_id = :user_id`,
        { user_id },
      )
      .then(({ rows }) => rows[0].total);

    if (!page_number) {
      page_number = 1;
    }

    if (!result_per_page) {
      result_per_page = 10;
    }

    const pager = pageBuilder
      .totalRecordCount(totalCount)
      .pageNumber(page_number)
      .resultPerPage(result_per_page);

    const result = await this.repo
      .raw(
        `SELECT a.*
         FROM artists a
         WHERE a.user_id = :user_id
         ORDER BY a.created_at DESC
         LIMIT :result_per_page OFFSET :offset`,
        { user_id, result_per_page, offset: pager.offset() },
      )
      .then(({ rows }) => rows);

    return pager.build(result);
  }

  public async getPrimaryArtistCount(user_id: string): Promise<number> {
    const sql = `
      SELECT COUNT(DISTINCT a.id) AS total
      FROM artists a
      JOIN release_artists ra ON ra.artist_id = a.id
      WHERE a.user_id = :user_id
        AND ra.is_primary IS TRUE
    `;

    return this.repo.raw(sql, { user_id }).then(({ rows }) => {
      if (!rows?.length) return 0;
      return Number(rows[0].total) || 0;
    });
  }

  public async get(id: string, user_id?: string): Promise<Artist> {
    const binding: Record<string, any> = { id };

    if (user_id) {
      binding.user_id = user_id;
    }
    return this.repo
      .raw(
        `SELECT a.*,
                count(ra.artist_id) AS number_of_release_appearances,
                json_agg(r)         AS releases
         FROM artists a
                  LEFT JOIN release_artists ra ON a.id = ra.artist_id
                  LEFT JOIN releases r ON ra.release_id = r.id
         WHERE a.id = :id
           ${user_id != null ? 'AND a.user_id = :user_id' : ''}
         GROUP BY a.id
        `,
        binding,
      )
      .then(({ rows }) => rows[0]);
  }

  public async create(data: CreateArtistData): Promise<Artist> {
    return this.qb()
      .insert({
        ...data,
        localized_names: data.localized_names
          ? (JSON.stringify(data.localized_names) as unknown as LocalizedName[])
          : undefined,
        id: ulid(),
      })
      .returning('*')
      .then(([user]) => user);
  }

  public async update(id: string, data: Partial<CreateArtistData>) {
    return this.qb()
      .where({ id })
      .update({
        ...data,
        localized_names: data.localized_names
          ? (JSON.stringify(data.localized_names) as unknown as LocalizedName[])
          : undefined,
      })
      .returning('*')
      .then(([v]) => v);
  }

  public async listArtists({
    search_term,
    page_number = 1,
    result_per_page = 10,
    community_id,
    user_id,
  }: OffsetPaginationOption &
    Partial<{
      search_term: string;
      community_id: string;
      user_id: string;
    }>): Promise<OffsetPaginationResult<Artist>> {
    const pageBuilder = new OffsetPaginationResultBuilder<Artist>();

    let countQuery = `
        SELECT count(a.id) AS total
        FROM artists a
    `;

    let searchQuery = `
        SELECT a.*,
               json_build_object('id', u.id,
                                 'email', u.email,    
                                 'first_name', u.first_name, 
                                 'last_name', u.last_name, 
                                 'display_name',
                                 u.display_name, 
                                 'username', u.username, 
                                 'category', u.category, 
                                 'profile_picture', u.profile_picture) as created_by
        FROM artists a
    `;

    const queryParams: Record<string, any> = {};

    // Add conditions based on user_id or community_id
    if (user_id) {
      countQuery += ` JOIN users u ON a.user_id = u.id WHERE a.user_id = :user_id`;
      searchQuery += ` JOIN users u ON a.user_id = u.id WHERE a.user_id = :user_id`;
      queryParams.user_id = user_id;
    } else if (community_id) {
      countQuery += ` JOIN users u ON a.user_id = u.id AND u.community_id = :community_id`;
      searchQuery += ` JOIN users u ON a.user_id = u.id AND u.community_id = :community_id`;
      queryParams.community_id = community_id;
    }

    if (search_term) {
      // Add search conditions
      const searchCondition = `
      (a.id = :search_term OR
       a.name ILIKE :search_pattern OR
       a.revelator_id = :search_term)
    `;

      if (user_id || community_id) {
        countQuery += ` AND ${searchCondition}`;
        searchQuery += ` AND ${searchCondition}`;
      } else {
        countQuery += ` WHERE ${searchCondition}`;
        searchQuery += ` WHERE ${searchCondition}`;
      }

      const searchPattern = `%${search_term}%`;
      queryParams.search_pattern = searchPattern;
      queryParams.search_term = search_term;
    }

    const totalCount = await this.repo
      .raw(countQuery, queryParams)
      .then(({ rows }) => rows[0].total);

    const pager = pageBuilder
      .totalRecordCount(totalCount)
      .pageNumber(page_number)
      .resultPerPage(result_per_page);

    searchQuery += `
      ORDER BY a.created_at DESC
      LIMIT :result_per_page
      OFFSET :offset
    `;

    queryParams.result_per_page = result_per_page;
    queryParams.offset = pager.offset();

    const result = await this.repo
      .raw(searchQuery, queryParams)
      .then(({ rows }) => rows);

    return pager.build(result);
  }

  public async getRevArtists(community_id: string): Promise<
    {
      artist_rev_id: string;
      name: string;
      enterprise_id: string;
      enterprise_name: string;
    }[]
  > {
    return this.repo
      .raw(
        `select distinct(a.revelator_id) as artist_rev_id, a.name, u.enterprise_id, u.enterprise_name
         from artists a
                  join users u on a.user_id = u.id
         where u.community_id = :community_id`,
        { community_id },
      )
      .then(({ rows }) => rows);
  }
}
