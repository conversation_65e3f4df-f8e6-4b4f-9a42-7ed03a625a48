import { BaseEntity } from '@app/internal/postgres/base.entity';

export enum UserCategory {
  COMMUNITY_OWNER = 'community_owner',
  TEAM_MEMBER = 'team_member',
  CREATOR = 'creator',
  FAN = 'fan',
  BRAND = 'brand',
}

export enum Role {
  OWNER = 'owner',
  ADMIN = 'admin',
  FINANCE = 'finance',
  COMMUNITY_MANAGER = 'community_manager',
}

export enum UserStatus {
  active = 'active',
  suspended = 'suspended',
  blocked = 'blocked',
}

export class User extends BaseEntity<User> {
  first_name: string;
  last_name: string;
  gender: 'male' | 'female' | 'other';
  status: UserStatus;
  date_of_birth: Date;
  display_name: string;
  email: string;
  email_verified: boolean;
  phone: string;
  phone_verified: boolean;
  password: string;
  username: string;
  bio: string;
  community_id: string;
  /** community_owner | creator | fan | brand | team_member */
  category: UserCategory; // creator | fan | brand
  role: Role; /** owner | admin | finance | community_manager */
  permissions: object[];
  url: string;
  profile_picture: string;
  cover_picture: string;
  country: string;
  two_fa: string;
  service_update: boolean;
  vibrate_id: string;
  revelator_id: string;
  google_id: string;
  facebook_id: string;
  instagram_id: string;
  tiktok_id: string;
  enterprise_name: string;
  enterprise_id: string;
  payee_id: string;
  team_id: string;
  accounts: Record<string, any> | string;
  collaborators: string[];
  follower_count: number;
  following_count: number;
  last_login: Date;
}
