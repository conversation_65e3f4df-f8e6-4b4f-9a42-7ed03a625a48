import { inject, injectable } from 'inversify';
import { MODULE_TOKENS } from '@app/internal/ioc/tokens';
import { Repository } from '@app/internal/postgres/repository';
import { Role, User, UserCategory } from './entities/user.entity';
import { calculatePercentageChange } from '@app/utils/post.utils';
import { ulid } from 'ulid';
import {
  OffsetPaginationOption,
  OffsetPaginationResult,
  OffsetPaginationResultBuilder,
  SeekPaginationOption,
  SeekPaginationResult,
} from '@app/internal/postgres/pagination';
import { formatISO } from 'date-fns';
import { omit, pick } from 'lodash';
import { PeriodFilter } from '@app/http/controllers/analytics/dto/period.dto';
import { Period } from '@app/utils/period.utils';
import { WalletType } from '../wallet/entities/wallet.entity';
import {
  PaymentType,
  TransactionStatus,
  TransactionType,
} from '../transaction/entities/transaction.entity';
import { ReportStatus } from '../reports/entities/reports.entities';
import { InviteStatus } from '../invite/entities/invite.entity';
import { PresenceCache } from '@app/internal/presence';

export type CreateUserData = Partial<Omit<User, 'created_at' | 'updated_at'>>;

export type UpdateUserData = Partial<
  Omit<User, 'id' | 'created_at' | 'updated_at'>
>;

export type UserDescription = {
  id: string;
  email: string;
  first_name: string;
  enterprise_name: string;
  revelator_id: string;
  category: UserCategory;
  community_id: string;
  community_level_user: boolean;
  team_owner: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
    community_id: string;
    revelator_id: string;
  };
  community_owner: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
    community_id: string;
    revelator_id: string;
    enterprise_name: string;
    enterprise_id: string;
    payee_id: string;
  };
};

@injectable()
export class UserService {
  private readonly qb = this.repo.createBuilder('users');

  constructor(
    @inject(MODULE_TOKENS.Repository) private readonly repo: Repository<User>,
    @inject(MODULE_TOKENS.PresenceCache)
    private readonly presence: PresenceCache,
  ) {}

  public async teamMemberCount(team_id: string): Promise<number> {
    return this.qb()
      .where({ team_id, category: UserCategory.TEAM_MEMBER })
      .count()
      .then(([c]) => Number(c.count));
  }

  public async count(
    where: Partial<
      Pick<User, 'email' | 'phone' | 'username' | 'community_id'> & {
        category: UserCategory[];
      }
    >,
  ): Promise<number> {
    const { category, ...rest } = where;

    const query = this.qb().where(rest);

    if (category?.length > 0) {
      const orSearch = [];
      const binding = {};

      let i = 0;
      for (const value of category) {
        orSearch.push(`category = :v${i}`);
        binding[`v${i}`] = value;

        i += 1;
      }

      query.andWhere(this.repo.kx.raw(`(${orSearch.join(' OR ')})`, binding));
    }

    return query.count().then(([n]) => Number(n.count));
  }

  public async find(
    where: Partial<
      Pick<
        User,
        'id' | 'email' | 'phone' | 'username' | 'community_id' | 'payee_id'
      > & {
        category: UserCategory[];
      }
    >,
  ) {
    const { category, ...rest } = where;

    const query = this.qb().where(rest);

    if (category?.length > 0) {
      const orSearch = [];
      const binding = {};

      let i = 0;
      for (const value of category) {
        orSearch.push(`category = :v${i}`);
        binding[`v${i}`] = value;

        i += 1;
      }

      query.andWhere(this.repo.kx.raw(`(${orSearch.join(' OR ')})`, binding));
    }

    return query.first('*');
  }

  public async getRevCreators(
    community_id: string,
  ): Promise<
    Pick<
      User,
      'id' | 'email' | 'revelator_id' | 'enterprise_id' | 'enterprise_name'
    >[]
  > {
    return this.repo
      .raw(
        `select u.id, u.email, u.revelator_id, u.enterprise_id, u.enterprise_name
       from users u
       where enterprise_id is not null
         and category = 'creator' and community_id = :community_id`,
        { community_id },
      )
      .then(({ rows }) => rows);
  }

  public async getById(id: string): Promise<User> {
    return this.qb().where({ id }).first('*');
  }

  public async get(id: string, community_id: string): Promise<User> {
    return this.qb().where({ id, community_id }).first('*');
  }

  public async getProfile(
    id_or_username: string,
    {
      user_viewing_profile,
      community_id,
    }: { user_viewing_profile?: string; community_id: string },
  ): Promise<User> {
    if (user_viewing_profile && user_viewing_profile !== id_or_username) {
      const query = `SELECT u.id              AS id,
                            u.display_name    AS display_name,
                            u.username        AS username,
                            u.bio             AS bio,
                            u.url             AS url,
                            u.accounts        AS accounts,
                            u.collaborators   AS collaborators,
                            u.profile_picture AS profile_picture,
                            u.cover_picture   AS cover_picture,
                            u.category        AS category,
                            u.country         AS country,
                            u.vibrate_id      AS vibrate_id,
                            u.follower_count  AS follower_count,
                            u.following_count AS following_count,
                            CASE
                                WHEN f1.user_id IS NULL THEN false
                                ELSE true
                                END           AS followed_by_you,
                            CASE
                                WHEN f2.user_id IS NULL THEN false
                                ELSE true
                                END           AS follows_you,
                            u.created_at
                     FROM users u
                              LEFT JOIN followers f1 ON f1.user_id = u.id AND f1.follower_id = :user_viewing_profile
                              LEFT JOIN followers f2 ON f2.user_id = :user_viewing_profile AND f2.follower_id = u.id
                     WHERE (u.id = :id_or_username
                         OR u.username ILIKE :id_or_username)
                       AND u.community_id = :community_id`;

      return this.repo
        .raw(query, {
          id_or_username,
          user_viewing_profile,
          community_id,
        })
        .then(({ rows }) => rows?.[0]);
    }

    return this.qb()
      .whereRaw(
        '(id = :id_or_username OR username ILIKE :id_or_username) AND community_id = :community_id',
        { id_or_username, community_id },
      )
      .first(
        'id',
        'display_name',
        'username',
        'bio',
        'url',
        'accounts',
        'collaborators',
        'profile_picture',
        'cover_picture',
        'category',
        'country',
        'follower_count',
        'following_count',
        'created_at',
        'last_login',
      );
  }

  public async overview(
    user_id: string,
    options: {
      status?: ReportStatus;
      from?: string | Date;
      to?: string | Date;
      page_number?: number;
      result_per_page?: number;
    } = { page_number: 1, result_per_page: 10 },
  ): Promise<{
    profile: Pick<
      User,
      | 'id'
      | 'display_name'
      | 'username'
      | 'bio'
      | 'url'
      | 'accounts'
      | 'collaborators'
      | 'profile_picture'
      | 'cover_picture'
      | 'category'
      | 'country'
      | 'follower_count'
      | 'following_count'
      | 'created_at'
      | 'last_login'
    >;
    reports_received: OffsetPaginationResult<
      Report & { reporter: Partial<User>; content_title: string }
    >;
    reports_created: OffsetPaginationResult<
      Report & { reported_user: Partial<User>; content_title: string }
    >;
  }> {
    const { status, from, to, page_number = 1, result_per_page = 10 } = options;

    const profile = await this.qb()
      .where({ id: user_id })
      .first(
        'id',
        'display_name',
        'username',
        'bio',
        'url',
        'accounts',
        'collaborators',
        'profile_picture',
        'cover_picture',
        'category',
        'country',
        'follower_count',
        'following_count',
        'created_at',
        'last_login',
      );

    const extraWhere: string[] = [];
    const params: Record<string, any> = { user_id };

    if (status) {
      extraWhere.push('r.status = :status');
      params.status = status;
    }
    if (from) {
      extraWhere.push('r.reported_on >= :from');
      params.from = from;
    }
    if (to) {
      extraWhere.push('r.reported_on <= :to');
      params.to = to;
    }
    const extraSql = extraWhere.length ? `AND ${extraWhere.join(' AND ')}` : '';

    const totalReceived = await this.repo
      .raw(
        `
            SELECT COUNT(*) AS total
            FROM reports r
                     JOIN posts p ON p.id = r.post_id
            WHERE p.user_id = :user_id
                ${extraSql}
        `,
        params,
      )
      .then(({ rows }) => Number(rows[0].total));

    const receivedPager = new OffsetPaginationResultBuilder<
      Report & { reporter: Partial<User>; content_title: string }
    >()
      .totalRecordCount(totalReceived)
      .pageNumber(page_number)
      .resultPerPage(result_per_page);

    receivedPager.getTotalNumberOfPages();

    const receivedRows = await this.repo
      .raw(
        `
            SELECT r.*,
                   p.content ->> 'text' AS content_title,
                   json_build_object(
                           'id', rep.id,
                           'username', rep.username,
                           'display_name', rep.display_name,
                           'profile_picture', rep.profile_picture
                   )                    AS reporter
            FROM reports r
                     JOIN posts p ON p.id = r.post_id
                     JOIN users rep ON rep.id = r.user_id
            WHERE p.user_id = :user_id
                ${extraSql}
            ORDER BY r.reported_on DESC
            LIMIT :limit OFFSET :offset
        `,
        {
          ...params,
          limit: result_per_page,
          offset: receivedPager.offset(),
        },
      )
      .then(({ rows }) => rows);

    const totalCreated = await this.repo
      .raw(
        `
            SELECT COUNT(*) AS total
            FROM reports r
            WHERE r.user_id = :user_id
                ${extraSql}
        `,
        params,
      )
      .then(({ rows }) => Number(rows[0].total));

    const createdPager = new OffsetPaginationResultBuilder<
      Report & { reported_user: Partial<User>; content_title: string }
    >()
      .totalRecordCount(totalCreated)
      .pageNumber(page_number)
      .resultPerPage(result_per_page);

    createdPager.getTotalNumberOfPages();

    const createdRows = await this.repo
      .raw(
        `
            SELECT r.*,
                   p.content ->> 'text' AS content_title,
                   json_build_object(
                           'id', ru.id,
                           'username', ru.username,
                           'display_name', ru.display_name,
                           'profile_picture', ru.profile_picture
                   )                    AS reported_user
            FROM reports r
                     JOIN posts p ON p.id = r.post_id
                     JOIN users ru ON ru.id = p.user_id -- owner of content
            WHERE r.user_id = :user_id
                ${extraSql}
            ORDER BY r.reported_on DESC
            LIMIT :limit OFFSET :offset
        `,
        {
          ...params,
          limit: result_per_page,
          offset: createdPager.offset(),
        },
      )
      .then(({ rows }) => rows);

    return {
      profile,
      reports_received: receivedPager.build(receivedRows),
      reports_created: createdPager.build(createdRows),
    };
  }

  public async userDetails(user_id: string): Promise<any> {
    const userQuery = `
        SELECT u.id,
               u.first_name,
               u.last_name,
               u.username,
               u.display_name,
               u.country,
               u.email,
               u.two_fa,
               u.category,
               u.profile_picture,
               u.created_at,
               u.updated_at,
               u.last_login,
               u.accounts,
               u.vibrate_id
        FROM users u
        WHERE u.id = :user_id
    `;

    const user = await this.repo
      .raw(userQuery, { user_id })
      .then(({ rows }) => rows?.[0]);

    const additionalData: any = {};

    if (user.category === 'creator') {
      const releasesQuery = `
          SELECT r.id,
                 r.title,
                 r.version,
                 r.status,
                 r.in_distribution_queue,
                 r.cover_art,
                 r.release_code,
                 r.catalog_id,
                 r.created_at,
                 r.updated_at,
                 a.id              AS artist_id,
                 a.name            AS artist_name,
                 a.localized_names AS artist_localized_names,
                 a.spotify_id      AS artist_spotify_id,
                 a.apple_music_id  AS artist_apple_music_id,
                 a.image           AS artist_image,
                 a.revelator_id    AS artist_revelator_id,
                 ra.role           AS artist_role,
                 ra.is_primary     AS artist_is_primary,
                 ra.is_main        AS artist_is_main
          FROM releases r
                   LEFT JOIN release_artists ra ON r.id = ra.release_id
                   LEFT JOIN artists a ON ra.artist_id = a.id
          WHERE r.user_id = :user_id
          ORDER BY r.created_at DESC
          LIMIT 3
      `;

      const releases = await this.repo
        .raw(releasesQuery, { user_id })
        .then(({ rows }) => rows);

      additionalData.latestReleases = this.formatReleases(releases);
    } else {
      const followersQuery = `
          SELECT u.id,
                 u.first_name,
                 u.last_name,
                 u.username,
                 u.display_name,
                 u.country,
                 u.vibrate_id,
                 f.followed_on
          FROM followers f
                   JOIN users u ON u.id = f.follower_id
          WHERE f.user_id = :user_id
          ORDER BY f.followed_on DESC
          LIMIT 3
      `;

      additionalData.latestFollowers = await this.repo
        .raw(followersQuery, { user_id })
        .then(({ rows }) => rows);
    }

    return { ...user, ...additionalData };
  }

  private formatReleases(releases: any[]): any[] {
    const formattedReleases = [];
    const releaseMap = new Map();

    for (const release of releases) {
      if (!releaseMap.has(release?.id)) {
        releaseMap.set(release?.id, {
          id: release?.id,
          title: release?.title,
          version: release?.version,
          status: release?.status,
          in_distribution_queue: release?.in_distribution_queue,
          cover_art: release?.cover_art,
          release_code: release?.release_code,
          catalog_id: release?.catalog_id,
          created_at: release?.created_at,
          updated_at: release?.updated_at,
          artists: [],
        });
        formattedReleases.push(releaseMap.get(release?.id));
      }

      if (release?.artist_id) {
        releaseMap.get(release?.id).artists.push({
          id: release?.artist_id,
          name: release?.artist_name,
          localized_names: release?.artist_localized_names,
          spotify_id: release?.artist_spotify_id,
          apple_music_id: release?.artist_apple_music_id,
          image: release?.artist_image,
          revelator_id: release?.artist_revelator_id,
          role: release?.artist_role,
          is_primary: release?.artist_is_primary,
          is_main: release?.artist_is_main,
        });
      }
    }

    return formattedReleases;
  }

  public async getByVibrateId(vibrate_id: string): Promise<User> {
    return this.qb().where({ vibrate_id }).first('*');
  }

  public async getByPhone(phone: string): Promise<User> {
    return this.qb().where({ phone }).first('*');
  }

  public async getByEmail(email: string): Promise<User> {
    return this.qb().where({ email }).first('*');
  }

  public async searchUser(
    search: string,
    community_id: string,
  ): Promise<User[]> {
    const searchQuery = `%${search}%`;
    const query = `
        SELECT *
        FROM users
        WHERE (username ILIKE :searchQuery OR display_name ILIKE :searchQuery)
          AND community_id = :community_id
    `;
    return this.repo
      .raw(query, { searchQuery, community_id })
      .then(({ rows }) => rows);
  }

  public async teamMembers(
    team_id: string,
    filter: SeekPaginationOption<string> &
      Partial<{ search_term: string; status: InviteStatus }> = {},
  ): Promise<SeekPaginationResult<Partial<User>, string>> {
    const { search_term, cursor, result_per_page = 10, status } = filter;

    const query = this.qb()
      .from('users')
      .where(function () {
        this.where('users.team_id', team_id).orWhere('users.id', team_id);
      })
      .leftJoin('invitations as inv', function () {
        this.on('inv.email', '=', 'users.email').andOn(
          'inv.team_id',
          '=',
          'users.team_id',
        );
      });

    if (status) {
      query.andWhere('inv.status', status);
    }

    if (search_term?.trim()) {
      const term = `%${search_term.trim()}%`.toLowerCase();
      query.andWhere(function () {
        this.whereRaw('LOWER(users.first_name) ILIKE ?', [term])
          .orWhereRaw('LOWER(users.last_name)  ILIKE ?', [term])
          .orWhereRaw('LOWER(users.email)      ILIKE ?', [term]);
      });
    }

    if (cursor) {
      query.andWhere('users.id', '>', cursor);
    }

    const result = await query
      .orderBy('users.created_at', 'desc')
      .orderBy('users.id', 'desc')
      .limit(result_per_page)
      .select(
        'users.id',
        'users.first_name',
        'users.last_name',
        'users.email',
        'users.display_name',
        'users.username',
        'users.role',
        'users.team_id',
        'users.permissions',
        'users.community_id',
        'users.category',
        'users.bio',
        'users.url',
        'users.accounts',
        'users.profile_picture',
        'users.cover_picture',
        'users.country',
        'users.status',
        'users.created_at',
        'users.updated_at',
        'users.last_login',
        'inv.id as invite_id',
        'inv.role as invite_role',
        'inv.status as invite_status',
      );

    const cleaned = result.map((r) =>
      r.id === team_id || r.role === Role.OWNER
        ? (omit(r, ['invite_id', 'invite_role', 'invite_status']) as any)
        : r,
    );

    return new SeekPaginationResult({
      result_per_page,
      result: cleaned,
      cursor: result?.at?.(-1)?.id,
    });
  }

  public async all(
    community_id: string,
    queryParams: OffsetPaginationOption &
      Partial<{
        category: UserCategory;
        username: string;
        selectFields: (keyof User)[];
        role?: Role;
        search_term?: string;
      }>,
  ): Promise<OffsetPaginationResult<Partial<User> & { report_count: number }>> {
    const defaultSelect: (keyof User)[] = [
      'id',
      'first_name',
      'last_name',
      'vibrate_id',
      'email',
      'display_name',
      'username',
      'bio',
      'url',
      'accounts',
      'collaborators',
      'profile_picture',
      'cover_picture',
      'category',
      'country',
      'follower_count',
      'following_count',
      'created_at',
      'last_login',
    ];

    const {
      page_number = 1,
      result_per_page = 10,
      category,
      username,
      search_term,
      role,
      selectFields: userSelectFields,
    } = queryParams;

    const selectFields = userSelectFields?.length
      ? Array.from(new Set([...defaultSelect, ...userSelectFields]))
      : [...defaultSelect];

    const pageBuilder = new OffsetPaginationResultBuilder<
      Partial<User> & { report_count: number; is_online: boolean }
    >();

    let countQuery = this.qb().where({ community_id });

    let query = this.qb()
      .where('community_id', community_id)
      .select(selectFields);

    if (category) {
      countQuery = countQuery.andWhere({ category });
      query = query.andWhere({ category });
    } else {
      countQuery = countQuery.whereNotIn('category', [
        'team_member',
        'community_owner',
      ]);
      query = query.whereNotIn('category', ['team_member', 'community_owner']);
    }

    if (role) {
      countQuery = countQuery.andWhere({ role });
      query = query.andWhere({ role });
    }

    if (username) {
      const like = `%${username}%`;
      countQuery.andWhereRaw('LOWER(username) ILIKE LOWER(?)', [like]);
      query.andWhereRaw('LOWER(username) ILIKE LOWER(?)', [like]);
    }

    if (search_term?.trim()) {
      const term = `%${search_term.trim()}%`.toLowerCase();

      const searchRaw = `
        (
          LOWER(first_name) ILIKE ?
          OR LOWER(last_name) ILIKE ?
          OR LOWER(username) ILIKE ?
          OR LOWER(display_name) ILIKE ?
          OR LOWER(email) ILIKE ?
        )
      `;
      const bindings = Array(5).fill(term);

      countQuery.andWhereRaw(searchRaw, bindings);
      query.andWhereRaw(searchRaw, bindings);
    }

    const reportCountQuery = this.repo.kx.raw(`
      (
        SELECT COUNT(*)
        FROM   reports r
        JOIN   posts   p ON p.id = r.post_id
        WHERE  p.user_id = users.id
      )::int AS report_count
    `);
    query.select(reportCountQuery);

    const totalCount = await countQuery.count().then((c) => Number(c[0].count));

    const pager = pageBuilder
      .totalRecordCount(totalCount)
      .pageNumber(page_number)
      .resultPerPage(result_per_page);

    const result = await query
      .orderBy('created_at', 'desc')
      .limit(result_per_page)
      .offset(pager.offset());

    const ids = result.map((r) => r.id);
    const onlineSet = await this.presence.onlineSet(ids);

    const withPresence = result.map((r) => ({
      ...r,
      is_online: onlineSet.has(r.id),
    }));

    return pager.build(
      withPresence as (Partial<User> & {
        report_count: number;
        is_online: boolean;
      })[],
    );
  }

  public async getByUsername(username: string): Promise<User> {
    return this.repo
      .raw(
        `SELECT *
            FROM users u
            WHERE lower(u.username) = lower(:username)`,
        {
          username,
        },
      )
      .then(({ rows }) => rows?.[0]);
  }

  public async create(data: CreateUserData): Promise<User> {
    return this.qb()
      .insert({ id: ulid(), ...data })
      .returning('*')
      .then(([user]) => user);
  }

  public async fetch(condition: Partial<User>): Promise<User[]> {
    return this.qb().where(condition).select('*');
  }

  public async update(id: string, data: UpdateUserData): Promise<User> {
    return this.qb()
      .where({ id })
      .update(data)
      .returning('*')
      .then(([user]) => user);
  }

  public async getProfileViews(user_id: string): Promise<number> {
    return this.repo
      .raw(
        'SELECT COUNT(*) FROM profile_views WHERE profile_id = :profile_id',
        { profile_id: user_id },
      )
      .then(({ rows }) => Number(rows?.[0]?.count) || 0);
  }

  public async viewProfile(
    id: string,
    {
      viewer_id,
      metadata,
    }: {
      viewer_id: string;
      metadata: {
        ip: string;
        country: string;
        device_info: {
          type: string;
          os: string;
          os_version: string;
        };
        client_info: {
          name: string;
          version: string;
          type: string;
          is_bot: boolean;
        };
      };
    },
  ): Promise<void> {
    const query = `SELECT pv.profile_id AS profile_id,
                          pv.viewer_id  AS viewer_id
                   FROM profile_views pv
                   WHERE pv.profile_id = :profile_id
                     AND DATE(pv.viewed_on) = CURRENT_DATE
                     AND ${
                       viewer_id
                         ? 'pv.viewer_id = :viewer_id'
                         : "pv.metadata ->> 'ip' = :ip"
                     }`;

    const viewed_today = await this.repo
      .raw(query, {
        profile_id: id,
        viewer_id: viewer_id ?? null,
        ip: metadata.ip,
      })
      .then(({ rows }) => rows?.[0]);

    if (!viewed_today) {
      const query = `INSERT INTO profile_views (id, metadata, profile_id, viewer_id)
                     VALUES (:id, :metadata, :profile_id, :viewer_id)`;

      await this.repo.raw(query, {
        id: ulid(),
        metadata,
        profile_id: id,
        viewer_id: viewer_id ?? null,
      });
    }
  }

  public async profileViewerDeviceStats(id: string, period: PeriodFilter) {
    const time_line = Period(period);
    const past_date = time_line.from;
    const current_date = time_line.to;

    const period_duration =
      new Date(current_date).getTime() - new Date(past_date).getTime();
    const prev_period_end = new Date(past_date);
    const prev_period_start = new Date(
      prev_period_end.getTime() - period_duration,
    );

    const query = `with curr_nums as (select pv.id                                                        as view_id,
                                             coalesce(pv.metadata -> 'device_info' ->> 'type', 'unknown') as type
                                      from profile_views pv
                                      where pv.profile_id = :profile_id
                                        and pv.viewed_on >= :past_date
                                        and pv.viewed_on <= :current_date),
                        prev_nums as (select pv.id                                                        as view_id,
                                             coalesce(pv.metadata -> 'device_info' ->> 'type', 'unknown') as type
                                      from profile_views pv
                                      where pv.profile_id = :profile_id
                                        and pv.viewed_on >= :prev_period_start
                                        and pv.viewed_on < :past_date),
                        prev_stats as (select pn.type as type, count(*) as view_count
                                       from prev_nums pn
                                       group by pn.type),
                        curr_stats as (select cn.type as type, count(*) as view_count
                                       from curr_nums cn
                                       group by cn.type),
                        total as (select sum(view_count) as total_view_count
                                  from curr_stats)
                   select cs.type                          as type,
                          cs.view_count                    AS views,
                          case
                              when total.total_view_count > 0 then
                                  round((cs.view_count::numeric * 100) / total.total_view_count, 2)
                              else 0 end                   as percentage_view,
                          case
                              when ps.view_count is not null then
                                  round((cs.view_count / ps.view_count) * 100, 2)
                              else 100::numeric(13, 2) end as diff
                   from curr_stats cs
                            left join prev_stats ps on cs.type = ps.type
                            cross join total
                   order by cs.view_count desc`;

    return this.repo
      .raw(query, {
        profile_id: id,
        past_date,
        current_date,
        prev_period_start,
      })
      .then(({ rows }) => rows);
  }

  public async profileViewStats(id: string, period: PeriodFilter) {
    const time_line = Period(period);
    const past_date = time_line.from;
    const current_date = time_line.to;

    const query = `
        WITH profile_views_in_past_month AS (SELECT pv.id,
                                                    pv.profile_id,
                                                    COALESCE(pv.metadata ->> 'country', 'Unknown') AS country
                                             FROM profile_views pv
                                             WHERE pv.profile_id = :profile_id
                                               AND pv.viewed_on >= :past_date
                                               AND pv.viewed_on < :current_date),
             view_counts AS (SELECT country,
                                    COUNT(*) AS view_count
                             FROM profile_views_in_past_month
                             GROUP BY country),
             total_views AS (SELECT COALESCE(SUM(view_count), 0) AS total_view_count FROM view_counts)
        SELECT vc.country,
               vc.view_count  AS views,
               CASE
                   WHEN tv.total_view_count > 0 THEN
                       ROUND((vc.view_count::numeric * 100) / tv.total_view_count, 2)
                   ELSE 0 END AS percentage_view
        FROM view_counts vc
                 CROSS JOIN total_views tv
        ORDER BY vc.view_count DESC;
    `;

    return this.repo
      .raw(query, {
        profile_id: id,
        past_date,
        current_date,
      })
      .then(({ rows }) => rows);
  }

  public async toggleFollow(
    user_id: string,
    follower_id: string,
  ): Promise<boolean> {
    const followExists = await this.repo
      .raw(
        'SELECT 1 FROM followers WHERE user_id = :user_id AND follower_id = :follower_id',
        { user_id, follower_id },
      )
      .then(({ rows }) => rows.length > 0);

    if (followExists) {
      // Unfollow
      await this.repo.raw(
        'DELETE FROM followers WHERE user_id = :user_id AND follower_id = :follower_id',
        { user_id, follower_id },
      );
    } else {
      // Follow
      await this.repo.raw(
        'INSERT INTO followers (user_id, follower_id, followed_on) VALUES (:user_id, :follower_id, :followed_on)',
        {
          user_id,
          follower_id,
          followed_on: new Date(),
        },
      );
    }

    return !followExists;
  }

  public async userCommunityChannel(user_id: string): Promise<{
    followers: number;
    following: number;
    brand_followers: number;
    follower_percentage_change: number;
    following_percentage_change: number;
    brand_follower_percentage_change: number;
    total_community: number;
  }> {
    const currentDate = new Date();
    const pastDate = new Date(currentDate);
    pastDate.setMonth(currentDate.getMonth() - 1);
    const pastDateISO = formatISO(pastDate);

    // Query follower and following counts for the current date
    const currentStatsQuery = `
        SELECT (SELECT COUNT(*) FROM followers WHERE user_id = :user_id)     AS follower_count,
               (SELECT COUNT(*) FROM followers WHERE follower_id = :user_id) AS following_count,
               (SELECT COUNT(*)
                FROM followers f
                         JOIN users u ON u.id = f.follower_id
                WHERE f.user_id = :user_id
                  AND u.category = 'brand')                                  AS brand_follower_count
    `;

    const currentStats = await this.repo
      .raw(currentStatsQuery, { user_id })
      .then(({ rows }) => rows[0]);

    // Query follower and following counts from one month ago
    const pastStatsQuery = `
        SELECT (SELECT COUNT(*) FROM followers WHERE user_id = :user_id AND followed_on < :past_date)     AS follower_count,
               (SELECT COUNT(*)
                FROM followers
                WHERE follower_id = :user_id
                  AND followed_on < :past_date)                                                           AS following_count,
               (SELECT COUNT(*)
                FROM followers f
                         JOIN users u ON u.id = f.follower_id
                WHERE f.user_id = :user_id
                  AND f.followed_on < :past_date
                  AND u.category = 'brand')                                                               AS brand_follower_count
    `;

    const pastStats = await this.repo
      .raw(pastStatsQuery, { user_id, past_date: pastDateISO })
      .then(({ rows }) => rows[0]);

    const followers = Number(currentStats.follower_count) || 0;
    const pastFollowers = Number(pastStats.follower_count) || 0;
    const following = Number(currentStats.following_count) || 0;
    const pastFollowing = Number(pastStats.following_count) || 0;
    const brandFollowers = Number(currentStats.brand_follower_count) || 0;
    const pastBrandFollowers = Number(pastStats.brand_follower_count) || 0;

    const followerPercentageChange = calculatePercentageChange(
      followers,
      pastFollowers,
    );
    const followingPercentageChange = calculatePercentageChange(
      following,
      pastFollowing,
    );
    const brandFollowerPercentageChange = calculatePercentageChange(
      brandFollowers,
      pastBrandFollowers,
    );

    return {
      followers,
      following,
      brand_followers: brandFollowers,
      follower_percentage_change: followerPercentageChange,
      following_percentage_change: followingPercentageChange,
      brand_follower_percentage_change: brandFollowerPercentageChange,
      total_community: followers,
    };
  }

  /** Returns description of a user, always including the
   *  community owner except when the user is already a TEAM_MEMBER
   *  (whose team owner is returned instead).
   */
  public async describe(id: string): Promise<UserDescription> {
    const user = await this.getById(id);

    const userDescription: UserDescription = {
      id,
      email: user.email,
      first_name: user.first_name,
      enterprise_name: user.enterprise_name,
      revelator_id: user.revelator_id,
      team_owner: null,
      category: user.category,
      community_id: user.community_id,
      community_level_user: false,
      community_owner: null,
    };

    if (user.category === UserCategory.COMMUNITY_OWNER) {
      userDescription.community_level_user = true;
      userDescription.community_owner = pick(
        user,
        'id',
        'email',
        'first_name',
        'last_name',
        'community_id',
        'revelator_id',
        'enterprise_name',
        'enterprise_id',
        'payee_id',
      );
      return userDescription;
    }

    if (user.category === UserCategory.TEAM_MEMBER) {
      const teamOwner = await this.getById(user.team_id);

      userDescription.team_owner = pick(
        teamOwner,
        'id',
        'email',
        'first_name',
        'last_name',
        'community_id',
        'revelator_id',
        'enterprise_name',
        'enterprise_id',
        'payee_id',
      );

      if (teamOwner?.category === UserCategory.COMMUNITY_OWNER) {
        userDescription.community_level_user = true;
      }
      return userDescription;
    }

    const communityOwner = await this.find({
      community_id: user.community_id,
      category: [UserCategory.COMMUNITY_OWNER],
    });

    if (communityOwner) {
      userDescription.community_owner = communityOwner;
    }

    return userDescription;
  }

  public async demographicBreakdown(user_id: string) {
    const genderQuery = `
        SELECT u.gender,
               COUNT(*) AS total
        FROM users u
                 JOIN followers f ON f.follower_id = u.id
        WHERE f.user_id = :user_id
        GROUP BY u.gender;
    `;

    const ageQuery = `
        SELECT CASE
                   WHEN u.date_of_birth IS NULL THEN 'unknown'
                   WHEN u.date_of_birth <= current_date - INTERVAL '65 years' THEN '65-'
                   WHEN u.date_of_birth > current_date - INTERVAL '65 years' AND
                        u.date_of_birth <= current_date - INTERVAL '45 years' THEN '45-64'
                   WHEN u.date_of_birth > current_date - INTERVAL '45 years' AND
                        u.date_of_birth <= current_date - INTERVAL '35 years' THEN '35-44'
                   WHEN u.date_of_birth > current_date - INTERVAL '35 years' AND
                        u.date_of_birth <= current_date - INTERVAL '25 years' THEN '25-34'
                   WHEN u.date_of_birth > current_date - INTERVAL '25 years' AND
                        u.date_of_birth <= current_date - INTERVAL '18 years' THEN '18-24'
                   WHEN u.date_of_birth > current_date - INTERVAL '18 years' AND
                        u.date_of_birth <= current_date - INTERVAL '13 years' THEN '13-17'
                   ELSE 'unknown'
                   END  AS age_group,
               u.gender,
               COUNT(*) AS total
        FROM users u
                 JOIN followers f ON f.follower_id = u.id
        WHERE f.user_id = :user_id
        GROUP BY age_group, u.gender;
    `;

    const genderData = await this.repo
      .raw(genderQuery, { user_id })
      .then(({ rows }) => rows);

    const ageData = await this.repo
      .raw(ageQuery, { user_id })
      .then(({ rows }) => rows);

    const genderBreakdown = {
      male: { total: 0, percentage: 0 },
      female: { total: 0, percentage: 0 },
      unknown: { total: 0, percentage: 0 },
    };

    let totalGenderCount = 0;
    genderData.forEach(({ gender, total }) => {
      const count = Number(total);
      if (gender === 'male' || gender === 'female') {
        genderBreakdown[gender].total = count;
        totalGenderCount += count;
      } else {
        // Any other or null gender is considered unknown
        genderBreakdown.unknown.total += count;
        totalGenderCount += count;
      }
    });

    if (totalGenderCount > 0) {
      genderBreakdown.male.percentage = parseFloat(
        ((genderBreakdown.male.total / totalGenderCount) * 100).toFixed(2),
      );
      genderBreakdown.female.percentage = parseFloat(
        ((genderBreakdown.female.total / totalGenderCount) * 100).toFixed(2),
      );
      genderBreakdown.unknown.percentage = parseFloat(
        ((genderBreakdown.unknown.total / totalGenderCount) * 100).toFixed(2),
      );
    }

    const ALL_AGE_GROUPS = ['65-', '45-64', '35-44', '25-34', '18-24', '13-17'];

    const ageBreakdown = ALL_AGE_GROUPS.reduce((acc, group) => {
      acc[group] = {
        male: { total: 0 },
        female: { total: 0 },
        unknown: { total: 0 },
      };
      return acc;
    }, {} as Record<string, Record<string, { total: number }>>);

    ageData.forEach(({ age_group, gender, total }) => {
      if (age_group !== 'unknown' && ALL_AGE_GROUPS.includes(age_group)) {
        let genderKey = 'unknown';
        if (gender === 'male' || gender === 'female') {
          genderKey = gender;
        }
        ageBreakdown[age_group][genderKey].total = Number(total);
      }
    });

    return {
      gender: genderBreakdown,
      age: ageBreakdown,
    };
  }

  public async getWalletDetails(user_id: string): Promise<{
    balance: { amount: number; percentageChange: number };
    lastStatement: { amount: number; percentageChange: number };
    breakdown: Array<{
      payment_type: PaymentType;
      amount: number;
      percentage: number;
    }>;
  }> {
    const walletResult = await this.repo.raw(
      `SELECT *
       FROM wallets
       WHERE user_id = :user_id
         AND type = :type
       LIMIT 1`,
      { user_id, type: WalletType.FIAT },
    );
    const wallet = walletResult.rows[0];

    const transactionsResult = await this.repo.raw(
      `SELECT amount, type, created_at
       FROM transactions
       WHERE wallet_id = :wallet_id
         AND status = :status
       ORDER BY created_at DESC
       LIMIT 2`,
      { wallet_id: wallet.id, status: TransactionStatus.SUCCESSFUL },
    );
    const transactions = transactionsResult.rows;

    let balancePercentageChange = 0;
    if (transactions.length === 2) {
      const [latest, previous] = transactions;
      const latestNet =
        latest.type === TransactionType.CREDIT ? latest.amount : -latest.amount;
      const prevNet =
        previous.type === TransactionType.CREDIT
          ? previous.amount
          : -previous.amount;
      if (prevNet !== 0) {
        balancePercentageChange = parseFloat(
          (((latestNet - prevNet) / Math.abs(prevNet)) * 100).toFixed(2),
        );
      }
    }

    const lastStatementResult = await this.repo.raw(
      `SELECT SUM(CASE WHEN type = :credit THEN amount ELSE -amount END) AS statement_amount,
              MAX(created_at)                                            AS statement_date
       FROM transactions
       WHERE wallet_id = :wallet_id
         AND payment_type = :royalties
         AND status = :status
       GROUP BY DATE_TRUNC('month', created_at)
       ORDER BY statement_date DESC
       LIMIT 2`,
      {
        wallet_id: wallet.id,
        credit: TransactionType.CREDIT,
        royalties: PaymentType.ROYALTIES,
        status: TransactionStatus.SUCCESSFUL,
      },
    );

    const lastStatement: { amount: number; percentageChange: number } = {
      amount: 0,
      percentageChange: 0,
    };

    if (lastStatementResult.rows.length > 0) {
      const [curr, prev] = lastStatementResult.rows;
      lastStatement.amount = Number(curr.statement_amount);
      if (prev) {
        const prevAmt = Number(prev.statement_amount);
        if (prevAmt !== 0) {
          lastStatement.percentageChange = parseFloat(
            (
              ((lastStatement.amount - prevAmt) / Math.abs(prevAmt)) *
              100
            ).toFixed(2),
          );
        }
      }
    }

    const breakdownResult = await this.repo.raw(
      `SELECT payment_type,
              SUM(CASE WHEN type = :credit THEN amount ELSE -amount END)::float AS net_total
       FROM transactions
       WHERE wallet_id = :wallet_id
         AND status = :status
       GROUP BY payment_type`,
      {
        wallet_id: wallet.id,
        credit: TransactionType.CREDIT,
        status: TransactionStatus.SUCCESSFUL,
      },
    );

    const raw = breakdownResult.rows.map((r) => ({
      payment_type: r.payment_type as string,
      net_total: Number(r.net_total),
      abs_total: Math.abs(Number(r.net_total)),
    }));
    const sumAbs = raw.reduce((sum, r) => sum + r.abs_total, 0);

    const breakdown = raw.map((r) => ({
      payment_type: r.payment_type,
      amount: r.net_total,
      percentage:
        sumAbs > 0 ? parseFloat(((r.abs_total / sumAbs) * 100).toFixed(2)) : 0,
    }));

    return {
      balance: {
        amount: Number(wallet.balance),
        percentageChange: balancePercentageChange,
      },
      lastStatement,
      breakdown,
    };
  }
}
