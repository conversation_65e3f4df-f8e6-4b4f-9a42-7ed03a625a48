import { MODULE_TOKENS } from '@app/internal/ioc/tokens';
import { Repository } from '@app/internal/postgres/repository';
import { inject, injectable } from 'inversify';
import { Post } from './entities/post.entity';
import { ulid } from 'ulid';
import {
  SeekPaginationOption,
  SeekPaginationResult,
} from '@app/internal/postgres/pagination';
import {
  handleReplies,
  mmr,
  relevance,
  typeOfPost,
} from '@app/utils/post.utils';
import { format, formatISO, startOfDay, subDays, subMonths } from 'date-fns';
import { PerformanceDto } from '@app/http/controllers/analytics/dto/performance.dto';
import { ApplicationError } from '@app/internal/errors';
import { StatusCodes } from 'http-status-codes';
import { PeriodFilter } from '@app/http/controllers/analytics/dto/period.dto';
import {
  buildDateRange,
  Period,
  PeriodLength,
  previousPeriodStart,
} from '@app/utils/period.utils';

export type CreatePostData = Pick<
  Post,
  'content' | 'metadata' | 'user_id' | 'parent_id'
>;

export type UpdatePostData = Partial<
  Omit<Post, 'id' | 'created_at' | 'updated_at'>
>;

const IMPRESSIONS_CALC = `(p.views + p.anonymous_views + p.likes + p.plays + p.replies + p.shares) as impressions`;

@injectable()
export class PostService {
  private readonly qb = this.repo.createBuilder('posts');

  constructor(
    @inject(MODULE_TOKENS.Repository) private readonly repo: Repository<Post>,
  ) {}

  public async create(data: CreatePostData): Promise<Post> {
    return this.qb()
      .insert({ ...data, id: ulid() })
      .returning('*')
      .then(([post]) => post);
  }

  public async all(): Promise<Post[]> {
    return this.qb().select('*');
  }

  public async softDelete(id: string): Promise<void> {
    await this.qb()
      .where({ id })
      .update({ was_deleted: true, deleted_at: new Date() });
  }

  public async update(id: string, data: UpdatePostData): Promise<Post> {
    return this.qb()
      .where({ id })
      .update(data)
      .returning('*')
      .then(([post]) => post);
  }

  public async registerShare(id: string): Promise<void> {
    await this.qb().where({ id }).increment('shares', 1);
  }

  public async get(id: string, user_viewing_post_id?: string): Promise<Post> {
    let query = `SELECT p.id,
    p.likes,
    p.views,
    p.plays,
    p.anonymous_views,
    p.replies,
    p.shares,
    (p.views + p.anonymous_views + p.likes + p.plays + p.replies + p.shares) as impressions,
    CASE
      WHEN p.was_deleted = false THEN p.content
      ELSE NULL
    END AS content,
    CASE
      WHEN p.was_deleted = false THEN p.metadata
      ELSE NULL
    END AS metadata,
    p.user_id,
    p.parent_id,
    p.was_deleted,
    p.created_at,
    p.updated_at,
             json_build_object(
               'id', u.id,
               'first_name', u.first_name,
               'last_name', u.last_name,
               'display_name', u.display_name,
               'username', u.username,
               'category', u.category,
               'profile_picture', u.profile_picture,
               'community_id', u.community_id,
               'vibrate_id', u.vibrate_id
             ) AS author 
      FROM posts p
      LEFT JOIN users u ON p.user_id = u.id
      WHERE p.id = :id
    `;

    if (user_viewing_post_id) {
      query = `
      SELECT p.id,
            p.likes, p.views, p.plays, p.anonymous_views, p.replies, p.shares,
            ${IMPRESSIONS_CALC},
            CASE WHEN p.was_deleted = false THEN p.content ELSE NULL END AS content,
            CASE WHEN p.was_deleted = false THEN p.metadata ELSE NULL END AS metadata,
            p.user_id, p.parent_id, p.was_deleted, p.created_at, p.updated_at,
            json_build_object(
              'id', u.id, 'first_name', u.first_name, 'last_name', u.last_name,
              'display_name', u.display_name, 'username', u.username,
              'category', u.category, 'profile_picture', u.profile_picture, 'vibrate_id', u.vibrate_id,
              'community_id', u.community_id
            ) AS author,
            /* flags */
            CASE WHEN pl.user_id IS NOT NULL THEN true ELSE false END AS liked_by_you,
            CASE WHEN ps.user_id IS NOT NULL THEN true ELSE false END AS saved_by_you
      FROM   posts p
      LEFT  JOIN users      u  ON p.user_id = u.id
      LEFT  JOIN post_likes pl ON pl.user_id = :user_viewing_post_id AND pl.post_id = p.id
      LEFT  JOIN post_saves ps ON ps.user_id = :user_viewing_post_id AND ps.post_id = p.id
      WHERE  p.id = :id`;
    }

    const post = await this.repo
      .raw(query, { id, user_viewing_post_id })
      .then(({ rows }) => rows[0]);

    return post as Post;
  }

  public async getUserPost(id: string, user_id: string): Promise<Post> {
    return this.qb().where({ id }).andWhere({ user_id }).first('*');
  }

  public async fetchUsersPosts(
    user_id: string,
    {
      cursor,
      result_per_page,
      user_viewing_post_id,
    }: SeekPaginationOption<string> & Partial<{ user_viewing_post_id: string }>,
  ): Promise<SeekPaginationResult<Post, string>> {
    let query = `SELECT p.id,
    p.likes,
    p.views,
    p.replies,
    p.shares,
    p.plays,
    p.anonymous_views,
    ${IMPRESSIONS_CALC},
    p.content,
    p.metadata,
    p.user_id,
    p.parent_id,
    p.created_at,
    p.updated_at, 
    json_build_object(
      'id', u.id,
      'first_name', u.first_name,
      'last_name', u.last_name,
      'display_name', u.display_name,
      'username', u.username,
      'category', u.category,
      'profile_picture', u.profile_picture,
      'vibrate_id', u.vibrate_id
    ) AS author
    FROM posts p 
    LEFT JOIN users u ON p.user_id = u.id 
    WHERE p.user_id = :user_id AND p.was_deleted = false AND p.parent_id IS NULL ${
      cursor ? 'AND p.id < :cursor' : ''
    }
    ORDER BY p.created_at DESC, p.id DESC LIMIT :result_per_page`;

    if (user_viewing_post_id) {
      query = `
      SELECT p.id,
            p.likes, p.views, p.replies, p.shares, p.plays, p.anonymous_views,
            ${IMPRESSIONS_CALC},
            p.content, p.metadata, p.user_id, p.parent_id,
            p.created_at, p.updated_at,
            json_build_object(
              'id', u.id, 'first_name', u.first_name, 'last_name', u.last_name,
              'display_name', u.display_name, 'username', u.username,
              'category', u.category, 'profile_picture', u.profile_picture, 'vibrate_id', u.vibrate_id
            ) AS author,
            CASE WHEN pl.user_id IS NOT NULL THEN true ELSE false END AS liked_by_you,
            CASE WHEN ps.user_id IS NOT NULL THEN true ELSE false END AS saved_by_you
      FROM   posts p
      LEFT  JOIN users      u  ON p.user_id = u.id
      LEFT  JOIN post_likes pl ON pl.user_id = :user_viewing_post_id AND p.id = pl.post_id
      LEFT  JOIN post_saves ps ON ps.user_id = :user_viewing_post_id AND p.id = ps.post_id
      WHERE  p.user_id = :user_id
        AND  p.was_deleted = false
        AND  p.parent_id IS NULL
        ${cursor ? 'AND p.id < :cursor' : ''}
      ORDER BY p.created_at DESC, p.id DESC
      LIMIT  :result_per_page`;
    }

    const posts = await this.repo
      .raw(query, {
        cursor,
        result_per_page,
        user_id,
        user_viewing_post_id,
      })
      .then(({ rows }) => <Post[]>rows);

    return new SeekPaginationResult({
      result_per_page,
      cursor: posts.at(-1)?.id ?? null,
      result: posts,
    });
  }

  public async fetchUsersReplies(
    user_id: string,
    {
      cursor,
      result_per_page,
      user_viewing_post_id,
    }: SeekPaginationOption<string> & Partial<{ user_viewing_post_id: string }>,
  ): Promise<SeekPaginationResult<Post, string>> {
    let query = `SELECT p.id,
  p.likes,
  p.views,
  p.replies,
  p.shares,
  p.plays,
  p.anonymous_views,
  ${IMPRESSIONS_CALC},
  p.content,
  p.metadata,
  p.user_id,
  p.parent_id,
  p.created_at,
  p.updated_at,
  json_build_object(
    'id', u.id,
    'first_name', u.first_name,
    'last_name', u.last_name,
    'display_name', u.display_name,
    'username', u.username,
    'category', u.category,
    'profile_picture', u.profile_picture,
    'following_count', u.following_count,
    'follower_count', u.follower_count,
    'bio', u.bio,
    'accounts', u.accounts,
    'country', u.country,
    'collaborators', u.collaborators,
    'vibrate_id', u.vibrate_id
  ) AS author
  FROM posts p
  LEFT JOIN users u ON p.user_id = u.id
  WHERE p.user_id = :user_id AND p.parent_id IS NOT NULL AND p.was_deleted = false ${
    cursor ? 'AND p.id < :cursor' : ''
  }
  ORDER BY p.created_at DESC, p.id DESC LIMIT :result_per_page`;

    if (user_viewing_post_id) {
      query = `
      SELECT p.id,
            p.likes, p.views, p.replies, p.shares, p.plays, p.anonymous_views,
            ${IMPRESSIONS_CALC},
            p.content, p.metadata, p.user_id, p.parent_id,
            p.created_at, p.updated_at,
            json_build_object(
              'id', u.id, 'first_name', u.first_name, 'last_name', u.last_name,
              'display_name', u.display_name, 'username', u.username,
              'category', u.category, 'profile_picture', u.profile_picture, 'vibrate_id', u.vibrate_id
            ) AS author,
            CASE WHEN pl.user_id IS NOT NULL THEN true ELSE false END AS liked_by_you,
            CASE WHEN ps.user_id IS NOT NULL THEN true ELSE false END AS saved_by_you
      FROM   posts p
      LEFT  JOIN users      u  ON p.user_id = u.id
      LEFT  JOIN post_likes pl ON pl.user_id = :user_viewing_post_id AND p.id = pl.post_id
      LEFT  JOIN post_saves ps ON ps.user_id = :user_viewing_post_id AND p.id = ps.post_id
      WHERE  p.user_id = :user_id
        AND  p.parent_id IS NOT NULL
        AND  p.was_deleted = false
        ${cursor ? 'AND p.id < :cursor' : ''}
      ORDER BY p.created_at DESC, p.id DESC
      LIMIT  :result_per_page`;
    }

    const replies = await this.repo
      .raw(query, {
        cursor,
        result_per_page,
        user_id,
        user_viewing_post_id,
      })
      .then(({ rows }) => <Post[]>rows);

    return new SeekPaginationResult({
      result_per_page,
      cursor: replies.at(-1)?.id ?? null,
      result: replies,
    });
  }

  public async fetchUsersLikes(
    user_id: string,
    {
      cursor,
      result_per_page,
      user_viewing_post_id,
    }: SeekPaginationOption<string> & Partial<{ user_viewing_post_id: string }>,
  ): Promise<SeekPaginationResult<Post, string>> {
    let query = `SELECT p.id,
  p.likes,
  p.views,
  p.replies,
  p.shares,
  p.plays,
  p.anonymous_views,
  ${IMPRESSIONS_CALC},
  p.content,
  p.metadata,
  p.user_id,
  p.parent_id,
  p.created_at,
  p.updated_at,
  json_build_object(
    'id', u.id,
    'first_name', u.first_name,
    'last_name', u.last_name,
    'display_name', u.display_name,
    'username', u.username,
    'category', u.category,
    'profile_picture', u.profile_picture,
    'vibrate_id', u.vibrate_id
  ) AS author
  FROM posts p
  LEFT JOIN users u ON p.user_id = u.id
  INNER JOIN post_likes pl ON pl.post_id = p.id AND pl.user_id = :user_id
  WHERE p.was_deleted = false ${cursor ? 'AND p.id < :cursor' : ''}
  ORDER BY p.created_at DESC, p.id DESC LIMIT :result_per_page`;

    if (user_viewing_post_id) {
      query = `
      SELECT p.id,
            p.likes, p.views, p.replies, p.shares, p.plays, p.anonymous_views,
            ${IMPRESSIONS_CALC},
            p.content, p.metadata, p.user_id, p.parent_id,
            p.created_at, p.updated_at,
            json_build_object(
              'id', u.id, 'first_name', u.first_name, 'last_name', u.last_name,
              'display_name', u.display_name, 'username', u.username,
              'category', u.category, 'profile_picture', u.profile_picture, 'vibrate_id', u.vibrate_id
            ) AS author,
            CASE WHEN pl2.user_id IS NOT NULL THEN true ELSE false END AS liked_by_you,
            CASE WHEN ps.user_id IS NOT NULL THEN true ELSE false END AS saved_by_you
      FROM   posts p
      LEFT  JOIN users      u  ON p.user_id = u.id
      INNER JOIN post_likes pl  ON pl.post_id = p.id AND pl.user_id = :user_id     -- “liked” filter
      LEFT  JOIN post_likes pl2 ON pl2.user_id = :user_viewing_post_id AND p.id = pl2.post_id
      LEFT  JOIN post_saves ps  ON ps.user_id  = :user_viewing_post_id AND p.id = ps.post_id
      WHERE  p.was_deleted = false
        ${cursor ? 'AND p.id < :cursor' : ''}
      ORDER BY p.created_at DESC, p.id DESC
      LIMIT  :result_per_page`;
    }

    const likedPosts = await this.repo
      .raw(query, {
        cursor,
        result_per_page,
        user_id,
        user_viewing_post_id,
      })
      .then(({ rows }) => <Post[]>rows);

    return new SeekPaginationResult({
      result_per_page,
      cursor: likedPosts.at(-1)?.id ?? null,
      result: likedPosts,
    });
  }

  public async fetchPostParent(
    id: string,
    user_viewing_post_id?: string,
  ): Promise<Post> {
    let query = `SELECT p.id,
    p.likes,
    p.views,
    p.replies,
    p.shares,
    p.plays,
    p.anonymous_views,
    ${IMPRESSIONS_CALC},
    CASE
      WHEN p.was_deleted = false THEN p.content
      ELSE NULL
    END AS content,
    CASE
      WHEN p.was_deleted = false THEN p.metadata
      ELSE NULL
    END AS metadata,
    p.user_id,
    p.parent_id,
    p.was_deleted,
    p.created_at,
    p.updated_at,
    json_build_object(
      'id', u.id,
      'first_name', u.first_name,
      'last_name', u.last_name,
      'display_name', u.display_name,
      'username', u.username,
      'category', u.category,
      'profile_picture', u.profile_picture,
      'vibrate_id', u.vibrate_id
    ) AS author
    FROM posts p0
    INNER JOIN posts p ON p0.parent_id = p.id
    LEFT JOIN users u ON p.user_id = u.id
    WHERE p0.id = :id`;

    if (user_viewing_post_id) {
      query = `
      SELECT p.id,
            p.likes, p.views, p.replies, p.shares, p.plays, p.anonymous_views,
            ${IMPRESSIONS_CALC},
            CASE WHEN p.was_deleted = false THEN p.content  ELSE NULL END AS content,
            CASE WHEN p.was_deleted = false THEN p.metadata ELSE NULL END AS metadata,
            p.user_id, p.parent_id, p.was_deleted, p.created_at, p.updated_at,
            json_build_object(
              'id', u.id, 'first_name', u.first_name, 'last_name', u.last_name,
              'display_name', u.display_name, 'username', u.username,
              'category', u.category, 'profile_picture', u.profile_picture, 'vibrate_id', u.vibrate_id
            ) AS author,
            CASE WHEN pl.user_id IS NOT NULL THEN true ELSE false END AS liked_by_you,
            CASE WHEN ps.user_id IS NOT NULL THEN true ELSE false END AS saved_by_you
      FROM   posts p0
      INNER JOIN posts p ON p0.parent_id = p.id
      LEFT  JOIN users      u  ON p.user_id = u.id
      LEFT  JOIN post_likes pl ON pl.user_id = :user_viewing_post_id AND p.id = pl.post_id
      LEFT  JOIN post_saves ps ON ps.user_id = :user_viewing_post_id AND p.id = ps.post_id
      WHERE  p0.id = :id`;
    }

    return this.repo
      .raw(query, { id, user_viewing_post_id })
      .then(({ rows }) => rows?.[0]);
  }

  public async fetchPostChildren(
    id: string,
    {
      cursor,
      result_per_page,
      user_viewing_post_id,
    }: SeekPaginationOption<string> & Partial<{ user_viewing_post_id: string }>,
  ): Promise<SeekPaginationResult<Post, string>> {
    let query = `SELECT p.id,
    p.likes,
    p.views,
    p.replies,
    p.shares,
    p.plays,
    p.anonymous_views,
    ${IMPRESSIONS_CALC},
    CASE
      WHEN p.was_deleted = false THEN p.content
      ELSE NULL
    END AS content,
    CASE
      WHEN p.was_deleted = false THEN p.metadata
      ELSE NULL
    END AS metadata,
    p.user_id,
    p.parent_id,
    p.was_deleted,
    p.created_at,
    p.updated_at, 
    json_build_object(
      'id', u.id,
      'first_name', u.first_name,
      'last_name', u.last_name,
      'display_name', u.display_name,
      'username', u.username,
      'category', u.category,
      'profile_picture', u.profile_picture,
      'following_count', u.following_count,
      'follower_count', u.follower_count,
      'bio', u.bio,
      'accounts', u.accounts,
      'country', u.country,
      'collaborators', u.collaborators,
      'vibrate_id', u.vibrate_id
    ) AS author
    FROM posts p 
    LEFT JOIN users u ON p.user_id = u.id
    WHERE p.parent_id = :id ${!!cursor ? 'AND p.id < :cursor' : ''}
    ORDER BY p.created_at DESC, p.id DESC LIMIT :result_per_page`;

    if (user_viewing_post_id) {
      query = `
      SELECT p.id,
            p.likes, p.views, p.replies, p.shares, p.plays, p.anonymous_views,
            ${IMPRESSIONS_CALC},
            CASE WHEN p.was_deleted = false THEN p.content  ELSE NULL END AS content,
            CASE WHEN p.was_deleted = false THEN p.metadata ELSE NULL END AS metadata,
            p.user_id, p.parent_id, p.was_deleted, p.created_at, p.updated_at,
            json_build_object(
              'id', u.id, 
              'first_name', u.first_name, 
              'last_name', u.last_name,
              'display_name', u.display_name, 
              'username', u.username,
              'category', u.category, 
              'profile_picture', u.profile_picture, 
              'following_count', u.following_count,
              'follower_count', u.follower_count,
              'bio', u.bio,
              'accounts', u.accounts,
              'country', u.country,
              'collaborators', u.collaborators,
              'vibrate_id', u.vibrate_id
            ) AS author,
            CASE WHEN pl.user_id IS NOT NULL THEN true ELSE false END AS liked_by_you,
            CASE WHEN ps.user_id IS NOT NULL THEN true ELSE false END AS saved_by_you
      FROM   posts p
      LEFT  JOIN users      u  ON p.user_id = u.id
      LEFT  JOIN post_likes pl ON pl.user_id = :user_viewing_post_id AND p.id = pl.post_id
      LEFT  JOIN post_saves ps ON ps.user_id = :user_viewing_post_id AND p.id = ps.post_id
      WHERE  p.parent_id = :id
        ${cursor ? 'AND p.id < :cursor' : ''}
      ORDER BY p.created_at DESC, p.id DESC
      LIMIT  :result_per_page`;
    }

    const posts = await this.repo
      .raw(query, {
        id,
        user_viewing_post_id,
        cursor,
        result_per_page,
      })
      .then(({ rows }) => <Post[]>rows);

    return new SeekPaginationResult({
      result_per_page,
      cursor: posts.at(-1)?.id ?? null,
      result: posts,
    });
  }

  public async delete(id: string) {
    await this.qb().where({ id }).delete();
  }

  public async toggleLike(post_id: string, user_id: string): Promise<boolean> {
    const findQuery = `SELECT 1 FROM post_likes WHERE post_id = :post_id AND user_id = :user_id`;

    const result = await this.repo
      .raw(findQuery, { post_id, user_id })
      .then(({ rows }) => rows.length > 0);

    if (result) {
      const deleteQuery = `DELETE FROM post_likes WHERE post_id = :post_id AND user_id = :user_id`;
      await this.repo.raw(deleteQuery, { post_id, user_id });
    } else {
      const insertQuery = `INSERT INTO post_likes (post_id, user_id, liked_on) VALUES (:post_id, :user_id, :liked_on)`;
      await this.repo.raw(insertQuery, {
        post_id,
        user_id,
        liked_on: new Date(),
      });
    }

    return !result;
  }

  public async toggleSave(post_id: string, user_id: string): Promise<boolean> {
    const existsQuery = `
    SELECT 1 FROM post_saves WHERE post_id = :post_id AND user_id = :user_id
  `;

    const alreadySaved = await this.repo
      .raw(existsQuery, { post_id, user_id })
      .then(({ rows }) => rows.length > 0);

    if (alreadySaved) {
      await this.repo.raw(
        `DELETE FROM post_saves WHERE post_id = :post_id AND user_id = :user_id`,
        { post_id, user_id },
      );
    } else {
      await this.repo.raw(
        `INSERT INTO post_saves (post_id, user_id, saved_on)
       VALUES (:post_id, :user_id, NOW())`,
        { post_id, user_id },
      );
    }

    return !alreadySaved;
  }

  public async fetchUsersSaved(
    user_id: string,
    {
      cursor,
      result_per_page,
      user_viewing_post_id,
    }: SeekPaginationOption<string> & Partial<{ user_viewing_post_id: string }>,
  ): Promise<SeekPaginationResult<Post, string>> {
    const sql = `
      SELECT
        p.id,
        p.likes, p.views, p.replies, p.shares, p.plays, p.anonymous_views,
        ${IMPRESSIONS_CALC},
        p.content,
        p.metadata,
        p.user_id,
        p.parent_id,
        p.created_at,
        p.updated_at,
        json_build_object(
          'id',            u.id,
          'first_name',    u.first_name,
          'last_name',     u.last_name,
          'display_name',  u.display_name,
          'username',      u.username,
          'category',      u.category,
          'profile_picture', u.profile_picture,
          'vibrate_id',      u.vibrate_id
        ) AS author,
        CASE WHEN sv.save_id IS NOT NULL THEN true ELSE false END AS saved_by_you,
        CASE WHEN lk.like_id IS NOT NULL THEN true ELSE false END AS liked_by_you
      FROM      post_saves ps
      JOIN      posts      p  ON p.id = ps.post_id
      LEFT JOIN users      u  ON u.id = p.user_id

      LEFT JOIN LATERAL (
          SELECT 1 AS save_id
          FROM   post_saves
          WHERE  post_id = p.id
          AND    user_id = :viewer_id
          LIMIT  1
      ) sv ON true

      LEFT JOIN LATERAL (
          SELECT 1 AS like_id
          FROM   post_likes
          WHERE  post_id = p.id
          AND    user_id = :viewer_id
          LIMIT  1
      ) lk ON true

      WHERE ps.user_id = :user_id
        AND p.was_deleted = false
        ${cursor ? 'AND p.id < :cursor' : ''}
      ORDER BY ps.saved_on DESC, p.id DESC
      LIMIT  :limit OFFSET :offset
    `;

    const limit = result_per_page;
    const offset = result_per_page * (cursor ? 1 : 0);

    const rows = await this.repo
      .raw(sql, {
        user_id,
        viewer_id: user_viewing_post_id ?? user_id,
        cursor,
        limit,
        offset,
      })
      .then((r) => r.rows as Post[]);

    return new SeekPaginationResult({
      result_per_page,
      cursor: rows.at(-1)?.id ?? null,
      result: rows,
    });
  }

  public async viewPost(
    post_id: string,
    {
      user_id,
      metadata,
    }: {
      user_id?: string;
      metadata: {
        ip: string;
        country: string;
        device_info: {
          type: string;
          os: string;
          os_version: string;
        };
        client_info: {
          name: string;
          version: string;
          type: string;
          is_bot: boolean;
        };
      };
    },
  ): Promise<void> {
    const findQuery = `SELECT p.post_id AS post_id, 
                       p.user_id AS user_id
                       FROM post_views p 
                       WHERE p.post_id = :post_id 
                       AND ${
                         user_id
                           ? 'p.user_id = :user_id'
                           : "p.metadata ->> 'ip' = :ip"
                       }`;

    const viewed = await this.repo
      .raw(findQuery, { post_id, user_id: user_id ?? null, ip: metadata.ip })
      .then(({ rows }) => rows?.[0]);

    if (!viewed) {
      const insertQuery = `INSERT INTO post_views (id, post_id, user_id, metadata)
                           VALUES (:id, :post_id, :user_id, :metadata)`;

      await this.repo.raw(insertQuery, {
        id: ulid(),
        post_id,
        user_id: user_id ?? null,
        metadata,
      });
    }
  }

  public async createPostLink(
    post_id: string,
    link: string,
    user_id: string,
    link_id: string,
  ): Promise<void> {
    const insertQuery = `INSERT INTO post_links (id, clicks, link, post_id, user_id, created_at, updated_at) VALUES (:id, :clicks, :link, :post_id, :user_id, NOW(), NOW())`;
    await this.repo.raw(insertQuery, {
      id: link_id,
      clicks: 0,
      link,
      post_id,
      user_id,
    });
  }

  public async updateLinkClicks(link_id: string): Promise<void> {
    const updateQuery = `UPDATE post_links
                      SET clicks = clicks + 1, updated_at = NOW()
                      WHERE id = :link_id`;

    await this.repo.raw(updateQuery, { link_id });
  }

  public async registerPlay(
    id: string,
    {
      user_id,
      media_id,
      metadata,
    }: {
      user_id: string;
      media_id: string;
      metadata: { ip: string; country: string };
    },
  ) {
    const query = `SELECT 1
                   FROM post_plays pp
                   WHERE pp.post_id = :post_id
                     AND pp.user_id = :user_id
                     AND pp.media_id = :media_id`;

    const played = await this.repo
      .raw(query, {
        post_id: id,
        user_id,
        media_id,
      })
      .then(({ rows }) => rows?.length > 0);

    if (!played) {
      const query = `INSERT INTO post_plays (post_id, user_id, media_id, metadata)
                     VALUES (:post_id, :user_id, :media_id, :metadata)`;

      await this.repo.raw(query, {
        post_id: id,
        user_id,
        media_id,
        metadata,
      });
    }
  }

  public async getMediaPlays(media_id: string): Promise<number> {
    const query = `SELECT COUNT(*) as plays
                   FROM post_plays pp
                   WHERE pp.media_id = :media_id`;

    const { rows } = await this.repo.raw(query, { media_id });

    return rows[0].plays;
  }

  public async getLink(link_id: string): Promise<any> {
    const fetchQuery = `SELECT * FROM post_links WHERE id = :link_id LIMIT 1`;

    const link = await this.repo
      .raw(fetchQuery, { link_id })
      .then(({ rows }) => rows[0]);

    return link;
  }

  public async followingPosts(
    user_id: string,
    {
      cursor,
      result_per_page = 10,
      user_viewing_post_id,
    }: SeekPaginationOption<string> & Partial<{ user_viewing_post_id: string }>,
  ): Promise<SeekPaginationResult<Post, string>> {
    let query = `
    SELECT p.id,
      p.likes,
      p.views,
      p.plays,
      p.anonymous_views,
      p.replies,
      p.shares,
      (p.views + p.anonymous_views + p.likes + p.plays + p.replies + p.shares) AS impressions,
      p.content,
      p.metadata,
      p.user_id,
      p.parent_id,
      p.was_deleted,
      p.created_at,
      p.updated_at,
      json_build_object(
        'id', u.id,
        'first_name', u.first_name,
        'last_name', u.last_name,
        'display_name', u.display_name,
        'username', u.username,
        'category', u.category,
        'profile_picture', u.profile_picture,
        'vibrate_id', u.vibrate_id
      ) AS author
    FROM posts p
    JOIN followers f ON f.user_id = p.user_id
    JOIN users u ON p.user_id = u.id
    WHERE f.user_id = :user_id
      AND p.was_deleted = false
      ${cursor ? 'AND p.id < :cursor' : ''}
    ORDER BY p.created_at DESC, p.id DESC
    LIMIT :result_per_page
  `;

    if (user_viewing_post_id) {
      query = `
        SELECT p.id,
              p.likes, p.views, p.plays, p.anonymous_views, p.replies, p.shares,
              ${IMPRESSIONS_CALC},
              p.content, p.metadata, p.user_id, p.parent_id, p.was_deleted,
              p.created_at, p.updated_at,
              json_build_object(
                'id', u.id, 'first_name', u.first_name, 'last_name', u.last_name,
                'display_name', u.display_name, 'username', u.username,
                'category', u.category, 'profile_picture', u.profile_picture, 'vibrate_id', u.vibrate_id
              ) AS author,
              CASE WHEN pl.user_id IS NOT NULL THEN true ELSE false END AS liked_by_you,
              CASE WHEN ps.user_id IS NOT NULL THEN true ELSE false END AS saved_by_you
        FROM   posts p
        JOIN   followers f ON f.user_id = p.user_id
        JOIN   users     u ON p.user_id = u.id
        LEFT  JOIN post_likes pl ON pl.user_id = :user_viewing_post_id AND p.id = pl.post_id
        LEFT  JOIN post_saves ps ON ps.user_id = :user_viewing_post_id AND p.id = ps.post_id
        WHERE  f.user_id   = :user_id
          AND  p.was_deleted = false
          ${cursor ? 'AND p.id < :cursor' : ''}
        ORDER BY p.created_at DESC, p.id DESC
        LIMIT  :result_per_page`;
    }

    const posts = await this.repo
      .raw(query, {
        user_id,
        user_viewing_post_id,
        cursor,
        result_per_page,
      })
      .then(({ rows }) => rows as Post[]);

    return new SeekPaginationResult({
      result_per_page,
      cursor: posts.at(-1)?.id ?? null,
      result: posts,
    });
  }

  public async fetchUserInteractions(
    user_id: string,
    community_id: string,
  ): Promise<Map<string, number>> {
    const query = `
      SELECT p.id,
        (CASE WHEN pl.user_id IS NULL THEN 0 ELSE 1 END) +
        (CASE WHEN rp.user_id IS NULL THEN 0 ELSE 1 END) AS interactions
      FROM posts p

      JOIN users u ON u.id = p.user_id AND u.community_id = :community_id
      LEFT JOIN post_likes pl ON pl.post_id = p.id

      AND pl.user_id = :user_id
      LEFT JOIN post_views pv ON pv.post_id = p.id

      AND pv.user_id = :user_id
      LEFT JOIN posts rp ON rp.parent_id = p.id

      AND rp.user_id = :user_id
      WHERE  p.was_deleted = FALSE;
    `;

    const rows = await this.repo
      .raw(query, {
        user_id,
        community_id,
      })
      .then((r) => r.rows);

    return new Map(rows.map((r) => [r.id, r.interactions]));
  }

  public async fetchUsersFollowersActivity(
    user_id: string,
    community_id: string,
  ): Promise<Post[]> {
    const query = `
    SELECT DISTINCT p.*,
    ${IMPRESSIONS_CALC}
    FROM posts p
    JOIN users u ON u.id = p.user_id
    WHERE u.community_id = :community_id
      AND EXISTS (
        SELECT 1
        FROM followers f
        WHERE f.user_id = :user_id
          AND (EXISTS (SELECT 1 FROM post_likes pl WHERE pl.post_id = p.id AND pl.user_id = f.follower_id)
    OR EXISTS (SELECT 1 FROM post_views pv WHERE pv.post_id = p.id AND pv.user_id = f.follower_id)
    OR EXISTS (SELECT 1 FROM posts rp WHERE rp.parent_id = p.id AND rp.user_id = f.follower_id)
      )
    );
  `;

    const posts = await this.repo
      .raw(query, { user_id, community_id })
      .then(({ rows }) => rows as Post[]);

    return posts;
  }

  public async fetchPopularPosts(community_id: string): Promise<Post[]> {
    const query = `
    SELECT 
      p.*,
      ${IMPRESSIONS_CALC}
    FROM posts p
    JOIN users u ON p.user_id = u.id
    WHERE u.community_id = :community_id
    ORDER BY likes + views + replies DESC
  `;

    return await this.repo
      .raw(query, { community_id })
      .then(({ rows }) => rows as Post[]);
  }

  public async fetchRecentPosts(community_id: string): Promise<Post[]> {
    const query = `
    SELECT 
      p.*,
      ${IMPRESSIONS_CALC}
    FROM posts p
    JOIN users u ON p.user_id = u.id
    WHERE u.community_id = :community_id
    ORDER BY created_at DESC
  `;

    return await this.repo
      .raw(query, { community_id })
      .then(({ rows }) => rows as Post[]);
  }

  public async fetchUserFollowingPosts(
    user_id: string,
    community_id: string,
  ): Promise<Post[]> {
    const query = `
    SELECT 
      p.*,
      ${IMPRESSIONS_CALC}
    FROM posts p 
    JOIN followers f ON p.user_id = f.user_id 
    JOIN users u ON p.user_id = u.id
    WHERE f.user_id = :user_id AND u.community_id = :community_id
    ORDER BY p.created_at DESC
  `;

    return await this.repo
      .raw(query, { user_id, community_id })
      .then(({ rows }) => rows as Post[]);
  }

  public async recommendPosts(
    user_id: string,
    {
      cursor,
      result_per_page = 10,
      user_viewing_post_id,
    }: SeekPaginationOption<string> & Partial<{ user_viewing_post_id: string }>,
  ): Promise<SeekPaginationResult<Post, string>> {
    const community_id = await this.repo
      .raw(`SELECT community_id FROM users WHERE id = :user_id`, { user_id })
      .then(({ rows }) => rows[0]?.community_id);

    if (!community_id) {
      throw new ApplicationError(
        StatusCodes.NOT_FOUND,
        'User not found in a community',
      );
    }

    const viewedPostIds: string[] = await this.repo
      .raw(
        `SELECT post_id
          FROM post_views
          WHERE user_id  = :user_id
          AND viewed_on < (CURRENT_TIMESTAMP - INTERVAL '3 days')`,
        { user_id },
      )
      .then((r) => r.rows.map((row) => row.post_id));

    const [followersActivity, popular, recent, following] = await Promise.all([
      this.fetchUsersFollowersActivity(user_id, community_id),
      this.fetchPopularPosts(community_id),
      this.fetchRecentPosts(community_id),
      this.fetchUserFollowingPosts(user_id, community_id),
    ]);

    const candidatesMap = new Map<string, Post>();
    [followersActivity, popular, recent, following]
      .flat()
      .forEach((p) => !candidatesMap.has(p.id) && candidatesMap.set(p.id, p));

    let candidates = [...candidatesMap.values()]
      .filter((p) => !p.was_deleted)
      .filter((p) => !viewedPostIds.includes(p.id));

    if (candidates.length === 0) {
      return new SeekPaginationResult({
        result_per_page,
        cursor: null,
        result: [],
      });
    }

    const authorIds = [...new Set(candidates.map((p) => p.user_id))];

    const authorVerification = await this.repo
      .raw(
        `SELECT id, vibrate_id
          FROM users
          WHERE id = ANY(:ids)`,
        { ids: authorIds },
      )
      .then((r) => {
        const map: Record<string, boolean> = {};
        (r.rows as Array<{ id: string; vibrate_id: string | null }>).forEach(
          ({ id, vibrate_id }) => {
            map[id] = !!vibrate_id;
          },
        );
        return map;
      });

    candidates = candidates.map((p) => ({
      ...p,
      verified: authorVerification[p.user_id] ?? false,
    }));

    candidates = candidates.filter((p) => !viewedPostIds.includes(p.id));

    const userInteractions = await this.fetchUserInteractions(
      user_id,
      community_id,
    );
    const engagedPosts = candidates.filter((p) => userInteractions.has(p.id));

    const scoreMap = new Map<string, number>();
    candidates.forEach((p) =>
      scoreMap.set(p.id, relevance(p, userInteractions, engagedPosts)),
    );

    const ranked = mmr(candidates, scoreMap, result_per_page);
    const threaded = handleReplies(ranked);

    let startIdx = 0;
    if (cursor) {
      const pos = threaded.findIndex((p) => p.id === cursor);
      startIdx = pos >= 0 ? pos + 1 : 0;
    }
    const page = threaded.slice(startIdx, startIdx + result_per_page);

    if (page.length === 0) {
      return new SeekPaginationResult({
        result_per_page,
        cursor: null,
        result: [],
      });
    }

    const postIds = threaded.map((p) => p.id);
    const viewerId = user_viewing_post_id ?? user_id;

    const [authorRows, likeRows, saveRows] = await Promise.all([
      this.repo
        .raw(
          `SELECT u.id,
            json_build_object(
              'id', u.id,
              'first_name', u.first_name,
              'last_name', u.last_name,
              'display_name', u.display_name,
              'username', u.username,
              'category', u.category,
              'profile_picture', u.profile_picture,
              'follower_count', u.follower_count,
              'following_count', u.following_count,
              'bio', u.bio,
              'accounts', u.accounts,
              'collaborators', u.collaborators,
              'country', u.country,
              'vibrate_id', u.vibrate_id
            ) AS data
          FROM users u
          WHERE u.id = ANY(:ids)`,
          { ids: authorIds },
        )
        .then((r) => r.rows),

      this.repo
        .raw(
          `SELECT pl.post_id
            FROM post_likes pl
            WHERE pl.user_id = :viewer
              AND pl.post_id = ANY(:ids)`,
          { viewer: viewerId, ids: postIds },
        )
        .then((r) => r.rows),

      this.repo
        .raw(
          `SELECT ps.post_id
            FROM post_saves ps
            WHERE ps.user_id = :viewer
              AND ps.post_id = ANY(:ids)`,
          { viewer: viewerId, ids: postIds },
        )
        .then((r) => r.rows),
    ]);

    const authors = Object.fromEntries(authorRows.map((r) => [r.id, r.data]));
    const liked = new Set(likeRows.map((r) => r.post_id));
    const saved = new Set(saveRows.map((r) => r.post_id));

    const enriched = threaded.map((p) => ({
      ...p,
      author: authors[p.user_id] ?? null,
      liked_by_you: liked.has(p.id),
      saved_by_you: saved.has(p.id),
    }));

    return new SeekPaginationResult({
      result_per_page,
      cursor: enriched.at(-1)?.id ?? null,
      result: enriched,
    });
  }

  public async contentPerformance(
    user_id: string,
    payload: PerformanceDto,
  ): Promise<{
    daily: Record<string, number>;
    periods: { day: number; week: number; month: number };
  }> {
    const now = new Date();
    const daily: Record<string, number> = {};
    const sql = this.buildUserQuery(payload.filter);

    let buckets: { from: Date; to: Date; label: string }[];

    if (payload.from && payload.to) {
      buckets = buildDateRange(payload.from, payload.to);
    } else {
      const span = PeriodLength(payload.period);
      const baseDate = new Date();

      buckets = Array.from({ length: span }).map((_, i) => {
        const end = startOfDay(subDays(baseDate, i));
        const start = startOfDay(subDays(baseDate, i + 1));
        const label = format(start, 'yyyy MMM d');
        return { from: start, to: end, label };
      });
    }

    for (const { from, to, label } of buckets.reverse()) {
      const total = await this.repo
        .raw(sql, {
          user_id,
          start_date: formatISO(from),
          end_date: formatISO(to),
        })
        .then(({ rows }) => Number(rows[0]?.total) || 0);
      daily[label] = total;
    }

    const agg = async (from: Date, to: Date) =>
      this.repo
        .raw(sql, {
          user_id,
          start_date: formatISO(from),
          end_date: formatISO(to),
        })
        .then(({ rows }) => Number(rows[0]?.total) || 0);

    const periods = {
      day: await agg(startOfDay(subDays(now, 1)), now),
      week: await agg(startOfDay(subDays(now, 7)), now),
      month: await agg(startOfDay(subMonths(now, 1)), now),
    };

    return { daily, periods };
  }

  private buildUserQuery(filter: string): string {
    switch (filter) {
      case 'posts':
        return `
        SELECT COUNT(*) AS total
        FROM posts p
        WHERE p.user_id = :user_id
          AND p.created_at >= :start_date
          AND p.created_at <  :end_date
          AND p.was_deleted = false`;

      case 'views':
        return `
        SELECT COALESCE(SUM(p.views), 0) AS total
        FROM posts p
        WHERE p.user_id = :user_id
          AND p.created_at >= :start_date
          AND p.created_at <  :end_date
          AND p.was_deleted = false`;

      case 'likes':
        return `
        SELECT COALESCE(SUM(p.likes), 0) AS total
        FROM posts p
        WHERE p.user_id = :user_id
          AND p.created_at >= :start_date
          AND p.created_at <  :end_date
          AND p.was_deleted = false`;

      case 'comments':
        return `
        SELECT COALESCE(SUM(p.replies), 0) AS total
        FROM posts p
        WHERE p.user_id = :user_id
          AND p.created_at >= :start_date
          AND p.created_at <  :end_date
          AND p.was_deleted = false`;

      case 'mentions':
        return `
        SELECT COUNT(*) AS total
        FROM posts p
        JOIN users u ON u.id = :user_id
        CROSS JOIN LATERAL jsonb_array_elements(p.content -> 'tags') tag
        WHERE p.created_at >= :start_date
          AND p.created_at <  :end_date
          AND p.was_deleted = false
          AND p.user_id <> :user_id              -- exclude self-posts
          AND lower(tag->>'handle') = lower(u.username)`;

      case 'clicks':
        return `
        SELECT COALESCE(SUM(pl.clicks), 0) AS total
        FROM post_links pl
        JOIN posts p ON p.id = pl.post_id
        WHERE p.user_id = :user_id
          AND pl.user_id <> :user_id
          AND pl.created_at >= :start_date
          AND pl.created_at <  :end_date`;

      case 'shares':
        return `
        SELECT COALESCE(SUM(p.shares), 0) AS total
        FROM posts p
        WHERE p.user_id = :user_id
          AND p.created_at >= :start_date
          AND p.created_at <  :end_date
          AND p.was_deleted = false`;

      case 'visitors':
        return `
        SELECT COUNT(DISTINCT COALESCE(pv.viewer_id::text, pv.metadata ->> 'ip')) AS total
        FROM profile_views pv
        WHERE pv.profile_id = :user_id
          AND pv.viewed_on >= :start_date
          AND pv.viewed_on <  :end_date`;

      case 'saves':
        return `
        SELECT COUNT(DISTINCT ps.post_id) AS total
        FROM post_saves ps
        JOIN posts p ON p.id = ps.post_id
        WHERE p.user_id  = :user_id        -- the post belongs to the author
          AND ps.user_id <> :user_id       -- exclude saves by the author
          AND ps.saved_on >= :start_date
          AND ps.saved_on <  :end_date`;

      default:
        throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Invalid filter');
    }
  }

  public async topPosts(
    user_id: string,
    period: PeriodFilter,
    from?: Date,
    to?: Date,
  ): Promise<(Post & { postType: string })[]> {
    const start_date = from ? from : Period(period).from;
    const end_date = to ? to : Period(period).to;

    const query = `
    SELECT
      p.*,
      COALESCE(p.views, 0) AS total_views,
      u.username
    FROM posts p
    JOIN users u ON p.user_id = u.id
    WHERE
      p.user_id = :user_id
      AND p.created_at >= :start_date
      AND p.created_at < :end_date
      AND p.was_deleted = false
    ORDER BY total_views DESC
    LIMIT 5
  `;

    const posts = await this.repo
      .raw(query, {
        user_id,
        start_date,
        end_date,
      })
      .then(({ rows }) => rows as (Post & { username: string })[]);

    return posts.map((post) => ({
      ...post,
      postType: typeOfPost(post),
    }));
  }

  public async contentEngagement(
    user_id: string,
    filter?: PeriodFilter,
    from?: Date,
    to?: Date,
  ) {
    const currentStart = from ?? Period(filter).from;
    const currentEnd = to ?? Period(filter).to;

    const previousStart = previousPeriodStart(filter, currentStart, currentEnd);

    return this.statsQuery(user_id, currentStart, currentEnd, previousStart);
  }

  private async statsQuery(
    user_id: string,
    startDate: Date,
    endDate: Date,
    previousStartDate: Date,
  ): Promise<any> {
    const startDateISO = formatISO(startDate, { representation: 'complete' });
    const endDateISO = formatISO(endDate, { representation: 'complete' });
    const previousStartDateISO = formatISO(previousStartDate, {
      representation: 'complete',
    });

    const query = `
    WITH current_stats AS (
      SELECT
        COUNT(p.id)                           AS posts,
        COALESCE(SUM(p.views), 0)             AS views,
        COALESCE(SUM(p.likes), 0)             AS likes,
        COALESCE(SUM(p.replies), 0)           AS comments,
        COALESCE(SUM(p.shares), 0)            AS shares,
        ( SELECT COUNT(*)
          FROM   post_saves ps
          JOIN   posts      p2 ON p2.id = ps.post_id
          WHERE  p2.user_id = :user_id          -- post belongs to this user
            AND  ps.user_id <> :user_id         -- save done by someone else
            AND  ps.saved_on >= :start_date
            AND  ps.saved_on <  :end_date
        ) AS saves,
        COALESCE(SUM(pl.clicks), 0)           AS clicks,
        COALESCE(COUNT(pp.*), 0)              AS plays
      FROM posts p
      JOIN users u            ON p.user_id = u.id
      LEFT JOIN post_links pl ON p.id      = pl.post_id
      LEFT JOIN post_plays pp ON p.id      = pp.post_id
      WHERE p.user_id      = :user_id
        AND p.created_at  >= :start_date
        AND p.created_at  <  :end_date
        AND p.was_deleted = false
    ),
    previous_stats AS (
      SELECT
        COUNT(p.id)                           AS posts,
        COALESCE(SUM(p.views), 0)             AS views,
        COALESCE(SUM(p.likes), 0)             AS likes,
        COALESCE(SUM(p.replies), 0)           AS comments,
        COALESCE(SUM(p.shares), 0)            AS shares,
        ( SELECT COUNT(*)
          FROM   post_saves ps
          JOIN   posts      p2 ON p2.id = ps.post_id
          WHERE  p2.user_id = :user_id
            AND  ps.user_id <> :user_id
            AND  ps.saved_on >= :previous_start_date
            AND  ps.saved_on <  :start_date
        ) AS saves,
        COALESCE(SUM(pl.clicks), 0)           AS clicks,
        COALESCE(COUNT(pp.*), 0)              AS plays
      FROM posts p
      JOIN users u            ON p.user_id = u.id
      LEFT JOIN post_links pl ON p.id      = pl.post_id
      LEFT JOIN post_plays pp ON p.id      = pp.post_id
      WHERE p.user_id      = :user_id
        AND p.created_at  >= :previous_start_date
        AND p.created_at  <  :start_date
        AND p.was_deleted = false
    ),

    current_mentions AS (
      SELECT COALESCE(SUM(mention_count), 0) AS mentions
      FROM (
        SELECT COUNT(*) AS mention_count
        FROM posts p
        JOIN users u_target ON u_target.id = :user_id
        CROSS JOIN LATERAL jsonb_array_elements(p.content -> 'tags') tag
        WHERE p.created_at >= :start_date
          AND p.created_at < :end_date
          AND p.was_deleted = false
          AND p.user_id <> :user_id
          AND lower(tag->>'handle') = lower(u_target.username)
        GROUP BY p.id
      ) sub
    ),

    previous_mentions AS (
      SELECT COALESCE(SUM(mention_count), 0) AS mentions
      FROM (
        SELECT COUNT(*) AS mention_count
        FROM posts p
        JOIN users u_target ON u_target.id = :user_id
        CROSS JOIN LATERAL jsonb_array_elements(p.content -> 'tags') tag
        WHERE p.created_at >= :previous_start_date
          AND p.created_at < :start_date
          AND p.was_deleted = false
          AND p.user_id <> :user_id
          AND lower(tag->>'handle') = lower(u_target.username)
        GROUP BY p.id
      ) sub
    ),

    current_visitors AS (
      SELECT COUNT(DISTINCT COALESCE(pv.viewer_id::text, pv.metadata->>'ip')) AS visitors
      FROM profile_views pv
      WHERE pv.profile_id = :user_id
        AND pv.viewed_on >= :start_date
        AND pv.viewed_on <  :end_date
    ),

    previous_visitors AS (
      SELECT COUNT(DISTINCT COALESCE(pv.viewer_id::text, pv.metadata->>'ip')) AS visitors
      FROM profile_views pv
      WHERE pv.profile_id = :user_id
        AND pv.viewed_on >= :previous_start_date
        AND pv.viewed_on <  :start_date
    )

    SELECT
      c.posts,
      c.views,
      c.likes,
      c.comments,
      c.shares,
      c.saves,
      c.clicks,
      c.plays,
      cm.mentions,
      cv.visitors,

      /* percentage deltas */
      CASE WHEN p.posts = 0 AND c.posts = 0 THEN 0
          WHEN p.posts = 0 THEN 100
          ELSE ROUND( ((c.posts - p.posts)::numeric / p.posts) * 100, 2 )
      END AS post_change,

      CASE WHEN p.views = 0 AND c.views = 0 THEN 0
          WHEN p.views = 0 THEN 100
          ELSE ROUND( ((c.views - p.views)::numeric / p.views) * 100, 2 )
      END AS view_change,

      CASE WHEN p.likes = 0 AND c.likes = 0 THEN 0
          WHEN p.likes = 0 THEN 100
          ELSE ROUND( ((c.likes - p.likes)::numeric / p.likes) * 100, 2 )
      END AS like_change,

      CASE WHEN p.comments = 0 AND c.comments = 0 THEN 0
          WHEN p.comments = 0 THEN 100
          ELSE ROUND( ((c.comments - p.comments)::numeric / p.comments) * 100, 2 )
      END AS comment_change,

      CASE WHEN p.shares = 0 AND c.shares = 0 THEN 0
          WHEN p.shares = 0 THEN 100
          ELSE ROUND( ((c.shares - p.shares)::numeric / p.shares) * 100, 2 )
      END AS share_change,

      CASE WHEN p.saves = 0 AND c.saves = 0 THEN 0
          WHEN p.saves = 0 THEN 100
          ELSE ROUND( ((c.saves - p.saves)::numeric / p.saves) * 100, 2 )
      END AS save_change,

      CASE WHEN p.clicks = 0 AND c.clicks = 0 THEN 0
          WHEN p.clicks = 0 THEN 100
          ELSE ROUND( ((c.clicks - p.clicks)::numeric / p.clicks) * 100, 2 )
      END AS click_change,

      CASE WHEN p.plays = 0 AND c.plays = 0 THEN 0
          WHEN p.plays = 0 THEN 100
          ELSE ROUND( ((c.plays - p.plays)::numeric / p.plays) * 100, 2 )
      END AS plays_change,

      CASE WHEN pm.mentions = 0 AND cm.mentions = 0 THEN 0
          WHEN pm.mentions = 0 THEN 100
          ELSE ROUND( ((cm.mentions - pm.mentions)::numeric / pm.mentions) * 100, 2 )
      END AS mention_change,

      CASE WHEN pv.visitors = 0 AND cv.visitors = 0 THEN 0
          WHEN pv.visitors = 0 THEN 100
          ELSE ROUND( ((cv.visitors - pv.visitors)::numeric / pv.visitors) * 100, 2 )
      END AS visitor_change

    FROM
      current_stats      c,
      previous_stats     p,
      current_mentions   cm,
      previous_mentions  pm,
      current_visitors   cv,
      previous_visitors  pv;
  `;

    const { rows } = await this.repo.raw(query, {
      user_id,
      start_date: startDateISO,
      end_date: endDateISO,
      previous_start_date: previousStartDateISO,
    });

    return rows[0];
  }
}
