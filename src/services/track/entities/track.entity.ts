import { BaseEntity } from '@app/internal/postgres/base.entity';
import PartialInstantiable from '@app/utils/partial-instantiable';

export interface Publisher {
  name: string;
  role: string;
}

export interface Contributors {
  name: string;
  role: string;
  artistId?: number;
  isPrimary?: boolean;
  roleId?: number;
  releaseId?: null;
  trackId?: null;
  contributorId?: null;
}

export type ExternalID = {
  distributorStoreId: number;
  profileId: string;
};

export class LocalizedName extends PartialInstantiable<LocalizedName> {
  language: number;
  name: string;
}

export class LocalizedTitle extends PartialInstantiable<LocalizedTitle> {
  language: number;
  title: string;
  version: string;
}

export type Local = {
  language_id: number;
  name: string;
};
export interface TrackLocal extends Local {
  version: string;
}

export interface ArtistLocal extends Local {
  artistId: null | number;
}

export type Composer = {
  composerName: string;
  rightsId: number;
  roleId: number;
  share: number;
};

export enum TrackType {
  ORIGINAL_SONG = 1,
  COVER_SONG,
  PUBLIC_DOMAIN_SONG,
}

export class Track extends BaseEntity<Track> {
  title: string;
  version: string;
  localized_titles: LocalizedTitle[];
  type: TrackType;
  properties: (1 | 2 | 3 | 4 | 5 | 6 | 7 | 8)[];
  audio: string;
  audio_metadata: Partial<{
    filename: string;
    file_size: number;
    file_id: string;
  }>;
  user_id: string;
  isrc: string;
  duration: number;
  bit_rate: number;
  sample_rate: number;
  channels: number;
  bit_depth: number;
  start_time: number;
  catalog_id: string;
  language: string;
  explicit: boolean;
  lyrics: string;
  release_ids: string[];
  publisher: {
    id: number;
    name: string;
    role: number;
    share: number;
    publishing_type: number;
    publication_id: number;
    publication_name: string;
    country: string;
  }[];
  contributors: Contributors[];
  primary_genre: string;
  secondary_genre: string;
  copyright: { year: number; entity: string };
}
