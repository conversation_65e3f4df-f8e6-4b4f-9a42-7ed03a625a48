import { BaseEntity } from '@app/internal/postgres/base.entity';

export class Community extends BaseEntity<Community> {
  name: string;
  status: 'pending' | 'review' | 'active' | 'suspended' | 'investigate';
  is_private: boolean;
  custom_domain: string;
  custom_domain_active: boolean;
  account_icon: string;
  favicon: string;
  brand_themed_logo: string;
  brand_color: string;
  support_email: string;
  phone: string;
  enabled_members: string[];
  enabled_sso: string[];
  has_booked_call: boolean;
  paid_setup_fees: boolean;
  statement_logo: boolean;
  _intercom_id: string;
  _google_analytics_id: string;
}
