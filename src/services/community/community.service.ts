import { inject, injectable } from 'inversify';
import { MODULE_TOKENS } from '@app/internal/ioc/tokens';
import { Repository } from '@app/internal/postgres/repository';
import { Community } from '@app/services/community/entities/community.entity';
import { ulid } from 'ulid';
import { PartialExcept } from '@app/internal/types';
import { calculatePercentageChange, typeOfPost } from '@app/utils/post.utils';
import {
  addDays,
  endOfDay,
  endOfMonth,
  format,
  formatISO,
  startOfDay,
  startOfMonth,
  subDays,
  subMonths,
} from 'date-fns';
import { ApplicationError } from '@app/internal/errors';
import { StatusCodes } from 'http-status-codes';
import { PerformanceDto } from '@app/http/controllers/analytics/dto/performance.dto';
import { Post } from '../post/entities/post.entity';
import {
  OffsetPaginationResult,
  OffsetPaginationResultBuilder,
  SeekPaginationOption,
  SeekPaginationResult,
} from '@app/internal/postgres/pagination';
import { PeriodFilter } from '@app/http/controllers/analytics/dto/period.dto';
import {
  Period,
  PeriodLength,
  previousPeriodStart,
} from '@app/utils/period.utils';
import { ReportStatus } from '../reports/entities/reports.entities';

export type CreateCommunity = PartialExcept<
  Omit<Community, 'id' | 'created_at' | 'updated_at'>,
  'name'
>;

export type UpdateCommunity = Partial<Omit<Community, 'id'>>;

const IMPRESSIONS_CALC = `(p.views + p.anonymous_views + p.likes + p.plays + p.replies + p.shares) as impressions`;

@injectable()
export class CommunityService {
  private readonly qb = this.repo.createBuilder('communities');

  constructor(
    @inject(MODULE_TOKENS.Repository)
    private readonly repo: Repository<Community>,
  ) {}

  public async overview(): Promise<{
    total_communities: number;
    active: { total: number; change: number };
    pending: { total: number; change: number };
    review: { total: number; change: number };
    investigate: { total: number; change: number };
    suspended: { total: number; change: number };
  }> {
    const now = new Date();
    const pastDateISO = formatISO(subMonths(now, 1), {
      representation: 'complete',
    });

    const currentSql = `
    SELECT
      COUNT(*)                                                   AS total,
      COUNT(*) FILTER (WHERE status = 'active')                  AS active,
      COUNT(*) FILTER (WHERE status = 'pending')                 AS pending,
      COUNT(*) FILTER (WHERE status = 'review')                  AS review,
      COUNT(*) FILTER (WHERE status = 'investigate')             AS investigate,
      COUNT(*) FILTER (WHERE status = 'suspended')               AS suspended
    FROM communities
  `;

    const cur = await this.repo.raw(currentSql).then(({ rows }) => rows[0]);

    const { total, active, pending, review, investigate, suspended } =
      Object.fromEntries(
        Object.entries(cur).map(([k, v]) => [k, Number(v) || 0]),
      ) as Record<string, number>;

    const previousSql = `
    SELECT
      COUNT(*) FILTER (WHERE status = 'active'      AND created_at < :past) AS active,
      COUNT(*) FILTER (WHERE status = 'pending'     AND created_at < :past) AS pending,
      COUNT(*) FILTER (WHERE status = 'review'      AND created_at < :past) AS review,
      COUNT(*) FILTER (WHERE status = 'investigate' AND created_at < :past) AS investigate,
      COUNT(*) FILTER (WHERE status = 'suspended'   AND created_at < :past) AS suspended
    FROM communities
  `;

    const prev = await this.repo
      .raw(previousSql, { past: pastDateISO })
      .then(({ rows }) => rows[0]);

    const prevActive = Number(prev.active) || 0;
    const prevPending = Number(prev.pending) || 0;
    const prevReview = Number(prev.review) || 0;
    const prevInvestigate = Number(prev.investigate) || 0;
    const prevSuspended = Number(prev.suspended) || 0;

    return {
      total_communities: total,
      active: {
        total: active,
        change: calculatePercentageChange(active, prevActive),
      },
      pending: {
        total: pending,
        change: calculatePercentageChange(pending, prevPending),
      },
      review: {
        total: review,
        change: calculatePercentageChange(review, prevReview),
      },
      investigate: {
        total: investigate,
        change: calculatePercentageChange(investigate, prevInvestigate),
      },
      suspended: {
        total: suspended,
        change: calculatePercentageChange(suspended, prevSuspended),
      },
    };
  }

  public async overviewByCommunity(community_id: string): Promise<{
    total_communities: number;
    pending: { total: number; change: number };
    resolved: { total: number; change: number };
    taken_down: { total: number; change: number };
  }> {
    const now = new Date();
    const periodStart = subMonths(now, 1);
    const prevPeriod = subMonths(periodStart, 1);

    const [{ total_communities }] = await this.repo
      .raw(
        `SELECT COUNT(*)::int AS total_users
       FROM users
      WHERE community_id = :community_id`,
        { community_id },
      )
      .then((r) => r.rows);

    const countReports = async (from: Date, to: Date) => {
      const rows = await this.repo.kx
        .select('status')
        .count('id as cnt')
        .from<Report>('reports')
        .where('community_id', community_id)
        .andWhere('reported_on', '>=', from)
        .andWhere('reported_on', '<', to)
        .groupBy('status');

      const out: Record<ReportStatus, number> = {
        [ReportStatus.PENDING]: 0,
        [ReportStatus.RESOLVED]: 0,
        [ReportStatus.TAKEN_DOWN]: 0,
      };
      rows.forEach((r) => (out[r.status as ReportStatus] = Number(r.cnt) || 0));
      return out;
    };

    const current = await countReports(periodStart, now);
    const prev = await countReports(prevPeriod, periodStart);

    return {
      total_communities,
      pending: {
        total: current[ReportStatus.PENDING],
        change: calculatePercentageChange(
          current[ReportStatus.PENDING],
          prev[ReportStatus.PENDING],
        ),
      },
      resolved: {
        total: current[ReportStatus.RESOLVED],
        change: calculatePercentageChange(
          current[ReportStatus.RESOLVED],
          prev[ReportStatus.RESOLVED],
        ),
      },
      taken_down: {
        total: current[ReportStatus.TAKEN_DOWN],
        change: calculatePercentageChange(
          current[ReportStatus.TAKEN_DOWN],
          prev[ReportStatus.TAKEN_DOWN],
        ),
      },
    };
  }

  public async fetch(
    page_number = 1,
    result_per_page = 10,
    status?: string,
    search_term?: string,
  ): Promise<OffsetPaginationResult<Community>> {
    const builder = new OffsetPaginationResultBuilder<Community>();

    let baseQuery = this.qb();

    if (status) {
      baseQuery = baseQuery.where('status', status);
    }

    if (search_term?.trim()) {
      const term = `%${search_term.trim()}%`;
      baseQuery = baseQuery.andWhere((qb) =>
        qb.whereILike('name', term).orWhereILike('custom_domain', term),
      );
    }

    const total = await baseQuery
      .clone()
      .count('*', { as: 'total' })
      .then(([row]) => Number(row.total));

    const pager = builder
      .totalRecordCount(total)
      .pageNumber(page_number)
      .resultPerPage(result_per_page);

    const communities = await baseQuery
      .clone()
      .select('*')
      .orderBy('created_at', 'desc')
      .limit(result_per_page)
      .offset(pager.offset());

    return pager.build(communities);
  }

  public async find(
    filter?: Partial<
      Pick<Community, 'is_private' | 'custom_domain' | 'custom_domain_active'>
    >,
  ): Promise<Community[]> {
    return this.qb().where(filter).select('*');
  }

  public async get(id: string): Promise<Community> {
    return this.qb().where({ id }).first('*');
  }

  public async queryByName(name: string): Promise<Community> {
    return this.repo
      .raw('select * from communities c where lower(c.name) = lower(:name)', {
        name,
      })
      .then(({ rows }) => rows?.[0]);
  }

  public async getWithCustomDomain(custom_domain: string): Promise<Community> {
    return this.qb().where({ custom_domain }).first('*');
  }

  public async create(data: CreateCommunity): Promise<Community> {
    return this.qb()
      .insert({ ...data, id: ulid() })
      .returning('*')
      .then(([val]) => val);
  }

  public async update(id: string, data: UpdateCommunity): Promise<Community> {
    return this.qb()
      .where({ id })
      .update(data)
      .returning('*')
      .then(([updatedVal]) => updatedVal);
  }

  public async communityChannel(community_id: string): Promise<{
    creators: number;
    fans: number;
    brands: number;
    total_community: number;
    creator_change: number;
    fan_change: number;
    brand_change: number;
  }> {
    const currentDate = new Date();
    const pastDate = subMonths(currentDate, 1); // One month ago
    const pastDateISO = formatISO(pastDate, { representation: 'complete' });

    const currentStatsQuery = `
    SELECT
      COUNT(*) FILTER (WHERE category = 'creator') AS creators,
      COUNT(*) FILTER (WHERE category = 'fan') AS fans,
      COUNT(*) FILTER (WHERE category = 'brand') AS brands
    FROM users
    WHERE community_id = :community_id
  `;

    const currentResult = await this.repo
      .raw(currentStatsQuery, { community_id })
      .then(({ rows }) => rows[0]);

    const creators = Number(currentResult.creators) || 0;
    const fans = Number(currentResult.fans) || 0;
    const brands = Number(currentResult.brands) || 0;
    const totalCommunity = creators + fans + brands;

    // Past counts for each category (one month ago)
    const pastStatsQuery = `
    SELECT
      COUNT(*) FILTER (WHERE category = 'creator' AND created_at < :past_date) AS creators_last_month,
      COUNT(*) FILTER (WHERE category = 'fan' AND created_at < :past_date) AS fans_last_month,
      COUNT(*) FILTER (WHERE category = 'brand' AND created_at < :past_date) AS brands_last_month
    FROM users
    WHERE community_id = :community_id
  `;

    const pastResult = await this.repo
      .raw(pastStatsQuery, { community_id, past_date: pastDateISO })
      .then(({ rows }) => rows[0]);

    const creatorsLastMonth = Number(pastResult.creators_last_month) || 0;
    const fansLastMonth = Number(pastResult.fans_last_month) || 0;
    const brandsLastMonth = Number(pastResult.brands_last_month) || 0;

    function safePercentageChange(current: number, past: number): number {
      if (past === 0 && current === 0) {
        return 0;
      } else if (past === 0 && current > 0) {
        return 100;
      } else {
        return Number((((current - past) / past) * 100).toFixed(2));
      }
    }

    const creatorChange = Number(
      safePercentageChange(creators, creatorsLastMonth).toFixed(2),
    );
    const fanChange = Number(
      safePercentageChange(fans, fansLastMonth).toFixed(2),
    );
    const brandChange = Number(
      safePercentageChange(brands, brandsLastMonth).toFixed(2),
    );

    return {
      creators,
      fans,
      brands,
      total_community: totalCommunity,
      creator_change: creatorChange,
      fan_change: fanChange,
      brand_change: brandChange,
    };
  }

  public async profileViewerDeviceStats(
    community_id: string,
    period: PeriodFilter,
  ) {
    const time_line = Period(period);
    const past_date = time_line.from;
    const current_date = time_line.to;

    const period_duration =
      new Date(current_date).getTime() - new Date(past_date).getTime();
    const prev_period_end = new Date(past_date);
    const prev_period_start = new Date(
      prev_period_end.getTime() - period_duration,
    );

    const query = `with curr_nums as (select u.id as id,
                                    pv.id as view_id,
                                    coalesce(pv.metadata -> 'device_info' ->> 'type', 'unknown') as type
                              from users u
                                  join profile_views pv on u.id = pv.viewer_id
                              where u.community_id = :community_id
                                and pv.viewed_on >= :past_date
                                and pv.viewed_on <= :current_date),
                    prev_nums as (select u.id as id,
                                pv.id as view_id,
                                coalesce(pv.metadata -> 'device_info' ->> 'type', 'unknown') as type
                            from users u
                                join profile_views pv on u.id = pv.viewer_id
                            where u.community_id = :community_id
                                and pv.viewed_on >= :prev_period_start
                                and pv.viewed_on < :past_date),
                    prev_stats as (select pn.type as type, count(*) as view_count
                            from prev_nums pn
                            group by pn.type),
                    curr_stats as (select cn.type as type, count(*) as view_count
                            from curr_nums cn
                            group by cn.type),
                    total as (select sum(view_count) as total_view_count
                            from curr_stats)
            select cs.type as type,
                  cs.view_count AS views,
                  case
                      when total.total_view_count > 0 then
                          round((cs.view_count::numeric * 100) / total.total_view_count, 2)
                      else 0 end as percentage_view,
                  case
                      when ps.view_count is not null then
                          round(((cs.view_count - ps.view_count) / ps.view_count) * 100, 2)
                      else 100::numeric(13,2) end as diff
            from curr_stats cs
                left join prev_stats ps on cs.type = ps.type
                cross join total
            order by cs.view_count desc`;

    return this.repo
      .raw(query, {
        community_id,
        past_date,
        current_date,
        prev_period_start,
      })
      .then(({ rows }) => rows);
  }

  public async profileViewStats(community_id: string, period: PeriodFilter) {
    const time_line = Period(period);
    const past_date = time_line.from;
    const current_date = time_line.to;

    const query = `
    WITH profile_views_in_past_month AS (
      SELECT
        pv.id,
        pv.viewer_id,
        COALESCE(pv.metadata->>'country', 'Unknown') AS country
      FROM profile_views pv
      JOIN users u ON u.id = pv.profile_id
      WHERE u.community_id = :community_id
        AND pv.viewed_on >= :past_date
        AND pv.viewed_on < :current_date
    ),
    profile_view_stats AS (
      SELECT
        country,
        COUNT(*) AS view_count
      FROM profile_views_in_past_month
      GROUP BY country
    ),
    total_view_count AS (
      SELECT COALESCE(SUM(view_count), 0) AS total
      FROM profile_view_stats
    )
    SELECT
      pvs.country,
      pvs.view_count AS views,
      CASE WHEN tvc.total > 0 THEN
        ROUND((pvs.view_count::numeric * 100 / tvc.total), 2)
      ELSE 0 END AS percentage_view
    FROM profile_view_stats pvs
    CROSS JOIN total_view_count tvc
    ORDER BY pvs.view_count DESC;
  `;

    return this.repo
      .raw(query, {
        community_id,
        past_date,
        current_date,
      })
      .then(({ rows }) => rows);
  }

  public async contentPerformance(
    community_id: string,
    payload: PerformanceDto,
  ): Promise<{
    daily: Record<string, number>;
    periods: { day: number; week: number; month: number };
  }> {
    const now = new Date();

    const daily: Record<string, number> = {};
    const span = PeriodLength(payload.period);

    const rangeFor = (i: number): { from: Date; to: Date } => {
      if (
        payload.period === PeriodFilter.last_year ||
        payload.period === PeriodFilter.last_6_months
      ) {
        const to = endOfMonth(subMonths(now, i));
        const from = startOfMonth(to);
        return { from, to: addDays(to, 1) };
      }
      const to = endOfDay(subDays(now, i));
      const from = startOfDay(to);
      return { from, to: addDays(to, 1) };
    };

    const sql = this.buildQuery(payload.filter);

    for (let i = 0; i < span; i++) {
      const { from, to } = rangeFor(i);
      const total = await this.repo
        .raw(sql, {
          community_id,
          start_date: formatISO(from),
          end_date: formatISO(to),
        })
        .then(({ rows }) => Number(rows[0]?.total) || 0);

      const label = format(
        from,
        payload.period === PeriodFilter.last_year ||
          payload.period === PeriodFilter.last_6_months
          ? 'yyyy MMM'
          : 'yyyy MMM d',
      );
      daily[label] = total;
    }

    const periods: { day: number; week: number; month: number } = {
      day: 0,
      week: 0,
      month: 0,
    };

    const fetchTotal = async (from: Date, to: Date) =>
      this.repo
        .raw(sql, {
          community_id,
          start_date: formatISO(from),
          end_date: formatISO(to),
        })
        .then(({ rows }) => Number(rows[0]?.total) || 0);

    periods.day = await fetchTotal(startOfDay(subDays(now, 1)), now);
    periods.week = await fetchTotal(startOfDay(subDays(now, 7)), now);
    periods.month = await fetchTotal(startOfDay(subMonths(now, 1)), now);

    return { daily, periods };
  }

  private buildQuery(filter: string): string {
    switch (filter) {
      case 'posts':
        return `
        SELECT COUNT(*) AS total
        FROM posts p
        JOIN users u ON u.id = p.user_id
        WHERE p.created_at >= :start_date
          AND p.created_at <  :end_date
          AND u.community_id = :community_id
          AND p.was_deleted  = false`;

      case 'views':
        return `
        SELECT COALESCE(SUM(p.views), 0) AS total
        FROM posts p
        JOIN users u ON u.id = p.user_id
        WHERE p.created_at >= :start_date
          AND p.created_at <  :end_date
          AND u.community_id = :community_id
          AND p.was_deleted  = false`;

      case 'likes':
        return `
        SELECT COALESCE(SUM(p.likes), 0) AS total
        FROM posts p
        JOIN users u ON u.id = p.user_id
        WHERE p.created_at >= :start_date
          AND p.created_at <  :end_date
          AND u.community_id = :community_id
          AND p.was_deleted  = false`;

      case 'comments':
        return `
        SELECT COUNT(*) AS total
        FROM posts p
        JOIN users u ON u.id = p.user_id
        WHERE p.parent_id IS NOT NULL
          AND p.created_at >= :start_date
          AND p.created_at <  :end_date
          AND u.community_id = :community_id
          AND p.was_deleted  = false`;

      case 'mentions':
        return `
        SELECT COUNT(*) AS total
        FROM posts p
        JOIN users u ON u.id = p.user_id
        CROSS JOIN LATERAL jsonb_array_elements(p.content -> 'tags') tag
        WHERE p.created_at >= :start_date
          AND p.created_at <  :end_date
          AND u.community_id = :community_id
          AND p.was_deleted  = false
          AND lower(tag->>'handle') = lower(u.username)`;

      case 'clicks':
        return `
        SELECT COALESCE(SUM(pl.clicks), 0) AS total
        FROM posts p
        JOIN post_links pl ON pl.post_id = p.id
        JOIN users u ON u.id = p.user_id
        WHERE pl.created_at >= :start_date
          AND pl.created_at <  :end_date
          AND u.community_id = :community_id`;

      case 'shares':
        return `
        SELECT COALESCE(SUM(p.shares), 0) AS total
        FROM posts p
        JOIN users u ON u.id = p.user_id
        WHERE p.created_at >= :start_date
          AND p.created_at <  :end_date
          AND u.community_id = :community_id
          AND p.was_deleted  = false`;

      case 'visitors':
        return `
        SELECT COUNT(DISTINCT COALESCE(pv.viewer_id::text, pv.metadata ->> 'ip')) AS total
        FROM profile_views pv
        JOIN users u ON u.id = pv.profile_id
        WHERE pv.viewed_on >= :start_date
          AND pv.viewed_on <  :end_date
          AND u.community_id = :community_id`;

      case 'saves':
        return `
        SELECT COALESCE(SUM(p.saves), 0) AS total
        FROM posts p
        JOIN users u ON u.id = p.user_id
        WHERE p.created_at >= :start_date
          AND p.created_at <  :end_date
          AND u.community_id = :community_id
          AND p.was_deleted  = false`;
      default:
        throw new ApplicationError(StatusCodes.BAD_REQUEST, 'Invalid filter');
    }
  }

  public async contentEngagement(community_id: string, filter: PeriodFilter) {
    const now = new Date();
    const { from: currentStart } = Period(filter);
    const previousStart = previousPeriodStart(filter, currentStart);

    return this.statsQuery(community_id, currentStart, now, previousStart);
  }

  private async statsQuery(
    community_id: string,
    startDate: Date,
    endDate: Date,
    previousStartDate: Date,
  ) {
    const startDateISO = formatISO(startDate, { representation: 'complete' });
    const endDateISO = formatISO(endDate, { representation: 'complete' });
    const previousStartDateISO = formatISO(previousStartDate, {
      representation: 'complete',
    });

    const query = `
      WITH current_stats AS (
        SELECT
            COUNT(p.id) AS posts,
            COALESCE(SUM(p.views ), 0) AS views,
            COALESCE(SUM(p.likes ), 0) AS likes,
            COALESCE(SUM(p.shares), 0) AS shares,
            ( SELECT COUNT(*)
              FROM   post_saves ps
              JOIN   posts pp   ON pp.id = ps.post_id
              JOIN   users uu   ON uu.id = pp.user_id
              WHERE  uu.community_id = :community_id
                AND  ps.saved_on    >= :start_date
                AND  ps.saved_on    <  :end_date
            ) AS saves,

            ( SELECT COUNT(*)
              FROM   post_plays pl
              JOIN   posts pp   ON pp.id = pl.post_id
              JOIN   users uu   ON uu.id = pp.user_id
              WHERE  uu.community_id = :community_id
                AND  pl.played_on   >= :start_date
                AND  pl.played_on   <  :end_date
            ) AS plays,

            ( SELECT COUNT(*)
              FROM posts pc
              JOIN users uc ON uc.id = pc.user_id
              WHERE pc.parent_id IS NOT NULL
                AND uc.community_id = :community_id
                AND pc.created_at  >= :start_date
                AND pc.created_at  <  :end_date
                AND pc.was_deleted = false ) AS comments,

            ( SELECT COALESCE(SUM(mention_count),0)
              FROM (
                  SELECT COUNT(*) AS mention_count
                  FROM posts pm
                  CROSS JOIN LATERAL jsonb_array_elements(pm.content -> 'tags') tag
                  JOIN users ut ON ut.community_id = :community_id
                  WHERE pm.created_at  >= :start_date
                    AND pm.created_at  <  :end_date
                    AND pm.was_deleted  = false
                    AND lower(tag->>'handle') = lower(ut.username)
                  GROUP BY pm.id
              ) sub ) AS mentions,

            ( SELECT COALESCE(SUM(pl.clicks),0)
              FROM post_links pl
              JOIN posts pp  ON pp.id = pl.post_id
              JOIN users uu  ON uu.id = pp.user_id
              WHERE uu.community_id = :community_id
                AND pl.created_at   >= :start_date
                AND pl.created_at   <  :end_date ) AS clicks,

            ( SELECT COUNT(DISTINCT COALESCE(pv.viewer_id::text, pv.metadata->>'ip'))
              FROM profile_views pv
              JOIN users up ON up.id = pv.profile_id
              WHERE up.community_id = :community_id
                AND pv.viewed_on >= :start_date
                AND pv.viewed_on <  :end_date ) AS visitors
        FROM posts p
        JOIN users u ON u.id = p.user_id
        WHERE u.community_id = :community_id
          AND p.created_at   >= :start_date
          AND p.created_at   <  :end_date
          AND p.was_deleted  = false
      ),

    previous_stats AS (
    SELECT
        COUNT(p.id)  AS posts,
        COALESCE(SUM(p.views ), 0) AS views,
        COALESCE(SUM(p.likes ), 0) AS likes,
        COALESCE(SUM(p.shares), 0) AS shares,
        ( SELECT COUNT(*)
        FROM   post_saves ps
        JOIN   posts pp   ON pp.id = ps.post_id
        JOIN   users uu   ON uu.id = pp.user_id
        WHERE  uu.community_id = :community_id
          AND  ps.saved_on    >= :previous_start_date
          AND  ps.saved_on    <  :start_date
        ) AS saves,

        ( SELECT COUNT(*)
          FROM   post_plays pl
          JOIN   posts pp   ON pp.id = pl.post_id
          JOIN   users uu   ON uu.id = pp.user_id
          WHERE  uu.community_id = :community_id
            AND  pl.played_on   >= :previous_start_date
            AND  pl.played_on   <  :start_date
        ) AS plays,

        ( SELECT COUNT(*)
          FROM posts pc
          JOIN users uc ON uc.id = pc.user_id
          WHERE pc.parent_id IS NOT NULL
            AND uc.community_id = :community_id
            AND pc.created_at  >= :previous_start_date
            AND pc.created_at  <  :start_date
            AND pc.was_deleted = false ) AS comments,

        ( SELECT COALESCE(SUM(mention_count),0)
          FROM (
              SELECT COUNT(*) AS mention_count
              FROM posts pm
              CROSS JOIN LATERAL jsonb_array_elements(pm.content -> 'tags') tag
              JOIN users ut ON ut.community_id = :community_id
              WHERE pm.created_at  >= :previous_start_date
                AND pm.created_at  <  :start_date
                AND pm.was_deleted  = false
                AND lower(tag->>'handle') = lower(ut.username)
              GROUP BY pm.id
          ) sub ) AS mentions,

        ( SELECT COALESCE(SUM(pl.clicks),0)
          FROM post_links pl
          JOIN posts pp  ON pp.id = pl.post_id
          JOIN users uu  ON uu.id = pp.user_id
          WHERE uu.community_id = :community_id
            AND pl.created_at   >= :previous_start_date
            AND pl.created_at   <  :start_date ) AS clicks,

        ( SELECT COUNT(DISTINCT COALESCE(pv.viewer_id::text, pv.metadata->>'ip'))
          FROM profile_views pv
          JOIN users up ON up.id = pv.profile_id
          WHERE up.community_id = :community_id
            AND pv.viewed_on >= :previous_start_date
            AND pv.viewed_on <  :start_date ) AS visitors
        FROM posts p
        JOIN users u ON u.id = p.user_id
        WHERE u.community_id = :community_id
          AND p.created_at   >= :previous_start_date
          AND p.created_at   <  :start_date
          AND p.was_deleted  = false
      )

    SELECT
      c.posts,
      c.views,
      c.likes,
      c.shares,
      c.saves,
      c.plays,
      c.comments,
      c.mentions,
      c.clicks,
      c.visitors,

      CASE WHEN p.posts = 0 AND c.posts = 0 THEN 0
          WHEN p.posts = 0 THEN 100
          ELSE ROUND(((c.posts - p.posts)::numeric / p.posts)*100,2) END AS post_change,

      CASE WHEN p.views = 0 AND c.views = 0 THEN 0
          WHEN p.views = 0 THEN 100
          ELSE ROUND(((c.views - p.views)::numeric / p.views)*100,2) END AS view_change,

      CASE WHEN p.likes = 0 AND c.likes = 0 THEN 0
          WHEN p.likes = 0 THEN 100
          ELSE ROUND(((c.likes - p.likes)::numeric / p.likes)*100,2) END AS like_change,

      CASE WHEN p.shares = 0 AND c.shares = 0 THEN 0
          WHEN p.shares = 0 THEN 100
          ELSE ROUND(((c.shares - p.shares)::numeric / p.shares)*100,2) END AS share_change,

      CASE WHEN p.saves = 0 AND c.saves = 0 THEN 0
          WHEN p.saves = 0 THEN 100
          ELSE ROUND(((c.saves - p.saves)::numeric / p.saves)*100,2) END AS save_change,

      CASE WHEN p.comments = 0 AND c.comments = 0 THEN 0
          WHEN p.comments = 0 THEN 100
          ELSE ROUND(((c.comments - p.comments)::numeric / p.comments)*100,2) END AS comment_change,

      CASE WHEN p.mentions = 0 AND c.mentions = 0 THEN 0
          WHEN p.mentions = 0 THEN 100
          ELSE ROUND(((c.mentions - p.mentions)::numeric / p.mentions)*100,2) END AS mention_change,

      CASE WHEN p.clicks = 0 AND c.clicks = 0 THEN 0
          WHEN p.clicks = 0 THEN 100
          ELSE ROUND(((c.clicks - p.clicks)::numeric / p.clicks)*100,2) END AS click_change,

      CASE WHEN p.visitors = 0 AND c.visitors = 0 THEN 0
          WHEN p.visitors = 0 THEN 100
          ELSE ROUND(((c.visitors - p.visitors)::numeric / p.visitors)*100,2) END AS visitor_change,

      CASE WHEN p.plays = 0 AND c.plays = 0 THEN 0
          WHEN p.plays = 0 THEN 100
          ELSE ROUND(((c.plays - p.plays)::numeric / p.plays)*100,2) END AS plays_change

    FROM current_stats c, previous_stats p;
    `;

    const { rows } = await this.repo.raw(query, {
      community_id,
      start_date: startDateISO,
      end_date: endDateISO,
      previous_start_date: previousStartDateISO,
    });

    return rows[0];
  }

  public async topPosts(
    community_id: string,
    period: PeriodFilter,
  ): Promise<Post[]> {
    const time_line = Period(period);
    const start_date = time_line.from;
    const end_date = time_line.to;

    const query = `
    WITH cu AS (
      SELECT id, username
      FROM users
      WHERE community_id = :community_id
    ),

    engaged_posts AS (
      SELECT DISTINCT p.id
      FROM posts p
      WHERE p.was_deleted = false
        AND (
          EXISTS (
            SELECT 1
            FROM cu
            WHERE id = p.user_id
              AND p.created_at >= :start_date
              AND p.created_at < :end_date
          )
          OR EXISTS (
            SELECT 1
            FROM post_likes pl
            JOIN cu ON cu.id = pl.user_id
            WHERE pl.post_id = p.id
              AND pl.liked_on >= :start_date
              AND pl.liked_on < :end_date
          )
          OR EXISTS (
            SELECT 1
            FROM posts cmt
            JOIN cu ON cu.id = cmt.user_id
            WHERE cmt.parent_id = p.id
              AND cmt.created_at >= :start_date
              AND cmt.created_at < :end_date
          )
          OR EXISTS (
            SELECT 1
            FROM jsonb_array_elements(p.content -> 'tags') tag
            JOIN cu ON lower(tag->>'handle') = lower(cu.username)
            WHERE p.created_at >= :start_date
              AND p.created_at < :end_date
          )
        )
    )

    SELECT  p.*,
      COALESCE(p.views,0) AS total_views,
      u.username
    FROM posts p
    JOIN engaged_posts ep ON ep.id = p.id
    LEFT JOIN users u ON u.id = p.user_id
    ORDER BY total_views DESC
    LIMIT 5;
    `;

    const posts = await this.repo
      .raw(query, {
        community_id,
        start_date,
        end_date,
      })
      .then(({ rows }) => <Post[]>rows);

    return posts.map((post) => ({
      ...post,
      postType: typeOfPost(post),
    }));
  }

  public async fetchPosts(
    community_id: string,
    { cursor, result_per_page }: SeekPaginationOption<string>,
  ): Promise<SeekPaginationResult<Post, string>> {
    const query = `SELECT p.id,
    p.likes,
    p.views,
    p.replies,
    p.shares,
    p.plays,
    p.anonymous_views,
    ${IMPRESSIONS_CALC},
    p.content,
    p.metadata,
    p.user_id,
    p.parent_id,
    p.created_at,
    p.updated_at, 
    json_build_object(
      'id', u.id,
      'first_name', u.first_name,
      'last_name', u.last_name,
      'display_name', u.display_name,
      'username', u.username,
      'category', u.category,
      'profile_picture', u.profile_picture
    ) AS author
    FROM posts p 
    LEFT JOIN users u ON p.user_id = u.id 
    WHERE u.community_id = :community_id 
      AND p.was_deleted = false 
      ${!!cursor ? 'AND p.id < :cursor' : ''}
    ORDER BY p.created_at DESC, p.id DESC 
    LIMIT :result_per_page`;

    const posts = await this.repo
      .raw(query, {
        cursor,
        result_per_page,
        community_id,
      })
      .then(({ rows }) => <Post[]>rows);

    return new SeekPaginationResult({
      result_per_page,
      cursor: posts.at(-1)?.id ?? null,
      result: posts,
    });
  }

  public async fetchPostParent(
    community_id: string,
    post_id: string,
  ): Promise<Post> {
    const query = `SELECT p.id,
    p.likes,
    p.views,
    p.replies,
    p.shares,
    p.plays,
    p.anonymous_views,
    ${IMPRESSIONS_CALC},
    CASE
      WHEN p.was_deleted = false THEN p.content
      ELSE NULL
    END AS content,
    CASE
      WHEN p.was_deleted = false THEN p.metadata
      ELSE NULL
    END AS metadata,
    p.user_id,
    p.parent_id,
    p.was_deleted,
    p.created_at,
    p.updated_at,
    json_build_object(
      'id', u.id,
      'first_name', u.first_name,
      'last_name', u.last_name,
      'display_name', u.display_name,
      'username', u.username,
      'category', u.category,
      'profile_picture', u.profile_picture
    ) AS author
    FROM posts p0
    INNER JOIN posts p ON p0.parent_id = p.id
    LEFT JOIN users u ON p.user_id = u.id
    WHERE p0.id = :post_id 
      AND u.community_id = :community_id`;

    return this.repo
      .raw(query, { post_id, community_id })
      .then(({ rows }) => rows?.[0]);
  }

  public async fetchPostChildren(
    community_id: string,
    post_id: string,
    { cursor, result_per_page }: SeekPaginationOption<string>,
  ): Promise<SeekPaginationResult<Post, string>> {
    const query = `SELECT p.id,
    p.likes,
    p.views,
    p.replies,
    p.shares,
    p.plays,
    p.anonymous_views,
    ${IMPRESSIONS_CALC},
    CASE
      WHEN p.was_deleted = false THEN p.content
      ELSE NULL
    END AS content,
    CASE
      WHEN p.was_deleted = false THEN p.metadata
      ELSE NULL
    END AS metadata,
    p.user_id,
    p.parent_id,
    p.was_deleted,
    p.created_at,
    p.updated_at, 
    json_build_object(
      'id', u.id,
      'first_name', u.first_name,
      'last_name', u.last_name,
      'display_name', u.display_name,
      'username', u.username,
      'category', u.category,
      'profile_picture', u.profile_picture
    ) AS author
    FROM posts p 
    LEFT JOIN users u ON p.user_id = u.id
    WHERE p.parent_id = :post_id 
      AND u.community_id = :community_id
      ${!!cursor ? 'AND p.id < :cursor' : ''}
    ORDER BY p.created_at DESC, p.id DESC 
    LIMIT :result_per_page`;

    const posts = await this.repo
      .raw(query, {
        post_id,
        community_id,
        cursor,
        result_per_page,
      })
      .then(({ rows }) => <Post[]>rows);

    return new SeekPaginationResult({
      result_per_page,
      cursor: posts.at(-1)?.id ?? null,
      result: posts,
    });
  }

  public async demographicBreakdown(community_id: string) {
    const genderQuery = `
    SELECT 
      gender,
      COUNT(*) AS total
    FROM users
    WHERE community_id = :community_id
    GROUP BY gender;
  `;

    const ageQuery = `
    SELECT 
      CASE
        WHEN date_of_birth IS NULL THEN 'unknown'
        WHEN date_of_birth <= current_date - INTERVAL '65 years' THEN '65-'
        WHEN date_of_birth > current_date - INTERVAL '65 years'
          AND date_of_birth <= current_date - INTERVAL '45 years' THEN '45-64'
        WHEN date_of_birth > current_date - INTERVAL '45 years'
          AND date_of_birth <= current_date - INTERVAL '35 years' THEN '35-44'
        WHEN date_of_birth > current_date - INTERVAL '35 years'
          AND date_of_birth <= current_date - INTERVAL '25 years' THEN '25-34'
        WHEN date_of_birth > current_date - INTERVAL '25 years'
          AND date_of_birth <= current_date - INTERVAL '18 years' THEN '18-24'
        WHEN date_of_birth > current_date - INTERVAL '18 years'
          AND date_of_birth <= current_date - INTERVAL '13 years' THEN '13-17'
        ELSE 'unknown'
      END AS age_group,
      gender,
      COUNT(*) AS total
    FROM users
    WHERE community_id = :community_id
    GROUP BY age_group, gender;
  `;

    const genderData = await this.repo
      .raw(genderQuery, { community_id })
      .then(({ rows }) => rows);
    const ageData = await this.repo
      .raw(ageQuery, { community_id })
      .then(({ rows }) => rows);

    const genderBreakdown = {
      male: { total: 0, percentage: 0 },
      female: { total: 0, percentage: 0 },
      unknown: { total: 0, percentage: 0 },
    };

    let totalGenderCount = 0;
    genderData.forEach(({ gender, total }) => {
      const count = Number(total);
      if (gender === 'male' || gender === 'female') {
        genderBreakdown[gender].total = count;
        totalGenderCount += count;
      } else {
        // Any other gender falls under unknown
        genderBreakdown.unknown.total += count;
        totalGenderCount += count;
      }
    });

    if (totalGenderCount > 0) {
      genderBreakdown.male.percentage = parseFloat(
        ((genderBreakdown.male.total / totalGenderCount) * 100).toFixed(2),
      );
      genderBreakdown.female.percentage = parseFloat(
        ((genderBreakdown.female.total / totalGenderCount) * 100).toFixed(2),
      );
      genderBreakdown.unknown.percentage = parseFloat(
        ((genderBreakdown.unknown.total / totalGenderCount) * 100).toFixed(2),
      );
    }

    const ALL_AGE_GROUPS = ['65-', '45-64', '35-44', '25-34', '18-24', '13-17'];

    const ageBreakdown: Record<
      string,
      {
        male: { total: number };
        female: { total: number };
        unknown: { total: number };
      }
    > = {};

    for (const group of ALL_AGE_GROUPS) {
      ageBreakdown[group] = {
        male: { total: 0 },
        female: { total: 0 },
        unknown: { total: 0 },
      };
    }

    ageData.forEach(({ age_group, gender, total }) => {
      if (age_group !== 'unknown' && ALL_AGE_GROUPS.includes(age_group)) {
        let genderKey = 'unknown';
        if (gender === 'male' || gender === 'female') {
          genderKey = gender;
        }
        ageBreakdown[age_group][genderKey].total = Number(total);
      }
    });

    return {
      gender: genderBreakdown,
      age: ageBreakdown,
    };
  }
}
