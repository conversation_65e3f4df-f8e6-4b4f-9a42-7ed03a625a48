import { createJob } from '@app/internal/bull';
import { Container } from 'inversify';
import { Logger } from '@app/internal/logger';
import { MODULE_TOKENS, SERVICE_TOKENS } from '@app/internal/ioc/tokens';
import { UserService } from '@app/services/user/user.service';
import { ContractTerm, Revelator, SignUpPayload } from '@app/modules/revelator';
import { generateRandomPassword } from '@app/utils/bcrypt-utils';
import Deasyncify from 'deasyncify';
import { CommunityService } from '@app/services/community/community.service';
import { env } from '@app/config/env';
import { SubscriptionService } from '@app/services/subscription/subscription.service';
import { MonetizationService } from '@app/services/monetization/monetization.service';
import DeliveryTypes from '@app/misc/delivery-type.json';
import ReleaseTypes from '@app/misc/release-type.json';

export type UpsertRevelatorAccountJob = {
  user_id: string;
};

export const upsertRevelatorAccountQueue = 'upsert_revelator_account';

createJob<UpsertRevelatorAccountJob>(
  upsertRevelatorAccountQueue,
  (container: Container) => {
    const logger = container.get<Logger>(MODULE_TOKENS.Logger);
    const userService = container.get<UserService>(SERVICE_TOKENS.UserService);
    const communityService = container.get<CommunityService>(
      SERVICE_TOKENS.CommunityService,
    );
    const revelator = container.get<Revelator>(MODULE_TOKENS.Revelator);
    const subscriptionService = container.get<SubscriptionService>(
      SERVICE_TOKENS.SubscriptionService,
    );
    const monetizationService = container.get<MonetizationService>(
      SERVICE_TOKENS.MonetizationService,
    );

    return async (job) => {
      try {
        const { user_id } = job.data;

        let user = await userService.getById(user_id);

        if (!user) {
          throw new Error(`User with ID ${user_id} not found`);
        }

        const userSub = await subscriptionService.getActiveSubscription(
          user.id,
        );

        if (!userSub) {
          throw new Error(`User with ID ${user_id} has no active subscription`);
        }

        const subPlan = await monetizationService.getPlanWithPricingId(
          userSub.metadata.price_id,
        );

        const roleDesc = await userService.describe(user.id);
        const community = await communityService.get(user.community_id);

        const [, loginError] = await Deasyncify.watch(
          revelator.login(
            roleDesc?.team_owner?.revelator_id ?? user.revelator_id,
          ),
        );

        if (loginError != null) {
          if (loginError?.response?.status === 401) {
            // create new revelator account

            logger.log(`Creating new Revelator account for user ${user_id}`);

            const enterpriseName =
              user.enterprise_name || `${user.first_name} ${user.last_name}`;

            const annotation = roleDesc.community_level_user ? 'o' : 'c';

            const suffixedEmail = user.email
              .split('@')
              .join(`+${annotation}.${community.name.split(' ').join('')}@`);

            const signUpPayload: SignUpPayload = {
              email: suffixedEmail,
              partnerUserId: user.revelator_id,
              enterpriseName,
              type: 'Growth',
              password: generateRandomPassword(),
            };

            if (user.first_name && user.last_name) {
              Object.assign(signUpPayload, {
                firstname: user.first_name,
                lastname: user.last_name,
              });
            }

            const signUpResponse = await revelator.signUp(signUpPayload);

            const revClientInfo = await revelator.retrieveClientInfo(
              user.revelator_id,
              signUpResponse.enterpriseId,
            );

            // Update user with Revelator information
            user = await userService.update(user_id, {
              revelator_id: user_id,
              enterprise_name: enterpriseName,
              enterprise_id: String(signUpResponse.enterpriseId),
              payee_id: String(revClientInfo.payeeId),
            });

            logger.log(
              `Successfully created Revelator account for user ${user_id}`,
            );
          } else {
            throw loginError;
          }
        }

        if (!roleDesc.community_level_user) {
          // fetch contract summaries
          const contractSummaryList = await revelator.fetchContractSummaryList(
            env.revelator_parent_account_id,
            {
              payeeIds: [Number(user.payee_id)],
              contractLevel: 0, // 0 represent account level contracts
            },
          );

          const contractSummary = contractSummaryList.items.find((c) => {
            const payee = c.contractPayees.find(
              (p) => p.payeeId === Number(user.payee_id),
            );

            return payee != null;
          });

          if (!contractSummary) {
            logger.log(
              `No contract found for user ${user_id} with payee ID ${user.payee_id}`,
            );
            return;
          }

          const contract = await revelator.fetchContract(
            env.revelator_parent_account_id,
            contractSummary.contractId,
          );

          const communityOwnerPayee = await revelator.fetchPayee(
            env.revelator_parent_account_id,
            Number(roleDesc.community_owner.payee_id),
          );

          const distributionTerms = subPlan.metadata.distribution_terms;

          let countries: number[] = [];
          let isCountriesIncluded = false;

          if (distributionTerms.countries?.only?.length > 0) {
            const countryList = await revelator.listCountries();

            isCountriesIncluded = true;

            countries = distributionTerms.countries.only.map((c) => {
              const country = countryList.find((cl) => cl.isO2Code === c);
              return country.countryId;
            });
          } else if (distributionTerms?.countries?.exclude?.length > 0) {
            const countryList = await revelator.listCountries();

            isCountriesIncluded = false;

            countries = distributionTerms.countries.exclude.map((c) => {
              const country = countryList.find((cl) => cl.isO2Code === c);
              return country.countryId;
            });
          }

          let distributorStores: number[] = [];
          let isDistributorStoresIncluded = false;

          if (distributionTerms?.services?.only?.length > 0) {
            isDistributorStoresIncluded = true;
            distributorStores = distributionTerms.services.only;
          } else if (distributionTerms?.services?.exclude?.length > 0) {
            isDistributorStoresIncluded = false;
            distributorStores = distributionTerms.services.exclude;
          }

          let deliveryTypes: number[] = [];

          if (distributionTerms?.channels?.only?.length > 0) {
            deliveryTypes = distributionTerms.channels.only;
          } else if (distributionTerms?.channels?.exclude?.length > 0) {
            deliveryTypes = DeliveryTypes.reduce((acc, deliveryType) => {
              Object.keys(deliveryType).forEach((key) => {
                acc.push(
                  ...deliveryType[key]
                    .filter(
                      (d) =>
                        !distributionTerms.channels.exclude.includes(
                          d.deliveryTypeId,
                        ),
                    )
                    .map((d) => d.deliveryTypeId),
                );
              });

              return acc;
            }, <number[]>[]);
          }

          let releaseTypes: number[] = [];

          if (distributionTerms?.formats?.only?.length > 0) {
            releaseTypes = distributionTerms.formats.only;
          } else if (distributionTerms?.formats?.exclude?.length > 0) {
            releaseTypes = ReleaseTypes.filter(
              (r) =>
                !distributionTerms.formats.exclude.includes(r.releaseTypeId),
            ).map((r) => r.releaseTypeId);
          }

          const contractTerm = {
            contractTermsRate: 97.5, // our fix rate as per business requirement
            isCountriesIncluded,
            countries,
            isDistributorStoresIncluded,
            distributorStores,
            deliveryTypes,
            releaseTypes,
          } as ContractTerm;

          if (contract.contractTerms?.length === 0) {
            contract.contractTerms = [contractTerm];
          } else {
            contract.contractTerms[0] = {
              ...contract.contractTerms[0],
              ...contractTerm,
            };
          }

          const creatorPayeeObj = contract.contractPayees.find(
            (p) => p.payee.payeeId === Number(user.payee_id),
          );

          creatorPayeeObj.sharePercentage =
            subPlan.metadata.distribution_terms.royalty_payout_rate;

          let communityOwnerPayeeObj = contract.contractPayees.find(
            (p) =>
              p.payee.payeeId === Number(roleDesc.community_owner.payee_id),
          );

          communityOwnerPayeeObj = {
            startingBalance: 0.0,
            isCommissionPayee: false,
            permission: null,
            payee: {
              payeeId: communityOwnerPayee.payeeId,
              companyName: communityOwnerPayee.companyName,
              contactId: communityOwnerPayee.contact.contactId,
              contact: {
                contactId: communityOwnerPayee.contact.contactId,
                name: communityOwnerPayee.contact.name,
                email: communityOwnerPayee.contact.email,
              },
              currencyCode: communityOwnerPayee.currencyCode,
            },
            ...communityOwnerPayeeObj,
            sharePercentage: 100 - distributionTerms.royalty_payout_rate,
          };

          await revelator.saveContract(
            env.revelator_parent_account_id,
            {
              ...contract,
              contractPayees: [creatorPayeeObj, communityOwnerPayeeObj],
            },
          );

          logger.log(
            `Successfully updated Revelator account contract for user ${user_id}`,
          );
        }
      } catch (error) {
        logger.error(error);
        throw error;
      }
    };
  },
);
