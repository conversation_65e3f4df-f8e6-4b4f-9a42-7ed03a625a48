import { create<PERSON>ob, JobQueueManager } from '@app/internal/bull';
import { MODULE_TOKENS, SERVICE_TOKENS } from '@app/internal/ioc/tokens';
import { Logger } from '@app/internal/logger';
import { Paystack } from '@app/modules/paystack';
import { Stripe } from '@app/modules/stripe';
import {
  Currency,
  PaymentMethod,
  PaymentType,
} from '@app/services/transaction/entities/transaction.entity';
import { WalletService } from '@app/services/wallet/wallet.service';
import { Job, Processor } from 'bullmq';
import { Container } from 'inversify';
import { emailQueue, SendEmailJob } from './send-email';
import { sendNotificationJob, SendNotificationJob } from './send-notification';
import { loadTemplate, render } from '@app/utils/template.utils';
import { formatDateTime } from '@app/utils/date-utils';
import { TransactionService } from '@app/services/transaction/transaction.service';

export type InitiateTransferJob = {
  user_id: string;
  wallet_id: string;
  debit_amount: number; // minor USD
  amount: number; // minor target currency
  community_id: string;
  currency: Currency;
  payment_method: PaymentMethod;
  payment_type: PaymentType;
  reference: string;
  description: string;
  beneficiary_id: string;
  fee: number; // minor USD
  conversion_rate: number;
  external_beneficiary_id: string;
};

export const foreignCountries = ['US'];

export const stripePayoutCurrencies = ['USD', 'EUR', 'GBP'];

export const initiateTransferJob = 'initiate_transfer_job';

createJob<InitiateTransferJob>(
  initiateTransferJob,
  (container: Container): Processor<InitiateTransferJob> => {
    const logger = container.get<Logger>(MODULE_TOKENS.Logger);

    const wallet = container.get<WalletService>(SERVICE_TOKENS.WalletService);

    const stripe = container.get<Stripe>(MODULE_TOKENS.Stripe);

    const paystack = container.get<Paystack>(MODULE_TOKENS.Paystack);

    const jobQueueManager = container.get<JobQueueManager>(
      MODULE_TOKENS.JobQueueManager,
    );

    const EmailQueue = jobQueueManager.getQueue<SendEmailJob>(emailQueue);

    const NotificationQueue =
      jobQueueManager.getQueue<SendNotificationJob>(sendNotificationJob);

    return async (job: Job<InitiateTransferJob>) => {
      const {
        amount,
        beneficiary_id,
        community_id,
        currency,
        description,
        reference,
        wallet_id,
        external_beneficiary_id,
        user_id,
        debit_amount,
      } = job.data;

      async function safeRollbackCredit(
        wallet: WalletService,
        logger: Logger,
        wallet_id: string,
        community_id: string,
        debit_amount: number, // minor USD
        original_reference: string,
      ) {
        const transaction = container.get<TransactionService>(
          SERVICE_TOKENS.TransactionService,
        );

        const transactionExist = await transaction.getByRef(original_reference);
        if (transactionExist) return;

        await wallet.credit({
          id: wallet_id,
          amount: debit_amount / 100,
          currency: Currency.USD,
          community_id,
          payment_method: PaymentMethod.INTERNAL,
          payment_type: PaymentType.REVERSAL,
          reference: crypto.randomUUID(),
          description: 'Withdrawal reversal',
        });

        logger.log(
          `Rollback credit applied for wallet ${wallet_id} (orig ref ${original_reference}) for job ${job.id}`,
        );
      }

      try {
        if (stripePayoutCurrencies.includes(currency)) {
          await stripe.createPayout({
            amount: debit_amount,
            currency,
            description,
            destination: external_beneficiary_id,
            metadata: { user_id, beneficiary_id, reference },
          });

          return;
        } else {
          const balances = await paystack.getBalances();
          const ledger = balances.data.find(
            (b) => b.currency === currency.toUpperCase(),
          );
          const available = ledger?.balance ?? 0;

          if (available < amount) {
            const template = loadTemplate('admin/low-float');
            const htmlData = {
              providerName: 'Paystack',
              currentBalance: `${available / 100} ${currency}`,
              amount: `${amount} ${currency}`,
              timeStamp: formatDateTime(new Date()),
              year: new Date().getFullYear().toString(),
            };

            const html = render(template, htmlData);

            await EmailQueue.add(
              'email',
              {
                from: 'Makerverse',
                to: '<EMAIL>',
                subject: 'Insufficient funds in paystack',
                body: html,
              },
              { removeOnComplete: true, removeOnFail: true },
            );

            await wallet.credit({
              id: wallet_id,
              amount: debit_amount / 100,
              currency: Currency.USD,
              community_id,
              payment_method: PaymentMethod.INTERNAL,
              payment_type: PaymentType.REVERSAL,
              reference: crypto.randomUUID(),
              description: 'Withdrawal reversal',
            });

            await NotificationQueue.add(
              'notification',
              {
                user_id,
                message: `Your recent withdrawal of ${
                  amount / 100
                } ${currency} has been reversed. You can try again in a few hours, we apologize for the inconvenience.`,
              },
              { removeOnComplete: true, removeOnFail: true },
            );

            logger.log(
              `Withdrawal reversal for user ${user_id} to beneficiary ${beneficiary_id} from job ${job.id} due to insufficient funds in Paystack`,
            );

            return;
          } else {
            logger.log(
              `Initiating paystack transfer for user ${user_id} to beneficiary ${beneficiary_id} from job ${job.id} with amount ${amount} ${currency}`,
            );
            await paystack.initiateTransfer({
              amount,
              currency,
              reason: description,
              recipient: external_beneficiary_id,
              reference,
              source: 'balance',
            });
          }
          return;
        }
      } catch (err) {
        logger.log(
          `Failed to debit wallet for user ${user_id} for withdrawal on reference ${reference} from job ${job.id} due to error: ${err.message}`,
        );

        // This handles for paystack for now
        if (
          err.response.data.body.status === false &&
          err.response.data.body.type === 'api_error'
        ) {
          await safeRollbackCredit(
            wallet,
            logger,
            wallet_id,
            community_id,
            debit_amount,
            reference,
          );

          await NotificationQueue.add(
            'notification',
            {
              user_id,
              message: `Your recent withdrawal of ${
                amount / 100
              } ${currency} has been reversed. You can try again in a few hours, we apologize for the inconvenience.`,
            },
            { removeOnComplete: true, removeOnFail: true },
          );
        }

        throw err;
      }
    };
  },
);
