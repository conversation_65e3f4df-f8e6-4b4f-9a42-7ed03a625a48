#!/usr/bin/env node

/**
 * Advanced script to update users' Revelator account information
 * 
 * This script:
 * 1. Connects to the database
 * 2. Fetches users with revelator_id but missing enterprise_id or payee_id
 * 3. Uses the Revelator module to login and fetch enterprise info
 * 4. Updates the users' enterprise_id and payee_id in the database
 * 
 * Usage: node update-revelator-info-advanced.js [--dry-run] [--batch-size=50]
 */

require('module-alias/register');
require('reflect-metadata');

const knex = require('knex');
const dotenv = require('dotenv');
const path = require('path');
const fs = require('fs');
const { Container } = require('inversify');
const { MODULE_TOKENS } = require('@app/internal/ioc/tokens');
const { RevelatorClient } = require('@app/modules/revelator/client');
const { Revelator } = require('@app/modules/revelator');

// Parse command line arguments
const args = process.argv.slice(2).reduce((acc, arg) => {
  if (arg.startsWith('--')) {
    const [key, value] = arg.slice(2).split('=');
    acc[key] = value === undefined ? true : value;
  }
  return acc;
}, {});

// Configuration
const config = {
  dryRun: args['dry-run'] === true,
  batchSize: parseInt(args['batch-size'] || '50', 10),
  logFile: args['log-file'] || `revelator-update-${new Date().toISOString().split('T')[0]}.log`,
};

// Load environment variables
dotenv.config({ path: path.resolve(process.cwd(), '.env') });

// Create a logger that writes to both console and file
const logger = {
  log: (message) => {
    const timestamp = new Date().toISOString();
    const formattedMessage = typeof message === 'object' 
      ? `${timestamp} - ${JSON.stringify(message)}`
      : `${timestamp} - ${message}`;
    
    console.log(formattedMessage);
    fs.appendFileSync(config.logFile, formattedMessage + '\n');
  },
  error: (message, error) => {
    const timestamp = new Date().toISOString();
    let formattedMessage = typeof message === 'object' 
      ? `${timestamp} - ERROR: ${JSON.stringify(message)}`
      : `${timestamp} - ERROR: ${message}`;
    
    if (error) {
      formattedMessage += `\n${timestamp} - ${error.stack || error}`;
    }
    
    console.error(formattedMessage);
    fs.appendFileSync(config.logFile, formattedMessage + '\n');
  }
};

// Create a knex instance for database connection
const db = knex({
  client: 'pg',
  connection: {
    connectionString: process.env.db_url,
    ssl: process.env.db_ssl === 'true' ? { rejectUnauthorized: false } : false,
  },
  debug: process.env.knex_debug === 'true',
});

// Set up the Revelator client
const container = new Container();
container.bind(MODULE_TOKENS.Revelator).to(RevelatorClient).inSingletonScope();
const revelator = container.get(MODULE_TOKENS.Revelator);

// Function to update user in the database
async function updateUser(userId, data) {
  try {
    if (config.dryRun) {
      logger.log(`[DRY RUN] Would update user ${userId} with data: ${JSON.stringify(data)}`);
      return { id: userId, ...data };
    }
    
    const result = await db('users')
      .where({ id: userId })
      .update({
        enterprise_id: data.enterprise_id,
        payee_id: data.payee_id,
        enterprise_name: data.enterprise_name,
        updated_at: db.fn.now()
      })
      .returning(['id', 'email', 'revelator_id', 'enterprise_id', 'payee_id', 'enterprise_name']);
    
    return result[0];
  } catch (error) {
    logger.error(`Error updating user ${userId}:`, error);
    return null;
  }
}

// Function to get users with missing Revelator information
async function getUsersWithMissingInfo(offset = 0, limit = config.batchSize) {
  return await db('users')
    .select('id', 'email', 'first_name', 'last_name', 'revelator_id', 'enterprise_name', 'enterprise_id', 'payee_id')
    .whereNotNull('revelator_id')
    .where(function() {
      this.whereNull('enterprise_id')
        .orWhereNull('payee_id');
    })
    .orderBy('id')
    .limit(limit)
    .offset(offset);
}

// Function to get total count of users with missing info
async function getTotalUsersWithMissingInfo() {
  const result = await db('users')
    .count('* as count')
    .whereNotNull('revelator_id')
    .where(function() {
      this.whereNull('enterprise_id')
        .orWhereNull('payee_id');
    })
    .first();
  
  return parseInt(result.count, 10);
}

// Retry function for API calls
async function retryOperation(operation, retryCount = 3, delay = 1000) {
  let lastError;
  
  for (let attempt = 1; attempt <= retryCount; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      logger.error(`Attempt ${attempt}/${retryCount} failed`, error);
      
      if (attempt < retryCount) {
        const waitTime = delay * Math.pow(2, attempt - 1); // Exponential backoff
        logger.log(`Waiting ${waitTime}ms before retry...`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }
  }
  
  throw lastError;
}

// Main function
async function main() {
  logger.log('Starting update process for users with missing Revelator information...');
  logger.log(`Configuration: ${JSON.stringify(config)}`);
  
  if (config.dryRun) {
    logger.log('DRY RUN MODE: No actual database updates will be performed');
  }
  
  try {
    // Get total count of users with missing info
    const totalUsers = await getTotalUsersWithMissingInfo();
    logger.log(`Found ${totalUsers} users with missing Revelator information`);
    
    let processedCount = 0;
    let successCount = 0;
    let errorCount = 0;
    
    // Process users in batches
    for (let offset = 0; offset < totalUsers; offset += config.batchSize) {
      const users = await getUsersWithMissingInfo(offset, config.batchSize);
      logger.log(`Processing batch: ${offset + 1} to ${Math.min(offset + config.batchSize, totalUsers)} of ${totalUsers}`);
      
      // Process each user in the batch
      for (const user of users) {
        processedCount++;
        logger.log(`\nProcessing user ${processedCount}/${totalUsers}: ${user.id} (${user.email})`);
        
        // Log what information is missing
        const missingFields = [];
        if (!user.enterprise_id) missingFields.push('enterprise_id');
        if (!user.payee_id) missingFields.push('payee_id');
        logger.log(`Missing fields: ${missingFields.join(', ')}`);
        
        try {
          // Try to log in to Revelator
          logger.log(`Logging in to Revelator for user ${user.id} with revelator_id: ${user.revelator_id}`);
          const loginResponse = await retryOperation(() => revelator.login(user.revelator_id));
          
          if (!loginResponse) {
            logger.log(`Could not log in to Revelator for user ${user.id}`);
            errorCount++;
            continue;
          }
          
          // Get the enterprise ID from the login response
          const enterpriseId = loginResponse.permissions[0]?.enterpriseId;
          const email = loginResponse.permissions[0]?.email;
          
          if (!enterpriseId) {
            logger.log(`No enterprise ID found for user ${user.id}`);
            errorCount++;
            continue;
          }
          
          // Generate enterprise name if not available
          const enterpriseName = user.enterprise_name || loginResponse.permissions[0]?.enterpriseName || `${user.first_name} ${user.last_name}`;
          
          // Retrieve client info
          logger.log(`Retrieving client info for user ${user.id} with enterpriseId: ${enterpriseId}`);
          const clientInfo = await retryOperation(() => 
            revelator.retrieveClientInfo(user.revelator_id, email, enterpriseName)
          );
          
          if (!clientInfo) {
            logger.log(`Could not retrieve client info for user ${user.id}`);
            errorCount++;
            continue;
          }
          
          logger.log(`Retrieved client info for user ${user.id}:`, {
            enterpriseId,
            payeeId: clientInfo.payeeId,
            enterpriseName
          });
          
          // Update user in the database
          const updateData = {
            enterprise_id: String(enterpriseId),
            payee_id: String(clientInfo.payeeId),
            enterprise_name: enterpriseName
          };
          
          logger.log(`Updating user ${user.id} with new data`);
          const updatedUser = await updateUser(user.id, updateData);
          
          if (updatedUser) {
            logger.log(`Successfully updated user ${user.id}`);
            successCount++;
          } else {
            logger.error(`Failed to update user ${user.id}`);
            errorCount++;
          }
        } catch (error) {
          logger.error(`Error processing user ${user.id}:`, error);
          errorCount++;
        }
        
        // Add a small delay between users to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 500));
      }
      
      // Log progress
      const progressPercent = ((offset + users.length) / totalUsers * 100).toFixed(2);
      logger.log(`\nProgress: ${progressPercent}% (${offset + users.length}/${totalUsers})`);
      logger.log(`Current stats - Success: ${successCount}, Errors: ${errorCount}`);
    }
    
    logger.log('\n--- Final Summary ---');
    logger.log(`Total users with missing info: ${totalUsers}`);
    logger.log(`Successfully updated: ${successCount}`);
    logger.log(`Errors: ${errorCount}`);
    
  } catch (error) {
    logger.error('Error in main process:', error);
  } finally {
    // Close the database connection
    await db.destroy();
    logger.log('Database connection closed');
  }
}

// Run the main function
main().catch(error => {
  logger.error('Unhandled error in main process:', error);
  process.exit(1);
});
