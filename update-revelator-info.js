#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to update users' Revelator account information
 *
 * This script:
 * 1. Connects to the database
 * 2. Fetches users with revelator_id but missing enterprise_id or payee_id
 * 3. Uses the Revelator client to login and fetch enterprise info
 * 4. Updates the users' enterprise_id and payee_id in the database
 *
 * Usage: node update-revelator-info.js [--dry-run]
 */

require('module-alias/register');
require('reflect-metadata');

const knex = require('knex');
const dotenv = require('dotenv');
const path = require('path');
const axios = require('axios');

// Parse command line arguments
const args = process.argv.slice(2).reduce((acc, arg) => {
  if (arg.startsWith('--')) {
    const [key, value] = arg.slice(2).split('=');
    acc[key] = value === undefined ? true : value;
  }
  return acc;
}, {});

// Configuration
const config = {
  dryRun: args['dry-run'] === true,
  batchSize: parseInt(args['batch-size'] || '50', 10),
};

// Load environment variables
dotenv.config({ path: path.resolve(process.cwd(), '.env') });

// Create a knex instance for database connection
const db = knex({
  client: 'pg',
  connection: {
    connectionString: '*************************************************',
    ssl: false,
  },
  debug: true,
});

// Create a revelator API client
const revelatorClient = axios.create({
  baseURL: 'https://api.revelator.com',
  headers: {
    Accept: 'application/json',
    'Content-Type': 'application/json',
  },
  timeout: 30000, // 30 seconds timeout
});

// Function to log into Revelator
async function loginToRevelator(partnerUserId) {
  try {
    const response = await revelatorClient.post('/partner/account/login', {
      partnerApiKey: 'a8b11b64-de72-4d23-84ff-e3c397372aec',
      partnerUserId,
    });
    return response.data;
  } catch (error) {
    console.error(
      `Error logging into Revelator for user ${partnerUserId}:`,
      error.message,
    );
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error(
        'Response data:',
        JSON.stringify(error.response.data, null, 2),
      );
    }
    return null;
  }
}

// Function to retrieve client info from Revelator
async function retrieveClientInfo(accessToken, enterpriseId) {
  try {
    const response = await revelatorClient.get(
      `/enterprise/clients/${enterpriseId}`,
      {
        headers: { Authorization: `Bearer ${accessToken}` },
      },
    );
    return response.data;
  } catch (error) {
    console.error(
      `Error retrieving client info for enterprise ${enterpriseId}:`,
      error.message,
    );
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error(
        'Response data:',
        JSON.stringify(error.response.data, null, 2),
      );
    }
    return null;
  }
}

// Function to update user in the database
async function updateUser(userId, data) {
  try {
    if (config.dryRun) {
      console.log(`[DRY RUN] Would update user ${userId} with data:`, data);
      return { id: userId, ...data };
    }

    const result = await db('users')
      .where({ id: userId })
      .update({
        enterprise_id: data.enterprise_id,
        payee_id: data.payee_id,
        enterprise_name: data.enterprise_name,
      })
      .returning([
        'id',
        'email',
        'revelator_id',
        'enterprise_id',
        'payee_id',
        'enterprise_name',
      ]);

    return result[0];
  } catch (error) {
    console.error(`Error updating user ${userId}:`, error.message);
    return null;
  }
}

// Function to get users with missing Revelator information
async function getUsersWithMissingInfo(offset = 0, limit = config.batchSize) {
  return await db('users')
    .select(
      'id',
      'email',
      'first_name',
      'last_name',
      'revelator_id',
      'enterprise_name',
      'enterprise_id',
      'payee_id',
    )
    .whereNotNull('revelator_id')
    .where(function () {
      this.whereNull('enterprise_id').orWhereNull('payee_id');
    })
    .orderBy('id')
    .limit(limit)
    .offset(offset);
}

// Function to get total count of users with missing info
async function getTotalUsersWithMissingInfo() {
  const result = await db('users')
    .count('* as count')
    .whereNotNull('revelator_id')
    .where(function () {
      this.whereNull('enterprise_id').orWhereNull('payee_id');
    })
    .where({ category: 'community_owner' })
    .first();

  return parseInt(result.count, 10);
}

// Retry function for API calls
async function retryOperation(operation, retryCount = 3, delay = 1000) {
  let lastError;

  for (let attempt = 1; attempt <= retryCount; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      console.error(`Attempt ${attempt}/${retryCount} failed:`, error.message);

      if (attempt < retryCount) {
        const waitTime = delay * Math.pow(2, attempt - 1); // Exponential backoff
        console.log(`Waiting ${waitTime}ms before retry...`);
        await new Promise((resolve) => setTimeout(resolve, waitTime));
      }
    }
  }

  throw lastError;
}

// Main function
async function main() {
  console.log(
    'Starting update process for users with missing Revelator information...',
  );

  if (config.dryRun) {
    console.log('DRY RUN MODE: No actual database updates will be performed');
  }

  try {
    // Get total count of users with missing info
    const totalUsers = await getTotalUsersWithMissingInfo();
    console.log(`Found ${totalUsers} users with missing Revelator information`);

    let processedCount = 0;
    let successCount = 0;
    let errorCount = 0;

    // Process users in batches
    for (let offset = 0; offset < totalUsers; offset += config.batchSize) {
      const users = await getUsersWithMissingInfo(offset, config.batchSize);
      console.log(
        `Processing batch: ${offset + 1} to ${Math.min(
          offset + config.batchSize,
          totalUsers,
        )} of ${totalUsers}`,
      );

      // Process each user in the batch
      for (const user of users) {
        processedCount++;
        console.log(
          `\nProcessing user ${processedCount}/${totalUsers}: ${user.id} (${user.email})`,
        );

        // Log what information is missing
        const missingFields = [];
        if (!user.enterprise_id) missingFields.push('enterprise_id');
        if (!user.payee_id) missingFields.push('payee_id');
        console.log(`Missing fields: ${missingFields.join(', ')}`);

        try {
          // Try to log in to Revelator
          console.log(
            `Logging in to Revelator for user ${user.id} with revelator_id: ${user.revelator_id}`,
          );
          const loginResponse = await retryOperation(() =>
            loginToRevelator(user.revelator_id),
          );

          if (!loginResponse) {
            console.log(`Could not log in to Revelator for user ${user.id}`);
            errorCount++;
            continue;
          }

          // Get the enterprise ID from the login response
          const enterpriseId = loginResponse.permissions[0]?.enterpriseId;

          if (!enterpriseId) {
            console.log(`No enterprise ID found for user ${user.id}`);
            errorCount++;
            continue;
          }

          // Generate enterprise name if not available
          const enterpriseName =
            user.enterprise_name ||
            loginResponse.permissions[0]?.enterpriseName;

          // Retrieve client info
          console.log(
            `Retrieving client info for user ${user.id} with enterpriseId: ${enterpriseId}`,
          );
          const clientInfo = await retryOperation(() =>
            retrieveClientInfo(loginResponse.accessToken, enterpriseId),
          );

          if (!clientInfo) {
            console.log(`Could not retrieve client info for user ${user.id}`);
            errorCount++;
            continue;
          }

          console.log(`Retrieved client info for user ${user.id}:`, {
            enterpriseId,
            payeeId: clientInfo.payeeId,
            enterpriseName: clientInfo.enterpriseName || enterpriseName,
          });

          // Update user in the database
          const updateData = {
            enterprise_id: String(enterpriseId),
            payee_id: String(clientInfo.payeeId),
            enterprise_name: clientInfo.enterpriseName || enterpriseName,
          };

          console.log(`Updating user ${user.id} with new data:`, updateData);
          const updatedUser = await updateUser(user.id, updateData);

          if (updatedUser) {
            console.log(`Successfully updated user ${user.id}`);
            successCount++;
          } else {
            console.log(`Failed to update user ${user.id}`);
            errorCount++;
          }
        } catch (error) {
          console.error(`Error processing user ${user.id}:`, error);
          errorCount++;
        }

        // Add a small delay between users to avoid rate limiting
        await new Promise((resolve) => setTimeout(resolve, 500));
      }

      // Log progress
      const progressPercent = (
        ((offset + users.length) / totalUsers) *
        100
      ).toFixed(2);
      console.log(
        `\nProgress: ${progressPercent}% (${
          offset + users.length
        }/${totalUsers})`,
      );
      console.log(
        `Current stats - Success: ${successCount}, Errors: ${errorCount}`,
      );
    }

    console.log('\n--- Summary ---');
    console.log(`Total users with missing info: ${totalUsers}`);
    console.log(`Successfully updated: ${successCount}`);
    console.log(`Errors: ${errorCount}`);
  } catch (error) {
    console.error('Error in main process:', error);
  } finally {
    // Close the database connection
    await db.destroy();
    console.log('Database connection closed');
  }
}

// Run the main function
main().catch(console.error);
