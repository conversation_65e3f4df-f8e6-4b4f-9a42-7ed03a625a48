const knex = require('knex');
const { ulid } = require('ulid');
const solUsers = require('./affected2.json');
const fs = require('fs');
const axios = require('axios');

// Create a revelator API client
const revClient = axios.create({
  baseURL: 'https://api.revelator.com',
  headers: {
    Accept: 'application/json',
    'Content-Type': 'application/json',
  },
  timeout: 30000, // 30 seconds timeout
});

// Function to log into Revelator
async function loginToRevelator(partnerUserId) {
  try {
    const response = await revelatorClient.post('/partner/account/login', {
      partnerApiKey: 'a8b11b64-de72-4d23-84ff-e3c397372aec',
      partnerUserId,
    });
    return response.data;
  } catch (error) {
    console.error(
      `Error logging into Revelator for user ${partnerUserId}:`,
      error.message,
    );
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error(
        'Response data:',
        JSON.stringify(error.response.data, null, 2),
      );
    }
    return null;
  }
}

const data = {
  createdBy: '825721a9-9bb9-4bd5-a2e1-b2942bb2b134',
  createdByEmail: '<EMAIL>',
  createdByUserId: '825721a9-9bb9-4bd5-a2e1-b2942bb2b134',
  enterpriseEmail: '<EMAIL>',
  enterpriseOwnerId: '825721a9-9bb9-4bd5-a2e1-b2942bb2b134',
  lastPayeePaymentDate: '2025-05-23',
  lastPayeePaymentAmount: 463.3603,
  isEnterpriseVip: false,
  isEnterpriseWhiteListed: true,
  payeeReferrerName: null,
  description: null,
  descriptionTitle: null,
  tags: null,
  languageId: 1,
  copyrightC: '2024 Nasty C',
  copyrightP: '2024 Nasty C',
  recordingLocation: null,
  recordingDate: null,
  parentalAdvisory: null,
  producedBy: null,
  mixedBy: null,
  masteredBy: null,
  masteredDate: null,
  productionCredits: null,
  totalSales: 0.0,
  enterpriseId: 593579,
  payeeOwner: 649856,
  hasRecordLabel: false,
  previouslyReleased: false,
  releasesLocals: [],
  contributors: [
    {
      contributorId: '5248db05-d182-471a-b6a7-52eba4d59600',
      roleId: 5,
      isPrimary: false,
      trackId: null,
      releaseId: 2581751,
      artist: {
        labelId: null,
        labelName: '',
        contactId: null,
        contact: null,
        biography: null,
        yearsActive: [],
        influencers: [],
        contemporaries: [],
        tags: [],
        isSigned: false,
        musicStyles: [],
        artistExternalIds: [
          {
            profileId: '**********',
            distributorStoreId: 1,
          },
          {
            profileId: '7M5xa3W8nnFeBaOvJVHRxj',
            distributorStoreId: 9,
          },
        ],
        socialUrls: [],
        artistLocals: [],
        artistsWebsites: [],
        webAlias: null,
        enterpriseId: 593579,
        artistId: 1427771,
        name: 'Audiomarc',
        image: {
          fileId: 'cb12cded-387e-42c0-8d77-7fee9a9ecd57',
          isTemp: false,
          filename:
            'ab67616100005174f05f920b52de3fccf0512ddc_2025-04-21T083516.948Z.jpg',
          externalUrl: null,
          lastUpdateDate: '2025-04-21T08:35:21.77',
        },
      },
    },
    {
      contributorId: '91ccee05-d818-423c-a2af-c9a5bc3906b4',
      roleId: 34,
      isPrimary: false,
      trackId: null,
      releaseId: 2581751,
      artist: {
        labelId: null,
        labelName: '',
        contactId: null,
        contact: null,
        biography: null,
        yearsActive: [],
        influencers: [],
        contemporaries: [],
        tags: [],
        isSigned: false,
        musicStyles: [],
        artistExternalIds: [
          {
            profileId: null,
            distributorStoreId: 1,
          },
          {
            profileId: null,
            distributorStoreId: 9,
          },
        ],
        socialUrls: [],
        artistLocals: [],
        artistsWebsites: [],
        webAlias: null,
        enterpriseId: 593579,
        artistId: 1440188,
        name: 'Waytoolost',
        image: null,
      },
    },
  ],
  artistLocals: [],
  tracks: [
    {
      enterpriseName: 'Nsikayesizwe Ngcobo',
      enterpriseId: 593579,
      trackId: 5913529,
      name: 'For Certain',
      spotifyId: null,
      artistId: 1415475,
      artistName: 'Nasty C',
      artistAppleId: '1041347781',
      artistSpotifyId: '2gzWmhOZhDN6gXL49JW9qj',
      artistExternalIds: [
        {
          profileId: '1041347781',
          distributorStoreId: 1,
        },
        {
          profileId: '2gzWmhOZhDN6gXL49JW9qj',
          distributorStoreId: 9,
        },
      ],
      labelId: 888108,
      labelName: 'Nasty C',
      discNumber: null,
      trackLength: 112,
      channels: 2,
      sampleRate: 48000,
      bitDepth: 24,
      bitrate: 2307171,
      trackVendorId: null,
      isrc: 'QZN882429074',
      version: null,
      copyrightC: null,
      copyrightP: '2024 Nasty_C',
      description: null,
      languageId: 1,
      explicit: true,
      lyrics: null,
      playingCount: 0,
      appleId: null,
      priceTierId: null,
      rdioId: null,
      previewStartSeconds: 0,
      totalSales: 0.0,
      image: null,
      wav: {
        fileId: '94c54bc8-9fd1-4139-9326-66289f12ea60',
        isTemp: false,
        filename: 'For Certain (MaxMaster) v4_2024-12-12T141743.549Z.wav',
        externalUrl: null,
        lastUpdateDate: '0001-01-01T00:00:00',
      },
      flac: null,
      trackRecordingVersions: [
        {
          isrc: 'QZN882429074',
          recordingVersionType: 1,
          audioFiles: [
            {
              audioId: '94c54bc8-9fd1-4139-9326-66289f12ea60',
              fileFormat: 1,
              audioFilename:
                'For Certain (MaxMaster) v4_2024-12-12T141743.549Z.wav',
              audioChannels: 2,
              audioBitDepth: 24,
              audioSampleRate: 48000,
              audioBitrate: 2307171,
              audioSeconds: 112,
              audioSize: 32099786,
            },
          ],
        },
      ],
      fileExtension: '.wav',
      isLockedForDistribution: true,
      isDolbyAtmosReadOnly: false,
      primaryMusicStyleId: 41,
      secondaryMusicStyleId: null,
      previouslyReleased: false,
      trackType: 1,
      licenseRequestStatus: null,
      isAudioValid: null,
      tracksLocals: [],
      artistLocals: [],
      contributors: [
        {
          contributorId: '11c0485c-571c-43ad-b3f2-178f74097b67',
          roleId: 5,
          isPrimary: false,
          trackId: 5913529,
          releaseId: null,
          artist: {
            labelId: null,
            labelName: '',
            contactId: null,
            contact: null,
            biography: null,
            yearsActive: [],
            influencers: [],
            contemporaries: [],
            tags: [],
            isSigned: false,
            musicStyles: [],
            artistExternalIds: [
              {
                profileId: '**********',
                distributorStoreId: 1,
              },
              {
                profileId: '7M5xa3W8nnFeBaOvJVHRxj',
                distributorStoreId: 9,
              },
            ],
            socialUrls: [],
            artistLocals: [],
            artistsWebsites: [],
            webAlias: null,
            enterpriseId: 593579,
            artistId: 1427771,
            name: 'Audiomarc',
            image: {
              fileId: 'cb12cded-387e-42c0-8d77-7fee9a9ecd57',
              isTemp: false,
              filename:
                'ab67616100005174f05f920b52de3fccf0512ddc_2025-04-21T083516.948Z.jpg',
              externalUrl: null,
              lastUpdateDate: '2025-04-21T08:35:21.77',
            },
          },
        },
      ],
      catalog: null,
      compositions: [],
      releaseIds: [2581751],
      releases: [
        {
          releaseId: 2581751,
          name: 'For Certain',
          version: null,
          releaseDate: '2024-12-31T00:00:00',
          releaseTypeId: 2,
          upc: 7316479360709.0,
          isLockedForDistribution: true,
          isDolbyAtmosReadOnly: false,
          isIngested: false,
          enterpriseName: 'Nsikayesizwe Ngcobo',
          enterpriseId: 593579,
          assetId: 10879492,
          image: {
            fileId: '6b9a48a2-6fff-44de-8080-cc6562eb9617',
            isTemp: false,
            filename: 'cover_art_2024-12-12T143157.449Z.jpg',
            externalUrl: null,
            lastUpdateDate: '2025-01-14T14:06:33.53',
          },
          artist: {
            artistId: 1415475,
            name: 'Nasty C',
            image: null,
          },
          contributors: [
            {
              contributorId: '5248db05-d182-471a-b6a7-52eba4d59600',
              roleId: 5,
              releaseId: 2581751,
              trackId: null,
              artist: {
                artistId: 1427771,
                name: 'Audiomarc',
                image: {
                  fileId: 'cb12cded-387e-42c0-8d77-7fee9a9ecd57',
                  isTemp: false,
                  filename:
                    'ab67616100005174f05f920b52de3fccf0512ddc_2025-04-21T083516.948Z.jpg',
                  externalUrl: null,
                  lastUpdateDate: '2025-04-21T08:35:21.77',
                },
              },
            },
            {
              contributorId: '91ccee05-d818-423c-a2af-c9a5bc3906b4',
              roleId: 34,
              releaseId: 2581751,
              trackId: null,
              artist: {
                artistId: 1440188,
                name: 'Waytoolost',
                image: null,
              },
            },
          ],
        },
      ],
      composerContentsDTO: [
        {
          contributorId: 5111149,
          composerId: 1067213,
          composerName: 'Nsikayesizwe David Junior Ngcobo',
          share: 100.0,
          publisherId: null,
          publisherName: '',
          publisherAdminId: null,
          publisherAdminName: null,
          proId: null,
          rightsId: 1,
          proRegistrationId: null,
          roleId: 1,
          composersLocals: [],
        },
      ],
      isFullyLocked: false,
      copyTrackId: null,
      isIngested: false,
      isLicensePaid: null,
      distributorStoreGenreIds: [],
      assetId: 10879493,
      monetizations: [
        {
          trackId: 5913529,
          distributorStoreId: 307,
          policyId: 1410,
          isEligible: null,
          optIn: null,
          isPaid: null,
          isLive: true,
          transactionId: null,
          transactionDate: null,
        },
        {
          trackId: 5913529,
          distributorStoreId: 310,
          policyId: 1412,
          isEligible: null,
          optIn: null,
          isPaid: null,
          isLive: null,
          transactionId: null,
          transactionDate: null,
        },
        {
          trackId: 5913529,
          distributorStoreId: 319,
          policyId: 2574,
          isEligible: null,
          optIn: null,
          isPaid: null,
          isLive: null,
          transactionId: null,
          transactionDate: null,
        },
      ],
      acrCloud: {
        timestampUtc: '2024-12-12T14:34:03.3979412Z',
        scans: [
          {
            scanStartTimeSeconds: 2,
            matches: null,
            error: 'No result',
          },
          {
            scanStartTimeSeconds: 28,
            matches: null,
            error: 'No result',
          },
          {
            scanStartTimeSeconds: 56,
            matches: null,
            error: 'No result',
          },
          {
            scanStartTimeSeconds: 89,
            matches: null,
            error: 'No result',
          },
        ],
      },
      royaltyToken: null,
      trackProperties: [1],
    },
  ],
  enterpriseImageId: '00000000-0000-0000-0000-000000000000',
  featureFmSmartLink: null,
  distributorStoreGenreIds: [],
  upc: 7316479360709.0,
  isrc: null,
  trackISRC: 'QZN882429074',
  trackRecordingVersions: [
    {
      isrc: 'QZN882429074',
      recordingVersionType: 1,
      audioFiles: [
        {
          audioId: '94c54bc8-9fd1-4139-9326-66289f12ea60',
          audioFilename:
            'For Certain (MaxMaster) v4_2024-12-12T141743.549Z.wav',
          fileFormat: 1,
        },
      ],
    },
  ],
  artistId: 1415475,
  artistName: 'Nasty C',
  artistAppleId: '1041347781',
  artistSpotifyId: '2gzWmhOZhDN6gXL49JW9qj',
  artistExternalIds: [
    {
      profileId: '1041347781',
      distributorStoreId: 1,
    },
    {
      profileId: '2gzWmhOZhDN6gXL49JW9qj',
      distributorStoreId: 9,
    },
  ],
  catalog: null,
  version: null,
  artistImageId: '',
  labelId: 888108,
  labelName: 'Nasty C',
  releaseDate: '2024-12-31T00:00:00',
  creationDate: '2024-12-12T14:31:57',
  primaryMusicStyleId: 41,
  secondaryMusicStyleId: 167,
  notesCount: 2,
  payeeNotesCount: 0,
  approvedDate: null,
  kountStatusId: null,
  kountStatusName: null,
  isFullyLocked: false,
  releaseId: 2581751,
  name: 'For Certain',
  releaseTypeId: 2,
  isLockedForDistribution: true,
  isDolbyAtmosReadOnly: false,
  isIngested: false,
  enterpriseName: 'Nsikayesizwe Ngcobo',
  assetId: 10879492,
  image: {
    fileId: '6b9a48a2-6fff-44de-8080-cc6562eb9617',
    isTemp: false,
    filename: 'cover_art_2024-12-12T143157.449Z.jpg',
    externalUrl: null,
    lastUpdateDate: '2025-01-14T14:06:33.53',
  },
  artist: null,
};

async function main() {
  let db;

  try {
    db = knex({
      client: 'pg',
      connection: {
        connectionString: '***************************************************',
        ssl: false,
      },
      debug: true,
    });

    const user = await db('users')
      .where({ id: '01JBYJ92JR8PCF6E4J4VDM2559' })
      .first('*');

    console.log(user);

    // const release = await db('releases')
    //   .where({ id: '01JEXMH891XVK2824V3WPEWADF' })
    //   .first('*');
    //
    // console.log(release);
    //
    // db('releases').where({ id: release.id }).update({
    //   status: 'distributed',
    // });
    //
    // const release_artists = await db('release_artists')
    //   .where({ release_id: release.id })
    //   .join('artists', 'artists.id', 'release_artists.artist_id')
    //   .select('release_artists.*', 'artists.revelator_id');
    //
    // console.log(release_artists);
    //
    // for (const releaseArtist of release_artists) {
    //   if (releaseArtist.is_main) {
    //     await db('release_artists')
    //       .where({
    //         release_id: release.id,
    //         artist_id: releaseArtist.artist_id,
    //       })
    //       .update({
    //         role: 49,
    //       });
    //   }
    // }
    //
    // for (const contributor of data.contributors) {
    //   const artistInRelease = release_artists.find(
    //     (a) => a.revelator_id === String(contributor.artist.artistId),
    //   );
    //
    //   if (!artistInRelease) {
    //     let artist = await db('artists')
    //       .where({
    //         revelator_id: String(contributor.artist.artistId),
    //       })
    //       .first('*');
    //
    //     if (!artist) {
    //       const image = contributor.artist.image
    //         ? `https://cdn.revelator.com/images/${contributor.artist.image.fileId}/file.jpg`
    //         : null;
    //
    //       const spotify_id =
    //         contributor.artist.artistExternalIds.find(
    //           (id) => id.distributorStoreId === 9,
    //         )?.profileId ?? '0';
    //
    //       const apple_music_id =
    //         contributor.artist.artistExternalIds.find(
    //           (id) => id.distributorStoreId === 1,
    //         )?.profileId ?? '0';
    //
    //       artist = await db('artists')
    //         .insert({
    //           id: ulid(),
    //           name: contributor.artist.name,
    //           image,
    //           spotify_id,
    //           apple_music_id,
    //           user_id: '01JEXK63BN2YN456W93RE9JWKY',
    //           revelator_id: String(contributor.artist.artistId),
    //         })
    //         .returning('*')
    //         .then(([v]) => v);
    //     }
    //
    //     db('release_artists')
    //       .insert({
    //         release_id: release.id,
    //         artist_id: artist.id,
    //         role: contributor.roleId,
    //         is_primary: false,
    //         is_main: false,
    //       })
    //       .then(() => console.log('Inserted release artist'));
    //   }
    // }

    // const newSub = {
    //   id: ulid(),
    //   status: 'active',
    //   user_id: '01JE3QFV4ZP45WSTV53XEV77Y3',
    //   metadata: {
    //     sub_id: null,
    //     price_id: 'price_1RKH6SHtBEOb7Cf9iotnXglX',
    //     product_id: 'prod_SEkbin62LXqJqw',
    //     idempotency_key: null,
    //   },
    //   last_payment_date: new Date('2025-06-17'),
    //   next_payment_date: new Date('2025-07-18'),
    //   interval: 'monthly',
    //   failed_at: null,
    // };
    //
    // const d = await db('subscriptions')
    //   .insert(newSub)
    //   .returning('*')
    //   .then(([val]) => val);
    //
    // console.log(d);
    //
    // return;
    //
    // const privateSub = {
    //   id: ulid(),
    //   name: 'SOL Distro Direct',
    //   active: false,
    //   description: 'For users on previous Sol Distro agreements.',
    //   currency: 'USD',
    //   price_per_month: 1.0,
    //   price_per_annum: 10.0,
    //   metadata: {
    //     pricing: {
    //       annual: 'price_1RbQacHtBEOb7Cf9iOS81NWH',
    //       monthly: 'price_1RbQW1HtBEOb7Cf9uOWCdYP0',
    //     },
    //     cta_text: 'Get Started',
    //     product_id: 'prod_SFZCIPLAgPsmJJ',
    //     max_team_members: 3,
    //     distribution_terms: {
    //       formats: {},
    //       channels: {},
    //       services: {},
    //       countries: {},
    //       royalty_payout_rate: 80,
    //     },
    //     max_primary_artist_accounts: 5,
    //   },
    //   user_id: '01JBYJ92JR8PCF6E4J4VDM2559',
    //   community_id: '01JBYJDPH9HQZDXMV3171FC1XB',
    // };
    //
    // const subPlan = await db('subscription_plans')
    //   .insert(privateSub)
    //   .returning('*')
    //   .then(([val]) => val);
    //
    // console.log({ subPlan });
    // const r = await db
    //   .raw(
    //     `with cp as (select sp.id                                 as plan_id,
    //                         sp.name                               as plan_name,
    //                         sp.metadata -> 'product_id'           as product_id,
    //                         sp.metadata -> 'pricing' -> 'monthly' as monthly_pricing_id,
    //                         sp.metadata -> 'pricing' -> 'annual'  as annual_pricing_id
    //                  from subscription_plans sp
    //                  where community_id = :community_id),
    //           sd as (select s.id     as subscription_id,
    //                         s.status as subscription_status,
    //                         case
    //                             when (s.metadata -> 'price_id')::jsonb = cp.monthly_pricing_id
    //                                 then 'monthly'
    //                             when (s.metadata -> 'price_id')::jsonb = cp.annual_pricing_id
    //                                 then 'annual'
    //                             end  as interval
    //                  from cp
    //                           join subscriptions s on
    //                      ((s.metadata -> 'price_id')::jsonb = cp.monthly_pricing_id or
    //                       (s.metadata -> 'price_id')::jsonb = cp.annual_pricing_id))
    //      select count(sd.subscription_id)::int as total
    //      from sd`,
    //     { community_id: '01JBYJDPH9HQZDXMV3171FC1XB' },
    //   )
    //   .then(({ rows }) => rows);
    //
    // console.log(r);
    // //
    // const user = await db('users')
    //   .where({
    //     id: '01JRJR5WB3X7BADTBPH3HE39SB',
    //   })
    //   .first('*');
    //
    // console.log({ user });
    //
    // const subs = await db('subscriptions')
    //   .where({
    //     user_id: user.id,
    //   })
    //   .select('*');
    //
    // console.table(subs);

    //    const communitites = await db('communities').select('*');

    //   console.table(communitites);

    // const soldistro = await db('communities')
    //   .where({
    //     id: '01JBYJDPH9HQZDXMV3171FC1XB',
    //   })
    //   .first('*');

    // const user = await db('users')
    //   .where({
    //     community_id: soldistro.id,
    //     category: 'community_owner',
    //   })
    //   .first('*');
    //
    // const soldistroSub = await db('subscriptions')
    //   .where({
    //     user_id: user.id,
    //   })
    //   .first('*');
    //
    // console.log({ soldistroSub, soldistro });
    //
    // const users = await db('users')
    //   .where({ email: '<EMAIL>' })
    //   .select('*');
    //
    // console.table(users);

    // await db('users')
    //   .where({ id: '01JBYJ92JR8PCF6E4J4VDM2559' })
    //   .update({ payee_id: '534090' });

    return;

    // const release = await db('releases')
    //   .where({
    //     id: '01JTN1ZJ3CKV5MKHB8AHQD7W8W',
    //   })
    //   .first('*');

    //  const user = await db('users').where({ id: release.user_id }).first('*');

    //console.log({ release, user });
    // const users = await db('users')
    //   .where({ email: '<EMAIL>' })
    //   .select(
    //     'id',
    //     'email',
    //     'first_name',
    //     'last_name',
    //     'revelator_id',
    //     'enterprise_id',
    //     'enterprise_name',
    //     'payee_id',
    //   );
    //
    // console.table(users);
    //
    // await db('subscriptions')
    //   .where({ id: '01JWE8YQZMK4FEJ4BYXQD8J5AP' })
    //   .update({ status: 'active' });
    //
    // const subs = await db('subscriptions')
    //   .where({ user_id: '01JVZ53SA1D8A1MYB150PJMSEX' })
    //   .select('*');
    //
    // console.table(subs);
    // return;

    // const artist = await db('artists')
    //   .where({ id: '01JWDJAXZYRAKQCYET4WDJ7GYA' })
    //   .first('*');
    //
    // console.table([artist]);
    //
    // const r = await db('release_artists')
    //   .where({ artist_id: '01JWDJAXZYRAKQCYET4WDJ7GYA' })
    //   .select('*');
    //
    // console.table(r);
    //
    // const release = await db('releases')
    //   .where({ id: '01JWDJRTWQE4EQ95ZGBTE4K347' })
    //   .first('*');
    //
    // console.log({ release });
    //
    // const rt = await db('release_tracks')
    //   .where({
    //     release_id: '01JWDJRTWQE4EQ95ZGBTE4K347',
    //   })
    //   .first('*');
    //
    // console.table([rt]);
    //
    // const track = await db('tracks')
    //   .where({ id: '01JWDHPC6M3X1NXRXQWYFGBWCQ' })
    //   .first('*');
    //
    // console.log({ track });
    //
    // const user = await db('users')
    //   .where({ id: '01JTXQECN5EZ5Q3A5WNV95MH0A' })
    //   .first('*');
    //
    // console.log({ user });
    //
    // await db('release_artists')
    //   .where({
    //     release_id: '01JWDJRTWQE4EQ95ZGBTE4K347',
    //   })
    //   .delete();
    //
    // await db('release_tracks')
    //   .where({
    //     release_id: '01JWDJRTWQE4EQ95ZGBTE4K347',
    //   })
    //   .delete();
    //
    // await db('releases').where({ id: '01JWDJRTWQE4EQ95ZGBTE4K347' }).delete();

    // const labels = await db('labels')
    //   .where({ name: 'RYGOD Music (Pty) Ltd' })
    //   .select('*');
    //
    // console.table(labels);
    //
    // for (label of labels) {
    //   const releases = await db('releases')
    //     .where({ label_id: label.id })
    //     .select('*');
    //
    //   console.table(releases);
    // }
    //
    // return;

    // const release = await db('releases')
    //   .where({
    //     id: '01JSNWH05HFHN1Z20R1XPES30A',
    //   })
    //   .first('*');
    //
    // console.table([
    //   {
    //     id: release.id,
    //     title: release.title,
    //     user_id: release.user_id,
    //     revelator_release_id: release.revelator_release_id,
    //   },
    // ]);
    //
    // return;

    // await db('users').where({ id: '01JAQQHWWG9M2AYZRSZEZYT87Q' }).update({
    //   revelator_id: '<EMAIL>',
    //   enterprise_id: '570007',
    //   enterprise_name: 'Tallracks',
    //   payee_id: '623360',
    // });
    //
    // const u = await db('users')
    //   .where({ payee_id: 712486 })
    //   .orWhere({ email: '<EMAIL>' })
    //   .orWhere({ email: '<EMAIL>' })
    //   .orWhere({ id: '01JP5T3E8VQK5SE5H0NNQD3HKB' })
    //   .orWhere({ email: '01JAQQM9VQC94YAQGND4Y4XQWY' })
    //   .orWhere({ email: '<EMAIL>' })
    //   .select('*');
    //
    // console.table(
    //   u.map((d) => ({
    //     id: d.id,
    //     email: d.email,
    //     first_name: d.first_name,
    //     last_name: d.last_name,
    //     category: d.category,
    //     enterprise_id: d.enterprise_id,
    //     revelator_id: d.revelator_id,
    //     payee_id: d.payee_id,
    //   })),
    // );
    //
    // return;
    // const asyncOps = [];
    // const users = [];
    //
    // for (const solUser of solUsers) {
    //   asyncOps.push(
    //     new Promise(async (resolve, reject) => {
    //       try {
    //         const user = await db
    //           .raw(
    //             `select id,
    //                     email,
    //                     first_name,
    //                     last_name,
    //                     category,
    //                     revelator_id,
    //                     enterprise_id,
    //                     payee_id
    //              from users
    //              where lower(email) = :email
    //                and (category = 'creator' or category = 'owner') `,
    //             {
    //               email: solUser.email.toLowerCase(),
    //               category: 'creator',
    //             },
    //           )
    //           .then(({ rows }) => rows[0]);
    //
    //         if (user) {
    //           const updatedUser = await db('users')
    //             .where({ id: user.id })
    //             .update({
    //               revelator_id: solUser.revelatorId,
    //               enterprise_id: solUser.enterpriseId,
    //               payee_id: solUser.payeeId,
    //               enterprise_name: solUser.enterpriseName,
    //             })
    //             .returning('*')
    //             .then(([val]) => val);
    //
    //           users.push({
    //             ...user,
    //             newRevelatorId: updatedUser.revelator_id,
    //             newEnterpriseId: updatedUser.enterprise_id,
    //             newPayeeId: updatedUser.payee_id,
    //           });
    //         }
    //
    //         resolve(null);
    //       } catch (err) {
    //         reject(err);
    //       }
    //     }),
    //   );
    // }
    //
    // await Promise.all(asyncOps);
    //
    // console.table(users);

    // const users = await db
    //   .raw(
    //     `select u.id, u.email, u.first_name, u.last_name, u.category, u.revelator_id, u.enterprise_id
    //      from users u join  subscriptions s on u.id = s.user_id
    //      where u.revelator_id  = u.id
    //        and s.last_payment_date > '2025-05-02'::timestamptz`,
    //   )
    //   .then(({ rows }) => rows);

    //
    // const users = [];
    // const missingUsers = [];
    // const subs = [];
    //
    // for (const solUser of solUsers) {
    //   asyncOps.push(
    //     new Promise(async (resolve, reject) => {
    //       try {
    //         const user = await db
    //           .raw(
    //             `select id, email, first_name, last_name, category, revelator_id, enterprise_id
    //              from users
    //              where lower(email) = :email
    //                and category = :category`,
    //             {
    //               email: solUser.email.toLowerCase(),
    //               category: 'creator',
    //             },
    //           )
    //           .then(({ rows }) => rows[0]);
    //
    //         if (user) {
    //           users.push(user);
    //
    //           const userSub = await db('subscriptions')
    //             .where({ user_id: user.id })
    //             .first('*');
    //
    //           if (!userSub) {
    //             const sub = await db('subscriptions')
    //               .insert({
    //                 id: ulid(),
    //                 status: 'active',
    //                 user_id: user.id,
    //                 metadata: {
    //                   sub_id: 'sub_1RQnxvHtBEOb7Cf9jy2Xn57C',
    //                   product_id: 'prod_SFZCIPLAgPsmJJ',
    //                   price_id: 'price_1RLlx8HtBEOb7Cf9qzo2Cp4f',
    //                   idempotency_key: '5e285fe1-bf55-481e-a764-143b190e5c19',
    //                 },
    //                 last_payment_date: '2025-05-20T11:05:34.273Z',
    //                 next_payment_date: '2026-05-20T11:05:34.271Z',
    //                 created_at: '2025-05-20T11:05:34.272Z',
    //                 updated_at: '2025-05-20T11:05:34.272Z',
    //                 interval: 'annually',
    //                 failed_at: null,
    //               })
    //               .returning('*')
    //               .then((result) => result);
    //
    //             subs.push(sub);
    //           }
    //         } else {
    //           missingUsers.push(solUser);
    //         }
    //
    //         resolve(null);
    //       } catch (err) {
    //         reject(err);
    //       }
    //     }),
    //   );
    // }
    //
    // await Promise.all(asyncOps);
    //
    // console.log({
    //   tableUsers: solUsers.length,
    //   foundUsers: users.length,
    //   subs,
    //   missingUsers,
    // });
    //
    // console.table(users);
  } catch (err) {
    throw err;
  } finally {
    await db?.destroy();
  }
}

main().catch(console.error);
